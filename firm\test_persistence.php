<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Upload Persistence Test</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Handle test upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_upload'])) {
    $document_type = $_POST['document_type'];
    
    // Create a test file
    $upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $test_filename = $document_type . '_test_' . time() . '.txt';
    $test_filepath = $upload_dir . $test_filename;
    $test_content = "Test document for $document_type uploaded at " . date('Y-m-d H:i:s');
    
    if (file_put_contents($test_filepath, $test_content)) {
        $file_size = filesize($test_filepath);
        
        // Check if document already exists
        $check_sql = "SELECT id FROM firm_documents WHERE firm_id = ? AND document_type = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("is", $firm_id, $document_type);
        $check_stmt->execute();
        $existing = $check_stmt->get_result()->fetch_assoc();
        $check_stmt->close();
        
        if ($existing) {
            // Update existing document
            $update_sql = "UPDATE firm_documents SET 
                           file_name = ?, 
                           file_path = ?, 
                           file_size = ?, 
                           upload_date = NOW(),
                           status = 'uploaded'
                           WHERE firm_id = ? AND document_type = ?";
            
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("sssis", $test_filename, $test_filepath, $file_size, $firm_id, $document_type);
            
            if ($update_stmt->execute()) {
                echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px; color: #155724;'>";
                echo "✅ Test document updated successfully!";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; color: #721c24;'>";
                echo "❌ Failed to update document: " . $update_stmt->error;
                echo "</div>";
            }
            $update_stmt->close();
        } else {
            // Insert new document
            $insert_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status, upload_date) 
                           VALUES (?, ?, ?, ?, ?, 'uploaded', NOW())";
            
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("isssi", $firm_id, $document_type, $test_filename, $test_filepath, $file_size);
            
            if ($insert_stmt->execute()) {
                echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px; color: #155724;'>";
                echo "✅ Test document inserted successfully!";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; color: #721c24;'>";
                echo "❌ Failed to insert document: " . $insert_stmt->error;
                echo "</div>";
            }
            $insert_stmt->close();
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; color: #721c24;'>";
        echo "❌ Failed to create test file";
        echo "</div>";
    }
}

// Show current documents
echo "<h3>Current Documents in Database</h3>";
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY upload_date DESC";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Document Type</th><th>File Name</th><th>Status</th><th>Upload Date</th><th>File Exists</th><th>Actions</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        $file_color = $file_exists ? 'green' : 'red';
        $file_status = $file_exists ? '✅ Yes' : '❌ No';
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td style='color: $file_color;'>$file_status</td>";
        echo "<td>";
        echo "<a href='?delete_id=" . $doc['id'] . "' onclick='return confirm(\"Delete this document?\")' style='color: red;'>Delete</a>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>No documents found</p>";
}

// Handle delete
if (isset($_GET['delete_id'])) {
    $delete_id = (int)$_GET['delete_id'];
    
    // Get file path before deleting
    $get_file_sql = "SELECT file_path FROM firm_documents WHERE id = ? AND firm_id = ?";
    $get_file_stmt = $conn->prepare($get_file_sql);
    $get_file_stmt->bind_param("ii", $delete_id, $firm_id);
    $get_file_stmt->execute();
    $file_result = $get_file_stmt->get_result();
    
    if ($file_result->num_rows > 0) {
        $file_data = $file_result->fetch_assoc();
        $file_path = $file_data['file_path'];
        
        // Delete from database
        $delete_sql = "DELETE FROM firm_documents WHERE id = ? AND firm_id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("ii", $delete_id, $firm_id);
        
        if ($delete_stmt->execute()) {
            // Delete file
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            
            echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px; color: #856404;'>";
            echo "🗑️ Document deleted successfully";
            echo "</div>";
            echo "<script>setTimeout(() => window.location.href = 'test_persistence.php', 1000);</script>";
        }
    }
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px;">
    <h3>Test Upload</h3>
    <p>Use this form to test if documents persist after upload:</p>
    
    <form method="POST" style="margin: 10px 0;">
        <p>
            <label><strong>Document Type:</strong></label><br>
            <select name="document_type" required style="padding: 5px; margin: 5px 0;">
                <option value="">Select document type</option>
                <option value="memart">MEMART</option>
                <option value="cac_status">CAC Status Report</option>
                <option value="tax_clearance">Tax Clearance Certificate</option>
                <option value="utility_bill">Utility Bill</option>
                <option value="incorporation_cert">Certificate of Incorporation</option>
            </select>
        </p>
        <p>
            <button type="submit" name="test_upload" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Create Test Document</button>
        </p>
    </form>
</div>

<div style="margin: 20px 0; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px;">
    <h3 style="color: #0c5460;">📋 Persistence Test Instructions</h3>
    <ol style="color: #0c5460;">
        <li><strong>Create Test Document</strong> - Use the form above to create a test document</li>
        <li><strong>Check Database</strong> - Verify the document appears in the table above</li>
        <li><strong>Refresh Page</strong> - Refresh this page to see if document persists</li>
        <li><strong>Check Dashboard</strong> - Go to dashboard to see if document shows as uploaded</li>
        <li><strong>Test Again</strong> - Refresh dashboard to confirm persistence</li>
    </ol>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="debug_persistence.php" style="background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Debug Info</a>
    </p>
</div>
