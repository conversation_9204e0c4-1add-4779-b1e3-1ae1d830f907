// Process rejection
if ($action === 'reject') {
    $rejection_category = $_POST['rejection_reason_category'] ?? '';
    $rejection_details = $_POST['rejection_details'] ?? '';
    $next_steps = $_POST['next_steps'] ?? '';
    
    // Format the complete rejection reason
    $formatted_reason = $rejection_category;
    if (!empty($rejection_details)) {
        $formatted_reason .= ": " . $rejection_details;
    }
    
    // Add next steps if provided, otherwise use defaults
    if (empty($next_steps)) {
        // Default next steps based on category
        switch ($rejection_category) {
            case 'Incomplete Documentation':
                $next_steps = "Please submit all required documents including: valid ID, proof of qualifications, and professional references.";
                break;
            case 'Qualification Issues':
                $next_steps = "Please ensure you meet the minimum qualification requirements and provide certified copies of all relevant certificates.";
                break;
            case 'Experience Requirements':
                $next_steps = "Additional professional experience is required. Please reapply when you have gained the necessary experience in tax practice.";
                break;
            case 'Verification Failed':
                $next_steps = "We were unable to verify the information provided. Please ensure all details are accurate and verifiable.";
                break;
            default:
                $next_steps = "Please review the rejection reason and make the necessary corrections before submitting a new application.";
        }
    }
    
    // Store both the reason and next steps
    $complete_rejection_info = [
        'reason' => $formatted_reason,
        'next_steps' => $next_steps
    ];
    
    // Convert to JSON for storage
    $rejection_json = json_encode($complete_rejection_info);
    
    // Update application status
    $result = update_application_status($application_id, 'Rejected', $rejection_json, $admin_id);
    
    if ($result) {
        $_SESSION['alert'] = [
            'type' => 'success',
            'message' => 'Application has been rejected and applicant notified.'
        ];
    } else {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Failed to update application status.'
        ];
    }
}

// Process additional information request
if ($action === 'request_info') {
    $sections_to_update = isset($_POST['sections_to_update']) ? $_POST['sections_to_update'] : [];
    $info_request_details = $_POST['info_request_details'] ?? '';
    $info_request_deadline = $_POST['info_request_deadline'] ?? '';
    
    // Format the sections as a comma-separated list
    $sections_list = implode(',', $sections_to_update);
    
    // Create a structured info request
    $info_request = [
        'sections' => $sections_to_update,
        'details' => $info_request_details,
        'deadline' => $info_request_deadline
    ];
    
    // Convert to JSON for storage
    $info_request_json = json_encode($info_request);
    
    // Update application status
    $result = update_application_status($application_id, 'Info_Required', $info_request_json, $admin_id);
    
    if ($result) {
        $_SESSION['alert'] = [
            'type' => 'success',
            'message' => 'Additional information request has been sent to the applicant.'
        ];
    } else {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Failed to update application status.'
        ];
    }
    
    header("Location: review_application.php?id=$application_id");
    exit;
}
