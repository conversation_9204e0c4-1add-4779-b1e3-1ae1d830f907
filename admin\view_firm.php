<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: manage_firms.php");
    exit();
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

$firm_id = (int)$_GET['id'];
$page_title = "View Firm Details";

// Get firm details
$sql = "SELECT * FROM tax_firms WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $firm_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: manage_firms.php");
    exit();
}

$firm = $result->fetch_assoc();

// Process status update if submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    $action = $_POST['action'];
    $reason = $_POST['reason'] ?? '';
    
    $new_status = '';
    switch ($action) {
        case 'approve':
            $new_status = 'Active';
            break;
        case 'reject':
            $new_status = 'Rejected';
            break;
        case 'suspend':
            $new_status = 'Suspended';
            break;
        case 'reactivate':
            $new_status = 'Active';
            break;
    }
    
    if (!empty($new_status)) {
        $update_sql = "UPDATE tax_firms SET registration_status = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("si", $new_status, $firm_id);
        
        if ($update_stmt->execute()) {
            // Log the action
            if (function_exists('log_admin_action')) {
                log_admin_action($_SESSION['admin_id'], $action . '_firm', "Firm ID: {$firm_id}, Reason: {$reason}");
            }
            
            $_SESSION['alert'] = [
                'type' => 'success',
                'message' => "Firm status updated to {$new_status} successfully."
            ];
            
            header("Location: view_firm.php?id=$firm_id");
            exit();
        } else {
            $_SESSION['alert'] = [
                'type' => 'danger',
                'message' => 'Failed to update firm status.'
            ];
        }
    }
}

// Get associated practitioners count
$practitioners_sql = "SELECT COUNT(*) as count FROM practitioner_firm_associations pfa
                     JOIN tax_practitioners tp ON pfa.practitioner_id = tp.id
                     WHERE pfa.firm_id = ? AND pfa.status = 'active'";
$practitioners_stmt = $conn->prepare($practitioners_sql);
$practitioners_stmt->bind_param("i", $firm_id);
$practitioners_stmt->execute();
$practitioners_count = $practitioners_stmt->get_result()->fetch_assoc()['count'] ?? 0;

// Get comprehensive firm statistics for admin monitoring
$stats = [];

// Document statistics
$doc_stats = $conn->query("SELECT
    COUNT(*) as total_documents,
    COUNT(CASE WHEN status = 'uploaded' THEN 1 END) as uploaded_docs,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_docs,
    MAX(upload_date) as last_upload
    FROM firm_documents WHERE firm_id = $firm_id");
$stats['documents'] = $doc_stats ? $doc_stats->fetch_assoc() : ['total_documents' => 0, 'uploaded_docs' => 0, 'pending_docs' => 0, 'last_upload' => null];

// Shareholder statistics
$sh_stats = $conn->query("SELECT
    COUNT(*) as total_shareholders,
    COUNT(CASE WHEN status = 'verified' THEN 1 END) as verified_shareholders,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_shareholders,
    SUM(share_percentage) as total_share_percentage,
    MAX(created_at) as last_added
    FROM firm_shareholders WHERE firm_id = $firm_id");
$stats['shareholders'] = $sh_stats ? $sh_stats->fetch_assoc() : ['total_shareholders' => 0, 'verified_shareholders' => 0, 'pending_shareholders' => 0, 'total_share_percentage' => 0, 'last_added' => null];

// Director statistics
$dir_stats = $conn->query("SELECT
    COUNT(*) as total_directors,
    COUNT(CASE WHEN status = 'verified' THEN 1 END) as verified_directors,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_directors,
    MAX(created_at) as last_added
    FROM firm_directors WHERE firm_id = $firm_id");
$stats['directors'] = $dir_stats ? $dir_stats->fetch_assoc() : ['total_directors' => 0, 'verified_directors' => 0, 'pending_directors' => 0, 'last_added' => null];

// Secretary statistics
$sec_stats = $conn->query("SELECT
    COUNT(*) as total_secretaries,
    COUNT(CASE WHEN status = 'verified' THEN 1 END) as verified_secretaries,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_secretaries,
    MAX(created_at) as last_added
    FROM firm_secretaries WHERE firm_id = $firm_id");
$stats['secretaries'] = $sec_stats ? $sec_stats->fetch_assoc() : ['total_secretaries' => 0, 'verified_secretaries' => 0, 'pending_secretaries' => 0, 'last_added' => null];

// Tax clearance statistics
$tax_stats = $conn->query("SELECT
    COUNT(*) as total_tax_records,
    COUNT(CASE WHEN status = 'ISSUED' THEN 1 END) as issued_records,
    COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_records,
    MAX(created_at) as last_added
    FROM firm_tax_clearance WHERE firm_id = $firm_id");
$stats['tax_clearance'] = $tax_stats ? $tax_stats->fetch_assoc() : ['total_tax_records' => 0, 'issued_records' => 0, 'expired_records' => 0, 'last_added' => null];

include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Firm Details</h1>
                    <p class="text-muted">View and manage firm information</p>
                </div>
                <div>
                    <a href="manage_firms.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Firms
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (isset($_SESSION['alert'])): ?>
    <div class="alert alert-<?php echo $_SESSION['alert']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['alert']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['alert']); endif; ?>

    <div class="row">
        <!-- Firm Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Firm Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Firm Name</label>
                                <p class="form-control-plaintext"><?php echo htmlspecialchars($firm['name']); ?></p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email Address</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-envelope me-1"></i>
                                    <a href="mailto:<?php echo htmlspecialchars($firm['email']); ?>">
                                        <?php echo htmlspecialchars($firm['email']); ?>
                                    </a>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Phone Number</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($firm['phone']); ?>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Registration Number</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info fs-6"><?php echo htmlspecialchars($firm['registration_number']); ?></span>
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tax ID</label>
                                <p class="form-control-plaintext"><?php echo htmlspecialchars($firm['tax_id'] ?? 'Not provided'); ?></p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Business Type</label>
                                <p class="form-control-plaintext"><?php echo htmlspecialchars($firm['business_type'] ?? 'Not specified'); ?></p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Registration Status</label>
                                <p class="form-control-plaintext">
                                    <?php
                                    $status_class = 'secondary';
                                    switch ($firm['registration_status']) {
                                        case 'Active':
                                            $status_class = 'success';
                                            break;
                                        case 'Pending':
                                            $status_class = 'warning';
                                            break;
                                        case 'Suspended':
                                            $status_class = 'info';
                                            break;
                                        case 'Rejected':
                                            $status_class = 'danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?php echo $status_class; ?> fs-6">
                                        <?php echo htmlspecialchars($firm['registration_status']); ?>
                                    </span>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Registration Date</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo date('F d, Y \a\t g:i A', strtotime($firm['registration_date'])); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Address</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo nl2br(htmlspecialchars($firm['address'] ?? 'Not provided')); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Associated Practitioners -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Associated Practitioners
                        <span class="badge bg-light text-dark ms-2"><?php echo $practitioners_count; ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($practitioners_count > 0): ?>
                        <p>This firm has <?php echo $practitioners_count; ?> associated practitioner(s).</p>
                        <a href="view_firm_practitioners.php?firm_id=<?php echo $firm_id; ?>" class="btn btn-outline-info">
                            <i class="fas fa-eye me-1"></i>View Associated Practitioners
                        </a>
                    <?php else: ?>
                        <p class="text-muted mb-0">No practitioners are currently associated with this firm.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Actions Panel -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($firm['registration_status'] == 'Pending'): ?>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#actionModal" data-action="approve">
                                <i class="fas fa-check me-1"></i>Approve Firm
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#actionModal" data-action="reject">
                                <i class="fas fa-times me-1"></i>Reject Firm
                            </button>
                        </div>
                    <?php elseif ($firm['registration_status'] == 'Active'): ?>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#actionModal" data-action="suspend">
                                <i class="fas fa-pause me-1"></i>Suspend Firm
                            </button>
                        </div>
                    <?php elseif ($firm['registration_status'] == 'Suspended'): ?>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#actionModal" data-action="reactivate">
                                <i class="fas fa-play me-1"></i>Reactivate Firm
                            </button>
                        </div>
                    <?php endif; ?>
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        <a href="edit_firm.php?id=<?php echo $firm_id; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-1"></i>Edit Details
                        </a>
                        <a href="manage_firms.php?search=<?php echo urlencode($firm['email']); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-search me-1"></i>Find Similar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Quick Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-0"><?php echo $practitioners_count; ?></h4>
                                <small class="text-muted">Practitioners</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info mb-0">
                                <?php echo date('Y', strtotime($firm['registration_date'])); ?>
                            </h4>
                            <small class="text-muted">Registered</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="actionModalTitle">Confirm Action</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" id="actionInput">
                    <p id="actionMessage"></p>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason (Optional)</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="Enter reason for this action..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="actionButton">Confirm</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Comprehensive Firm Activity Monitoring Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Firm Activity Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Documents Statistics -->
                        <div class="col-md-2 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-upload fa-2x text-primary mb-2"></i>
                                    <h4><?php echo $stats['documents']['total_documents']; ?></h4>
                                    <p class="mb-1">Documents</p>
                                    <small class="text-muted">
                                        <?php echo $stats['documents']['uploaded_docs']; ?> uploaded,
                                        <?php echo $stats['documents']['pending_docs']; ?> pending
                                    </small>
                                    <?php if ($stats['documents']['last_upload']): ?>
                                        <br><small class="text-muted">Last: <?php echo date('M j', strtotime($stats['documents']['last_upload'])); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Shareholders Statistics -->
                        <div class="col-md-2 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                                    <h4><?php echo $stats['shareholders']['total_shareholders']; ?></h4>
                                    <p class="mb-1">Shareholders</p>
                                    <small class="text-muted">
                                        <?php echo $stats['shareholders']['verified_shareholders']; ?> verified,
                                        <?php echo $stats['shareholders']['pending_shareholders']; ?> pending
                                    </small>
                                    <br><small class="text-success">
                                        <?php echo number_format($stats['shareholders']['total_share_percentage'], 2); ?>% total shares
                                    </small>
                                    <?php if ($stats['shareholders']['last_added']): ?>
                                        <br><small class="text-muted">Last: <?php echo date('M j', strtotime($stats['shareholders']['last_added'])); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Directors Statistics -->
                        <div class="col-md-2 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-tie fa-2x text-info mb-2"></i>
                                    <h4><?php echo $stats['directors']['total_directors']; ?></h4>
                                    <p class="mb-1">Directors</p>
                                    <small class="text-muted">
                                        <?php echo $stats['directors']['verified_directors']; ?> verified,
                                        <?php echo $stats['directors']['pending_directors']; ?> pending
                                    </small>
                                    <?php if ($stats['directors']['last_added']): ?>
                                        <br><small class="text-muted">Last: <?php echo date('M j', strtotime($stats['directors']['last_added'])); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Secretaries Statistics -->
                        <div class="col-md-2 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-graduate fa-2x text-warning mb-2"></i>
                                    <h4><?php echo $stats['secretaries']['total_secretaries']; ?></h4>
                                    <p class="mb-1">Secretaries</p>
                                    <small class="text-muted">
                                        <?php echo $stats['secretaries']['verified_secretaries']; ?> verified,
                                        <?php echo $stats['secretaries']['pending_secretaries']; ?> pending
                                    </small>
                                    <?php if ($stats['secretaries']['last_added']): ?>
                                        <br><small class="text-muted">Last: <?php echo date('M j', strtotime($stats['secretaries']['last_added'])); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Tax Clearance Statistics -->
                        <div class="col-md-2 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-certificate fa-2x text-danger mb-2"></i>
                                    <h4><?php echo $stats['tax_clearance']['total_tax_records']; ?></h4>
                                    <p class="mb-1">Tax Records</p>
                                    <small class="text-muted">
                                        <?php echo $stats['tax_clearance']['issued_records']; ?> issued,
                                        <?php echo $stats['tax_clearance']['expired_records']; ?> expired
                                    </small>
                                    <?php if ($stats['tax_clearance']['last_added']): ?>
                                        <br><small class="text-muted">Last: <?php echo date('M j', strtotime($stats['tax_clearance']['last_added'])); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Practitioners Statistics -->
                        <div class="col-md-2 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-check fa-2x text-secondary mb-2"></i>
                                    <h4><?php echo $practitioners_count; ?></h4>
                                    <p class="mb-1">Practitioners</p>
                                    <small class="text-muted">Associated</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-item {
    border-left: 2px solid #e9ecef;
    padding-left: 1rem;
    position: relative;
}

.timeline-marker {
    position: absolute;
    left: -0.6rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
}
</style>

<script>
// Handle action modal
document.addEventListener('DOMContentLoaded', function() {
    const actionModal = document.getElementById('actionModal');
    const actionInput = document.getElementById('actionInput');
    const actionModalTitle = document.getElementById('actionModalTitle');
    const actionMessage = document.getElementById('actionMessage');
    const actionButton = document.getElementById('actionButton');
    
    actionModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const action = button.getAttribute('data-action');
        
        actionInput.value = action;
        
        const firmName = '<?php echo addslashes($firm['name']); ?>';
        
        switch(action) {
            case 'approve':
                actionModalTitle.textContent = 'Approve Firm';
                actionMessage.textContent = `Are you sure you want to approve "${firmName}"?`;
                actionButton.textContent = 'Approve';
                actionButton.className = 'btn btn-success';
                break;
            case 'reject':
                actionModalTitle.textContent = 'Reject Firm';
                actionMessage.textContent = `Are you sure you want to reject "${firmName}"?`;
                actionButton.textContent = 'Reject';
                actionButton.className = 'btn btn-danger';
                break;
            case 'suspend':
                actionModalTitle.textContent = 'Suspend Firm';
                actionMessage.textContent = `Are you sure you want to suspend "${firmName}"?`;
                actionButton.textContent = 'Suspend';
                actionButton.className = 'btn btn-warning';
                break;
            case 'reactivate':
                actionModalTitle.textContent = 'Reactivate Firm';
                actionMessage.textContent = `Are you sure you want to reactivate "${firmName}"?`;
                actionButton.textContent = 'Reactivate';
                actionButton.className = 'btn btn-success';
                break;
        }
    });
});

// Auto-hide alerts
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('show')) {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }
    });
}, 5000);
</script>

<?php include '../includes/admin_footer.php'; ?>
