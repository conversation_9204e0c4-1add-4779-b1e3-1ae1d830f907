<?php
session_start();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Logout Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .btn { padding: 10px 15px; margin: 5px; text-decoration: none; border-radius: 4px; display: inline-block; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Simple Logout Test</h1>
    
    <?php if (isset($_SESSION['firm_id'])): ?>
        <div class="status success">
            <strong>Currently logged in as:</strong><br>
            Firm ID: <?php echo $_SESSION['firm_id']; ?><br>
            Firm Name: <?php echo $_SESSION['firm_name'] ?? 'Not set'; ?>
        </div>
        
        <p>Click the button below to test logout (no JavaScript confirmation):</p>
        <a href="logout.php" class="btn btn-danger">Direct Logout Test</a>
        
        <p>Or test with JavaScript confirmation:</p>
        <button onclick="testLogout()" class="btn btn-danger">Logout with Confirmation</button>
        
    <?php else: ?>
        <div class="status error">
            <strong>Not logged in</strong>
        </div>
        <a href="quick_login_test.php?test_login=1" class="btn btn-success">Quick Login for Testing</a>
    <?php endif; ?>
    
    <script>
        function testLogout() {
            console.log('Testing logout function...');
            if (confirm('Are you sure you want to logout?')) {
                console.log('User confirmed, redirecting to logout.php');
                window.location.href = 'logout.php';
            } else {
                console.log('User cancelled logout');
            }
        }
    </script>
    
    <hr>
    <p><strong>Debug Info:</strong></p>
    <p>Session ID: <?php echo session_id(); ?></p>
    <p>Current URL: <?php echo $_SERVER['REQUEST_URI']; ?></p>
    <p><a href="<?php echo $_SERVER['PHP_SELF']; ?>">Refresh Page</a></p>
</body>
</html>
