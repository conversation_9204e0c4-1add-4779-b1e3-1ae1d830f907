<?php
session_start();
header('Content-Type: application/json');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo json_encode([
    'debug' => true,
    'session_firm_id' => $_SESSION['firm_id'] ?? 'NOT SET',
    'post_data' => $_POST,
    'files_data' => $_FILES,
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'NOT SET',
    'session_data' => $_SESSION
], JSON_PRETTY_PRINT);
?>
