<?php
session_start();

// Check if logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit;
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: dashboard.php");
    exit;
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

$consultant_id = (int)$_GET['id'];
$page_title = "View Practitioner Details";

// Get practitioner details
$sql = "SELECT * FROM tax_practitioners WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $consultant_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: dashboard.php");
    exit;
}

$consultant = $result->fetch_assoc();

// Process status update if submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action == 'approve') {
        $update_sql = "UPDATE tax_practitioners SET registration_status = 'Active' WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("i", $consultant_id);
        $update_stmt->execute();
        header("Location: view_consultant.php?id=$consultant_id&status=approved");
        exit;
    } elseif ($action == 'reject') {
        $update_sql = "UPDATE tax_practitioners SET registration_status = 'Rejected' WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("i", $consultant_id);
        $update_stmt->execute();
        header("Location: view_consultant.php?id=$consultant_id&status=rejected");
        exit;
    }
}

include '../includes/header.php';
?>

<div class="admin-actions">
    <a href="dashboard.php" class="btn">Back to Dashboard</a>
</div>

<?php if (isset($_GET['status'])): ?>
    <div class="success">
        <?php 
        if ($_GET['status'] == 'approved') {
            echo "Consultant has been approved successfully.";
        } elseif ($_GET['status'] == 'rejected') {
            echo "Consultant has been rejected.";
        }
        ?>
    </div>
<?php endif; ?>

<h2>Consultant Details</h2>

<div class="consultant-details">
    <div class="section">
        <h3>Personal Information</h3>
        <table>
            <tr>
                <th>NIN:</th>
                <td><?php echo htmlspecialchars($consultant['nin']); ?></td>
            </tr>
            <tr>
                <th>Full Name:</th>
                <td><?php echo htmlspecialchars($consultant['full_name']); ?></td>
            </tr>
            <tr>
                <th>Email:</th>
                <td><?php echo htmlspecialchars($consultant['email']); ?></td>
            </tr>
            <tr>
                <th>Phone:</th>
                <td><?php echo htmlspecialchars($consultant['phone']); ?></td>
            </tr>
            <tr>
                <th>Address:</th>
                <td><?php echo htmlspecialchars($consultant['address']); ?></td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h3>Qualifications</h3>
        <table>
            <tr>
                <th>Highest Qualification:</th>
                <td><?php echo htmlspecialchars($consultant['qualification']); ?></td>
            </tr>
            <tr>
                <th>Certification Number:</th>
                <td><?php echo htmlspecialchars($consultant['certification_number'] ?: 'N/A'); ?></td>
            </tr>
            <tr>
                <th>Years of Experience:</th>
                <td><?php echo htmlspecialchars($consultant['years_experience']); ?></td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h3>Employment Information</h3>
        <table>
            <tr>
                <th>Employer:</th>
                <td><?php echo htmlspecialchars($consultant['employer']); ?></td>
            </tr>
            <tr>
                <th>Position:</th>
                <td><?php echo htmlspecialchars($consultant['position']); ?></td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h3>Registration Status</h3>
        <p class="status-badge <?php echo strtolower($consultant['registration_status']); ?>">
            <?php echo htmlspecialchars($consultant['registration_status']); ?>
        </p>
        <p>Registration Date: <?php echo htmlspecialchars($consultant['registration_date']); ?></p>

        <?php if ($consultant['registration_status'] == 'Pending'): ?>
            <div class="status-actions">
                <form method="post">
                    <button type="submit" name="action" value="approve" class="btn btn-success">Approve Registration</button>
                    <button type="submit" name="action" value="reject" class="btn btn-danger">Reject Registration</button>
                </form>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php 
include '../includes/footer.php';
$conn->close();
?>