<?php
// Database setup for company structure tables (shareholders, directors, company secretaries)
require_once '../includes/config.php';

echo "<h2>Setting up Company Structure Tables</h2>";

// Create shareholders table
$create_shareholders_table = "
CREATE TABLE IF NOT EXISTS firm_shareholders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    firm_id INT NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    number_of_shares INT NOT NULL DEFAULT 0,
    share_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    share_class VARCHAR(50) DEFAULT 'Ordinary',
    appointment_date DATE,
    status ENUM('verified', 'pending', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (firm_id) REFERENCES tax_firms(id) ON DELETE CASCADE,
    INDEX idx_firm_id (firm_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

if ($conn->query($create_shareholders_table)) {
    echo "<p style='color: green;'>✅ firm_shareholders table created successfully!</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create firm_shareholders table: " . $conn->error . "</p>";
}

// Create directors table
$create_directors_table = "
CREATE TABLE IF NOT EXISTS firm_directors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    firm_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    position VARCHAR(100) NOT NULL,
    appointment_date DATE,
    resignation_date DATE NULL,
    nationality VARCHAR(100) DEFAULT 'Nigerian',
    identification_type ENUM('NIN', 'International Passport', 'Driver License') DEFAULT 'NIN',
    identification_number VARCHAR(50),
    status ENUM('verified', 'pending', 'rejected', 'resigned') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (firm_id) REFERENCES tax_firms(id) ON DELETE CASCADE,
    INDEX idx_firm_id (firm_id),
    INDEX idx_status (status),
    INDEX idx_position (position)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

if ($conn->query($create_directors_table)) {
    echo "<p style='color: green;'>✅ firm_directors table created successfully!</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create firm_directors table: " . $conn->error . "</p>";
}

// Create company secretaries table
$create_secretaries_table = "
CREATE TABLE IF NOT EXISTS firm_secretaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    firm_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    qualification VARCHAR(255),
    professional_body VARCHAR(100),
    membership_number VARCHAR(50),
    appointment_date DATE,
    resignation_date DATE NULL,
    status ENUM('verified', 'pending', 'rejected', 'resigned') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (firm_id) REFERENCES tax_firms(id) ON DELETE CASCADE,
    INDEX idx_firm_id (firm_id),
    INDEX idx_status (status),
    INDEX idx_professional_body (professional_body)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

if ($conn->query($create_secretaries_table)) {
    echo "<p style='color: green;'>✅ firm_secretaries table created successfully!</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create firm_secretaries table: " . $conn->error . "</p>";
}

// Insert sample data for testing
echo "<h3>Inserting Sample Data</h3>";

// Get a firm ID for testing (assuming there's at least one firm)
$firm_result = $conn->query("SELECT id FROM tax_firms LIMIT 1");
if ($firm_result && $firm_result->num_rows > 0) {
    $firm = $firm_result->fetch_assoc();
    $firm_id = $firm['id'];
    
    // Sample shareholders
    $sample_shareholders = [
        ['John Smith', '<EMAIL>', '+***********-5678', '123 Lagos Street, Lagos', 10000, 25.50, 'Ordinary', '2024-01-15'],
        ['Jane Doe', '<EMAIL>', '+***********-6789', '456 Abuja Avenue, Abuja', 15000, 38.20, 'Ordinary', '2024-02-10'],
        ['ABC Corporation', '<EMAIL>', '+***********-7890', '789 Port Harcourt Road, PH', 8500, 21.70, 'Preference', '2024-03-05']
    ];
    
    foreach ($sample_shareholders as $shareholder) {
        $stmt = $conn->prepare("INSERT INTO firm_shareholders (firm_id, name, email, phone, address, number_of_shares, share_percentage, share_class, appointment_date, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'verified')");
        $stmt->bind_param("issssisss", $firm_id, $shareholder[0], $shareholder[1], $shareholder[2], $shareholder[3], $shareholder[4], $shareholder[5], $shareholder[6], $shareholder[7]);
        $stmt->execute();
    }
    echo "<p style='color: green;'>✅ Sample shareholders data inserted!</p>";
    
    // Sample directors
    $sample_directors = [
        ['John Smith', '<EMAIL>', '+***********-5678', '123 Lagos Street, Lagos', 'Managing Director', '2024-01-15'],
        ['Mary Johnson', '<EMAIL>', '+234-************', '321 Kano Street, Kano', 'Executive Director', '2024-01-20'],
        ['David Wilson', '<EMAIL>', '+234-************', '654 Ibadan Road, Ibadan', 'Non-Executive Director', '2024-02-01']
    ];
    
    foreach ($sample_directors as $director) {
        $stmt = $conn->prepare("INSERT INTO firm_directors (firm_id, name, email, phone, address, position, appointment_date, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'verified')");
        $stmt->bind_param("issssss", $firm_id, $director[0], $director[1], $director[2], $director[3], $director[4], $director[5]);
        $stmt->execute();
    }
    echo "<p style='color: green;'>✅ Sample directors data inserted!</p>";
    
    // Sample company secretaries
    $sample_secretaries = [
        ['Sarah Williams', '<EMAIL>', '+***********-0123', '987 Enugu Street, Enugu', 'ICSAN Certified', 'ICSAN', 'ICSAN/2023/001', '2024-01-10'],
        ['Michael Brown', '<EMAIL>', '+***********-1234', '147 Kaduna Avenue, Kaduna', 'Company Secretary (Professional)', 'ICSAN', 'ICSAN/2023/002', '2024-02-15']
    ];
    
    foreach ($sample_secretaries as $secretary) {
        $stmt = $conn->prepare("INSERT INTO firm_secretaries (firm_id, name, email, phone, address, qualification, professional_body, membership_number, appointment_date, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'verified')");
        $stmt->bind_param("issssssss", $firm_id, $secretary[0], $secretary[1], $secretary[2], $secretary[3], $secretary[4], $secretary[5], $secretary[6], $secretary[7]);
        $stmt->execute();
    }
    echo "<p style='color: green;'>✅ Sample company secretaries data inserted!</p>";
    
} else {
    echo "<p style='color: orange;'>⚠️ No firms found in database. Sample data not inserted.</p>";
}

echo "<h3>Setup Complete!</h3>";
echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>";

$conn->close();
?>
