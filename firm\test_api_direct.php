<?php
session_start();
require_once '../includes/config.php';

// Set up a test session if not already logged in
if (!isset($_SESSION['firm_id'])) {
    // Get the first firm for testing
    $firm_result = $conn->query("SELECT * FROM tax_firms LIMIT 1");
    if ($firm_result && $firm_result->num_rows > 0) {
        $firm = $firm_result->fetch_assoc();
        $_SESSION['firm_id'] = $firm['id'];
        $_SESSION['firm_name'] = $firm['name'];
        $_SESSION['firm_email'] = $firm['email'];
    } else {
        die("No firms found in database. Please create a firm first.");
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Shareholders API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #059669; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #047857; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d1fae5; border: 1px solid #10b981; color: #065f46; }
        .error { background: #fee2e2; border: 1px solid #ef4444; color: #991b1b; }
    </style>
</head>
<body>
    <h1>Test Shareholders API</h1>
    <p>Current Session - Firm ID: <?php echo $_SESSION['firm_id']; ?>, Name: <?php echo $_SESSION['firm_name']; ?></p>
    
    <form id="shareholderForm">
        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" required value="John Doe">
        </div>
        
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="phone">Phone</label>
            <input type="tel" id="phone" name="phone" value="+234-************">
        </div>
        
        <div class="form-group">
            <label for="address">Address</label>
            <textarea id="address" name="address">123 Test Street, Lagos</textarea>
        </div>
        
        <div class="form-group">
            <label for="number_of_shares">Number of Shares *</label>
            <input type="number" id="number_of_shares" name="number_of_shares" required value="1000">
        </div>
        
        <div class="form-group">
            <label for="share_percentage">Share Percentage *</label>
            <input type="number" id="share_percentage" name="share_percentage" step="0.01" required value="25.50">
        </div>
        
        <div class="form-group">
            <label for="share_class">Share Class</label>
            <select id="share_class" name="share_class">
                <option value="Ordinary">Ordinary</option>
                <option value="Preference">Preference</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="appointment_date">Appointment Date</label>
            <input type="date" id="appointment_date" name="appointment_date" value="<?php echo date('Y-m-d'); ?>">
        </div>
        
        <button type="submit">Add Shareholder</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('shareholderForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            // Convert numeric fields
            data.number_of_shares = parseInt(data.number_of_shares);
            data.share_percentage = parseFloat(data.share_percentage);
            data.status = 'pending';
            
            console.log('Sending data:', data);
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Sending request...</p>';
            
            fetch('api/shareholders.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                
                try {
                    const result = JSON.parse(text);
                    console.log('Parsed response:', result);
                    
                    if (result.success) {
                        resultDiv.innerHTML = '<div class="result success"><strong>Success!</strong> ' + result.message + ' (ID: ' + result.id + ')</div>';
                        document.getElementById('shareholderForm').reset();
                    } else {
                        resultDiv.innerHTML = '<div class="result error"><strong>Error:</strong> ' + result.error + '</div>';
                    }
                } catch (e) {
                    console.error('JSON parse error:', e);
                    resultDiv.innerHTML = '<div class="result error"><strong>Parse Error:</strong> ' + text + '</div>';
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = '<div class="result error"><strong>Network Error:</strong> ' + error.message + '</div>';
            });
        });
    </script>
    
    <p style="margin-top: 30px;">
        <a href="dashboard.php">← Back to Dashboard</a>
    </p>
</body>
</html>
