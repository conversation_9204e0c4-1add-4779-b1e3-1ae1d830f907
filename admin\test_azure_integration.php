<?php
/**
 * Azure AD Integration Test
 * 
 * This script tests the Azure AD integration to ensure everything is working correctly.
 */

session_start();
require_once '../includes/config.php';
require_once '../includes/azure_ad_integration.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$test_results = [];

// Test 1: Check if Azure AD libraries are available
$test_results['libraries'] = [
    'name' => 'Azure AD Libraries Check',
    'status' => check_azure_libraries() ? 'PASS' : 'FAIL',
    'message' => check_azure_libraries() ? 'Basic cURL implementation is available' : 'Libraries not available'
];

// Test 2: Check Azure AD configuration
$azure_config = initialize_azure_ad();
$test_results['config'] = [
    'name' => 'Azure AD Configuration',
    'status' => $azure_config ? 'PASS' : 'FAIL',
    'message' => $azure_config ? 'Configuration loaded successfully' : 'Configuration missing or invalid',
    'details' => $azure_config ? array_keys($azure_config) : []
];

// Test 3: Check if authorization URL can be generated
$auth_url = get_azure_auth_url();
$test_results['auth_url'] = [
    'name' => 'Authorization URL Generation',
    'status' => $auth_url ? 'PASS' : 'FAIL',
    'message' => $auth_url ? 'Authorization URL generated successfully' : 'Failed to generate authorization URL',
    'url' => $auth_url ?: 'N/A'
];

// Test 4: Check database table
$table_check = $conn->query("SHOW TABLES LIKE 'system_config'");
$test_results['database'] = [
    'name' => 'Database Configuration Table',
    'status' => $table_check->num_rows > 0 ? 'PASS' : 'FAIL',
    'message' => $table_check->num_rows > 0 ? 'system_config table exists' : 'system_config table missing'
];

// Test 5: Check administrators table for Azure ID column
$column_check = $conn->query("SHOW COLUMNS FROM administrators LIKE 'azure_id'");
$test_results['admin_table'] = [
    'name' => 'Administrators Table Azure ID Column',
    'status' => $column_check->num_rows > 0 ? 'PASS' : 'FAIL',
    'message' => $column_check->num_rows > 0 ? 'azure_id column exists' : 'azure_id column missing - you may need to add it'
];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>Azure AD Integration Test - Tax Registration System</title>
    <link href="../assets/css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.1.0/js/all.js" crossorigin="anonymous"></script>
</head>
<body class="sb-nav-fixed">
    <nav class="sb-topnav navbar navbar-expand navbar-dark bg-dark">
        <a class="navbar-brand ps-3" href="dashboard.php">Tax Registration System</a>
        <button class="btn btn-link btn-sm order-1 order-lg-0 me-4 me-lg-0" id="sidebarToggle" href="#!"><i class="fas fa-bars"></i></button>
        <ul class="navbar-nav ms-auto ms-md-0 me-3 me-lg-4">
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" id="navbarDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fas fa-user fa-fw"></i></a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                    <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                </ul>
            </li>
        </ul>
    </nav>
    
    <div id="layoutSidenav">
        <div id="layoutSidenav_content">
            <main>
                <div class="container-fluid px-4">
                    <h1 class="mt-4">Azure AD Integration Test</h1>
                    <ol class="breadcrumb mb-4">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Azure AD Test</li>
                    </ol>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-check-circle me-1"></i>
                            Integration Test Results
                        </div>
                        <div class="card-body">
                            <?php foreach ($test_results as $test): ?>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <strong><?php echo $test['name']; ?></strong>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge <?php echo $test['status'] === 'PASS' ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $test['status']; ?>
                                        </span>
                                    </div>
                                    <div class="col-md-6">
                                        <?php echo $test['message']; ?>
                                        <?php if (isset($test['details']) && !empty($test['details'])): ?>
                                            <br><small class="text-muted">Available keys: <?php echo implode(', ', $test['details']); ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($test['url']) && $test['url'] !== 'N/A'): ?>
                                            <br><small class="text-muted">URL: <?php echo substr($test['url'], 0, 100) . '...'; ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <hr>
                            <?php endforeach; ?>
                            
                            <div class="mt-4">
                                <h5>Next Steps:</h5>
                                <ul>
                                    <?php if (!$azure_config): ?>
                                        <li><a href="azure_config_setup.php">Configure Azure AD settings</a></li>
                                    <?php endif; ?>
                                    
                                    <?php if ($column_check->num_rows == 0): ?>
                                        <li>Add azure_id column to administrators table:
                                            <code>ALTER TABLE administrators ADD COLUMN azure_id VARCHAR(255) NULL;</code>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php if ($azure_config && $auth_url): ?>
                                        <li><a href="<?php echo $auth_url; ?>" target="_blank">Test Azure AD Login</a></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            
                            <div class="mt-4">
                                <a href="azure_config_setup.php" class="btn btn-primary">Configure Azure AD</a>
                                <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                                <button onclick="location.reload()" class="btn btn-info">Refresh Test</button>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($azure_config): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-cog me-1"></i>
                            Current Configuration
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($azure_config as $key => $value): ?>
                                    <div class="col-md-6 mb-2">
                                        <strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:</strong>
                                        <?php if ($key === 'client_secret'): ?>
                                            <span class="text-muted">[Hidden for security]</span>
                                        <?php else: ?>
                                            <code><?php echo htmlspecialchars($value); ?></code>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/scripts.js"></script>
</body>
</html>
