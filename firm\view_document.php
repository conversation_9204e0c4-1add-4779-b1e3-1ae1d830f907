<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in as a firm
if (!isset($_SESSION['firm_id'])) {
    http_response_code(403);
    die('Unauthorized access');
}

$firm_id = $_SESSION['firm_id'];

// Get document type from URL
if (!isset($_GET['type']) || empty($_GET['type'])) {
    http_response_code(400);
    die('Invalid document type');
}

$document_type = $_GET['type'];

// Validate document type
$allowed_types = ['memart', 'cac_status', 'tax_clearance', 'utility_bill', 'incorporation_cert'];
if (!in_array($document_type, $allowed_types)) {
    http_response_code(400);
    die('Invalid document type');
}

// Check if firm_documents table exists
$table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");
if (!$table_check || $table_check->num_rows === 0) {
    http_response_code(500);
    die('Document system not initialized');
}

// Get document information from database
$sql = "SELECT * FROM firm_documents WHERE document_type = ? AND firm_id = ? AND status = 'uploaded'";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    http_response_code(500);
    die('Database error: ' . $conn->error);
}

$stmt->bind_param("si", $document_type, $firm_id);
$stmt->execute();
$result = $stmt->get_result();
$document = $result->fetch_assoc();
$stmt->close();

if (!$document) {
    // Check if document exists but with different status
    $check_sql = "SELECT status FROM firm_documents WHERE document_type = ? AND firm_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("si", $document_type, $firm_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if ($check_result->num_rows > 0) {
        $doc_status = $check_result->fetch_assoc()['status'];
        $status_display = empty($doc_status) ? '(empty/null)' : $doc_status;
        http_response_code(404);
        die("Document found but status is: $status_display (not uploaded)");
    } else {
        http_response_code(404);
        die("Document type '$document_type' not found for this firm");
    }
}

// Check if file exists
$file_path = $document['file_path'];
if (!file_exists($file_path)) {
    http_response_code(404);
    die('File not found on server');
}

// Get file extension and set MIME type
$file_extension = strtolower(pathinfo($document['file_name'], PATHINFO_EXTENSION));
$mime_types = [
    'pdf' => 'application/pdf',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

$mime_type = $mime_types[$file_extension] ?? 'application/octet-stream';
$filename = $document['file_name'];

// Security: Only allow specific MIME types
$allowed_types = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg', 
    'image/png'
];

if (!in_array($mime_type, $allowed_types)) {
    http_response_code(403);
    die('File type not allowed');
}

// Set headers for file download/viewing
header('Content-Type: ' . $mime_type);
header('Content-Length: ' . filesize($file_path));
header('Content-Disposition: inline; filename="' . $filename . '"');
header('Cache-Control: private, max-age=3600');
header('X-Content-Type-Options: nosniff');

// Output file content
readfile($file_path);

$conn->close();
?>
