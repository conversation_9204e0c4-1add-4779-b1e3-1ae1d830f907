<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>🚀 Working Upload Test</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['document'])) {
    echo "<h3>Processing Upload...</h3>";
    
    $document_type = $_POST['document_type'];
    $file = $_FILES['document'];
    
    echo "<p>Document Type: $document_type</p>";
    echo "<p>File Name: " . $file['name'] . "</p>";
    echo "<p>File Size: " . number_format($file['size']) . " bytes</p>";
    
    // Validate file
    if ($file['error'] !== UPLOAD_ERR_OK) {
        echo "<p style='color: red;'>❌ Upload error: " . $file['error'] . "</p>";
        exit();
    }
    
    // Create upload directory
    $upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
        echo "<p style='color: green;'>✅ Created upload directory</p>";
    }
    
    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $unique_filename = $document_type . '_' . time() . '_' . uniqid() . '.' . $file_extension;
    $file_path = $upload_dir . $unique_filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        echo "<p style='color: green;'>✅ File saved to: $file_path</p>";
        
        // Check if document already exists
        $check_sql = "SELECT id FROM firm_documents WHERE firm_id = ? AND document_type = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("is", $firm_id, $document_type);
        $check_stmt->execute();
        $existing = $check_stmt->get_result()->fetch_assoc();
        $check_stmt->close();
        
        if ($existing) {
            echo "<p>Updating existing document...</p>";
            
            // Update existing document
            $update_sql = "UPDATE firm_documents SET 
                           file_name = ?, 
                           file_path = ?, 
                           file_size = ?, 
                           upload_date = NOW(),
                           status = 'uploaded'
                           WHERE firm_id = ? AND document_type = ?";
            
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("sssis", $file['name'], $file_path, $file['size'], $firm_id, $document_type);
            
            if ($update_stmt->execute()) {
                echo "<p style='color: green;'>✅ Database record updated successfully</p>";
                $success = true;
            } else {
                echo "<p style='color: red;'>❌ Failed to update database: " . $update_stmt->error . "</p>";
                unlink($file_path);
                $success = false;
            }
            $update_stmt->close();
        } else {
            echo "<p>Creating new document record...</p>";
            
            // Insert new document
            $insert_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status, upload_date) 
                           VALUES (?, ?, ?, ?, ?, 'uploaded', NOW())";
            
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("isssi", $firm_id, $document_type, $file['name'], $file_path, $file['size']);
            
            if ($insert_stmt->execute()) {
                echo "<p style='color: green;'>✅ Database record created successfully (ID: " . $conn->insert_id . ")</p>";
                $success = true;
            } else {
                echo "<p style='color: red;'>❌ Failed to create database record: " . $insert_stmt->error . "</p>";
                unlink($file_path);
                $success = false;
            }
            $insert_stmt->close();
        }
        
        if ($success) {
            echo "<div style='background: #d4edda; padding: 15px; margin: 15px 0; border-radius: 5px; border: 1px solid #c3e6cb;'>";
            echo "<h4 style='color: #155724;'>🎉 Upload Successful!</h4>";
            echo "<p style='color: #155724;'>Document uploaded and saved successfully. It should now persist after page refresh.</p>";
            echo "<p><a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>View in Dashboard</a></p>";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Failed to save file</p>";
    }
}

// Show current documents
echo "<h3>Current Documents</h3>";
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY upload_date DESC";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Type</th><th>File Name</th><th>Status</th><th>Upload Date</th><th>File Exists</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td style='color: " . ($file_exists ? 'green' : 'red') . ";'>" . ($file_exists ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>No documents found</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px;">
    <h3>📤 Upload Test Form</h3>
    <form method="POST" enctype="multipart/form-data">
        <p>
            <label><strong>Document Type:</strong></label><br>
            <select name="document_type" required style="padding: 8px; margin: 5px 0; width: 100%;">
                <option value="">Select document type</option>
                <option value="memart">CTC of Memorandum & Articles of Association</option>
                <option value="cac_status">CAC Company Status Report</option>
                <option value="tax_clearance">Current Tax Clearance Certificate</option>
                <option value="utility_bill">Current Utility Bill</option>
                <option value="incorporation_cert">CAC Certificate of Incorporation</option>
            </select>
        </p>
        <p>
            <label><strong>Select File:</strong></label><br>
            <input type="file" name="document" required style="padding: 8px; margin: 5px 0; width: 100%;">
            <small style="color: #666;">Any file type, max 10MB</small>
        </p>
        <p>
            <button type="submit" style="background: #28a745; color: white; padding: 12px 25px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">Upload Document</button>
        </p>
    </form>
</div>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h3 style="color: #856404;">📋 Test Steps</h3>
    <ol style="color: #856404;">
        <li><strong>Upload a document</strong> using the form above</li>
        <li><strong>Check success message</strong> - should show green success box</li>
        <li><strong>See document in table</strong> - should appear in the table above</li>
        <li><strong>Refresh this page</strong> - document should still be there</li>
        <li><strong>Go to dashboard</strong> - document should show as uploaded</li>
        <li><strong>Refresh dashboard</strong> - document should persist</li>
    </ol>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="?refresh=1" style="background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Refresh Page</a>
    </p>
</div>
