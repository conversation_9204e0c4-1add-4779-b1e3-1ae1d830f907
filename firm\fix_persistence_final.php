<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h1>🔧 Final Persistence Fix</h1>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Step 1: Ensure table exists with correct structure
echo "<h2>Step 1: Fix Database Table</h2>";

// Drop and recreate table to ensure correct structure
$drop_table = "DROP TABLE IF EXISTS firm_documents";
if ($conn->query($drop_table)) {
    echo "<p style='color: orange;'>⚠️ Dropped existing table</p>";
}

$create_table = "
CREATE TABLE firm_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    firm_id INT NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    status ENUM('pending', 'uploaded', 'rejected') DEFAULT 'uploaded',
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_firm_doc (firm_id, document_type),
    INDEX idx_firm_id (firm_id)
)";

if ($conn->query($create_table)) {
    echo "<p style='color: green;'>✅ Created fresh firm_documents table</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create table: " . $conn->error . "</p>";
    exit();
}

// Step 2: Create upload directory
echo "<h2>Step 2: Setup Upload Directory</h2>";

$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
if (!file_exists($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "<p style='color: green;'>✅ Created upload directory: $upload_dir</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create upload directory</p>";
        exit();
    }
} else {
    echo "<p style='color: green;'>✅ Upload directory exists: $upload_dir</p>";
}

// Step 3: Create test documents that will persist
echo "<h2>Step 3: Create Persistent Test Documents</h2>";

$test_documents = [
    'memart' => 'CTC of Memorandum & Articles of Association',
    'cac_status' => 'CAC Company Status Report',
    'utility_bill' => 'Current Utility Bill',
    'incorporation_cert' => 'CAC Certificate of Incorporation'
];

foreach ($test_documents as $type => $title) {
    // Create test file
    $filename = $type . '_test_' . date('Ymd_His') . '.txt';
    $filepath = $upload_dir . $filename;
    $content = "Test document: $title\nCreated: " . date('Y-m-d H:i:s') . "\nFirm ID: $firm_id";
    
    if (file_put_contents($filepath, $content)) {
        $filesize = filesize($filepath);
        
        // Insert into database with REPLACE to handle duplicates
        $insert_sql = "REPLACE INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status, upload_date) 
                       VALUES (?, ?, ?, ?, ?, 'uploaded', NOW())";
        
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("isssi", $firm_id, $type, $filename, $filepath, $filesize);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ Created: $title</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to save: $title - " . $stmt->error . "</p>";
        }
        $stmt->close();
    } else {
        echo "<p style='color: red;'>❌ Failed to create file for: $title</p>";
    }
}

// Step 4: Verify documents exist
echo "<h2>Step 4: Verify Documents Exist</h2>";

$verify_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY document_type";
$verify_stmt = $conn->prepare($verify_sql);
$verify_stmt->bind_param("i", $firm_id);
$verify_stmt->execute();
$verify_result = $verify_stmt->get_result();

if ($verify_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Type</th><th>File Name</th><th>Status</th><th>Upload Date</th><th>File Exists</th></tr>";
    
    while ($doc = $verify_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td style='color: " . ($file_exists ? 'green' : 'red') . ";'>" . ($file_exists ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p style='color: green;'><strong>✅ " . $verify_result->num_rows . " documents created and verified</strong></p>";
} else {
    echo "<p style='color: red;'>❌ No documents found after creation</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px;">
    <h3 style="color: #155724;">🎉 Persistence Fix Complete!</h3>
    <p style="color: #155724;">The database and test documents have been created. These should now persist after page refresh.</p>
    
    <h4 style="color: #155724;">Next Steps:</h4>
    <ol style="color: #155724;">
        <li><strong>Refresh this page</strong> - Documents should still be there</li>
        <li><strong>Go to dashboard</strong> - Documents should show as uploaded</li>
        <li><strong>Refresh dashboard</strong> - Documents should persist</li>
        <li><strong>Test upload</strong> - Try uploading new documents</li>
    </ol>
    
    <p style="margin-top: 15px;">
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px; font-weight: bold;">Go to Dashboard</a>
        <a href="?refresh=1" style="background: #28a745; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px;">Refresh This Page</a>
    </p>
</div>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h4 style="color: #856404;">⚠️ Important Notes:</h4>
    <ul style="color: #856404;">
        <li>This script recreated the database table with the correct structure</li>
        <li>Test documents were created that should persist across page refreshes</li>
        <li>The upload directory was properly set up with correct permissions</li>
        <li>If documents still don't persist, there may be a server configuration issue</li>
    </ul>
</div>
