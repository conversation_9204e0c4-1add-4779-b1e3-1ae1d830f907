<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    echo "<p><a href='../login.php'>Login</a></p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Adding Sample Documents for Testing</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Create upload directory if it doesn't exist
$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
    echo "<p style='color: green;'>✅ Created upload directory: $upload_dir</p>";
}

// Sample documents to create
$sample_documents = [
    [
        'type' => 'memart',
        'name' => 'MEMART_' . $firm_id . '_sample.pdf',
        'content' => 'Sample MEMART document content for testing purposes.',
        'size' => 1024
    ],
    [
        'type' => 'cac_status',
        'name' => 'CAC_Status_' . $firm_id . '_sample.pdf',
        'content' => 'Sample CAC Status Report document content for testing purposes.',
        'size' => 2048
    ],
    [
        'type' => 'utility_bill',
        'name' => 'Utility_Bill_' . $firm_id . '_sample.pdf',
        'content' => 'Sample Utility Bill document content for testing purposes.',
        'size' => 1536
    ],
    [
        'type' => 'incorporation_cert',
        'name' => 'Incorporation_' . $firm_id . '_sample.pdf',
        'content' => 'Sample Certificate of Incorporation document content for testing purposes.',
        'size' => 2560
    ]
];

echo "<h3>Creating Sample Documents:</h3>";

foreach ($sample_documents as $doc) {
    $file_path = $upload_dir . $doc['name'];
    
    // Check if document already exists in database
    $check_sql = "SELECT id FROM firm_documents WHERE firm_id = ? AND document_type = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("is", $firm_id, $doc['type']);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        echo "<p style='color: blue;'>ℹ️ Document already exists: " . $doc['type'] . "</p>";
        continue;
    }
    
    // Create sample file
    $content = "PDF Sample Document\n\n";
    $content .= "Document Type: " . strtoupper($doc['type']) . "\n";
    $content .= "Firm ID: " . $firm_id . "\n";
    $content .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
    $content .= $doc['content'] . "\n\n";
    $content .= "This is a sample document created for testing the document management system.\n";
    $content .= "In a real implementation, this would be an actual uploaded document.\n";
    
    if (file_put_contents($file_path, $content)) {
        $actual_size = filesize($file_path);
        
        // Insert into database
        $insert_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status) 
                       VALUES (?, ?, ?, ?, ?, 'uploaded')";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("isssi", $firm_id, $doc['type'], $doc['name'], $file_path, $actual_size);
        
        if ($insert_stmt->execute()) {
            echo "<p style='color: green;'>✅ Created: " . $doc['type'] . " (" . $doc['name'] . ")</p>";
        } else {
            echo "<p style='color: red;'>❌ Database error for " . $doc['type'] . ": " . $insert_stmt->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to create file: " . $doc['name'] . "</p>";
    }
}

// Show current documents
echo "<h3>Current Documents in Database:</h3>";
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY document_type";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Document Type</th><th>File Name</th><th>Status</th><th>Size</th><th>Upload Date</th><th>Actions</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . number_format($doc['file_size']) . " bytes</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td>";
        echo "<a href='view_document.php?type=" . $doc['document_type'] . "' target='_blank' style='margin-right: 5px;'>View</a>";
        echo "<a href='download_document.php?type=" . $doc['document_type'] . "'>Download</a>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No documents found.</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
    <h3 style="color: #155724;">Sample Documents Created!</h3>
    <p style="color: #155724;">Sample documents have been added for testing the view and download functionality.</p>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">View in Dashboard</a>
        <a href="test_upload.html" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Test Upload</a>
    </p>
</div>
