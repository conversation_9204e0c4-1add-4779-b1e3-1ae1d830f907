// Basic script.js file
document.addEventListener('DOMContentLoaded', function() {
    console.log('Tax Registration System script loaded');
    
    // Add any custom JavaScript functionality here
    
    // Example: Add confirmation for delete actions
    const deleteButtons = document.querySelectorAll('.btn-delete');
    if (deleteButtons) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Are you sure you want to delete this item?')) {
                    e.preventDefault();
                }
            });
        });
    }
});