<?php
require_once '../includes/config.php';
require_once '../includes/audit_functions.php';
session_start();

// Check if firm is logged in
if (!isset($_SESSION['firm_id'])) {
    header("Location: login.php");
    exit();
}

$firm_id = $_SESSION['firm_id'];

// Get practitioner ID if specified
$practitioner_id = isset($_GET['practitioner_id']) ? intval($_GET['practitioner_id']) : null;

// Get audit trail
if ($practitioner_id) {
    // Get audit trail for specific practitioner
    $audit_trail = get_association_audit_trail($practitioner_id, $firm_id, $conn);
    
    // Get practitioner details
    $practitioner_sql = "SELECT full_name, registration_number FROM tax_practitioners WHERE id = ?";
    $practitioner_stmt = $conn->prepare($practitioner_sql);
    $practitioner_stmt->bind_param("i", $practitioner_id);
    $practitioner_stmt->execute();
    $practitioner_result = $practitioner_stmt->get_result();
    $practitioner_data = $practitioner_result->fetch_assoc();
} else {
    // Get audit trail for all practitioners
    $audit_trail = get_firm_audit_trail($firm_id, $conn);
}

include '../includes/header.php';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-history me-2"></i>
            <?php if ($practitioner_id): ?>
                Association Audit Trail: <?php echo htmlspecialchars($practitioner_data['full_name']); ?>
            <?php else: ?>
                Association Audit Trail
            <?php endif; ?>
        </h2>
        <div>
            <a href="associated_practitioners.php" class="btn btn-primary me-2">
                <i class="fas fa-users me-2"></i>View All Practitioners
            </a>
            <a href="dashboard.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>
                    <?php if ($practitioner_id): ?>
                        Audit Records for <?php echo htmlspecialchars($practitioner_data['full_name']); ?>
                        <span class="badge bg-light text-dark ms-2">
                            Reg #: <?php echo htmlspecialchars($practitioner_data['registration_number']); ?>
                        </span>
                    <?php else: ?>
                        All Association Events
                    <?php endif; ?>
                </h4>
                <?php if (!$practitioner_id): ?>
                <div class="input-group" style="max-width: 300px;">
                    <span class="input-group-text bg-white">
                        <i class="fas fa-search text-primary"></i>
                    </span>
                    <input type="text" id="auditSearch" class="form-control" placeholder="Search audit trail...">
                </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($audit_trail) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="auditTable">
                        <thead class="table-light">
                            <tr>
                                <th>Date & Time</th>
                                <?php if (!$practitioner_id): ?>
                                <th>Practitioner</th>
                                <?php endif; ?>
                                <th>Event</th>
                                <th>Status Change</th>
                                <th>Performed By</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($audit_trail as $event): ?>
                                <tr>
                                    <td><?php echo date('M d, Y H:i:s', strtotime($event['timestamp'])); ?></td>
                                    
                                    <?php if (!$practitioner_id): ?>
                                    <td>
                                        <a href="association_audit_trail.php?practitioner_id=<?php echo $event['practitioner_id']; ?>">
                                            <?php echo htmlspecialchars($event['practitioner_name']); ?>
                                        </a>
                                    </td>
                                    <?php endif; ?>
                                    
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo strpos($event['event_type'], 'approve') !== false ? 'success' : 
                                                (strpos($event['event_type'], 'reject') !== false ? 'danger' : 
                                                    (strpos($event['event_type'], 'request') !== false ? 'info' : 'secondary')); 
                                        ?>">
                                            <?php 
                                            $event_name = str_replace('_', ' ', $event['event_type']);
                                            echo ucwords($event_name);
                                            ?>
                                        </span>
                                    </td>
                                    
                                    <td>
                                        <?php if ($event['status_before'] && $event['status_after']): ?>
                                            <span class="text-muted"><?php echo ucfirst($event['status_before']); ?></span>
                                            <i class="fas fa-arrow-right mx-2"></i>
                                            <span class="fw-bold"><?php echo ucfirst($event['status_after']); ?></span>