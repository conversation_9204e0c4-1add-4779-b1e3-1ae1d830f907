<?php
// Database setup for tax clearance records table
require_once '../includes/config.php';

echo "<h2>Setting up Tax Clearance Records Table</h2>";

// Create tax clearance records table
$create_tax_clearance_table = "
CREATE TABLE IF NOT EXISTS firm_tax_clearance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    firm_id INT NOT NULL,
    certificate_number VARCHAR(50) NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    issue_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    tax_office VARCHAR(100) NOT NULL,
    tax_amount DECIMAL(15,2) DEFAULT 0.00,
    tax_year YEAR NOT NULL,
    status ENUM('ISSUED', 'EXPIRED', 'REVOKED') DEFAULT 'ISSUED',
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'System',
    FOREIGN KEY (firm_id) REFERENCES tax_firms(id) ON DELETE CASCADE,
    UNIQUE KEY unique_firm_certificate (firm_id, certificate_number),
    INDEX idx_firm_id (firm_id),
    INDEX idx_certificate_number (certificate_number),
    INDEX idx_status (status),
    INDEX idx_tax_year (tax_year),
    INDEX idx_expiry_date (expiry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

if ($conn->query($create_tax_clearance_table)) {
    echo "<p style='color: green;'>✅ firm_tax_clearance table created successfully!</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create firm_tax_clearance table: " . $conn->error . "</p>";
}

// Insert sample data for testing
echo "<h3>Inserting Sample Data</h3>";

// Get a firm ID for testing (assuming there's at least one firm)
$firm_result = $conn->query("SELECT id, name FROM tax_firms LIMIT 1");
if ($firm_result && $firm_result->num_rows > 0) {
    $firm = $firm_result->fetch_assoc();
    $firm_id = $firm['id'];
    $firm_name = $firm['name'];
    
    // Sample tax clearance records
    $sample_records = [
        [
            'certificate_number' => '22547787799',
            'company_name' => $firm_name,
            'issue_date' => '2025-01-01',
            'expiry_date' => '2025-12-31',
            'tax_office' => 'MSTO ISOLO',
            'tax_amount' => 150000.00,
            'tax_year' => 2025,
            'status' => 'ISSUED',
            'notes' => 'Current tax clearance certificate for 2025',
            'created_by' => 'NCC_Mobifin'
        ],
        [
            'certificate_number' => '22547787798',
            'company_name' => $firm_name,
            'issue_date' => '2024-01-01',
            'expiry_date' => '2024-12-31',
            'tax_office' => 'MSTO ISOLO',
            'tax_amount' => 125000.00,
            'tax_year' => 2024,
            'status' => 'EXPIRED',
            'notes' => 'Previous year tax clearance certificate',
            'created_by' => 'NCC_Mobifin'
        ],
        [
            'certificate_number' => '22547787797',
            'company_name' => $firm_name,
            'issue_date' => '2023-01-01',
            'expiry_date' => '2023-12-31',
            'tax_office' => 'MSTO ISOLO',
            'tax_amount' => 100000.00,
            'tax_year' => 2023,
            'status' => 'EXPIRED',
            'notes' => '2023 tax clearance certificate',
            'created_by' => 'NCC_Mobifin'
        ]
    ];
    
    foreach ($sample_records as $record) {
        $stmt = $conn->prepare("INSERT INTO firm_tax_clearance (firm_id, certificate_number, company_name, issue_date, expiry_date, tax_office, tax_amount, tax_year, status, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("isssssdisss", 
            $firm_id, 
            $record['certificate_number'], 
            $record['company_name'], 
            $record['issue_date'], 
            $record['expiry_date'], 
            $record['tax_office'], 
            $record['tax_amount'], 
            $record['tax_year'], 
            $record['status'], 
            $record['notes'], 
            $record['created_by']
        );
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ Added certificate: " . $record['certificate_number'] . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to add certificate: " . $record['certificate_number'] . " - " . $stmt->error . "</p>";
        }
    }
    
    echo "<p style='color: green;'>✅ Sample tax clearance records inserted!</p>";
    
} else {
    echo "<p style='color: orange;'>⚠️ No firms found in database. Sample data not inserted.</p>";
}

echo "<h3>Setup Complete!</h3>";
echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>";

$conn->close();
?>
