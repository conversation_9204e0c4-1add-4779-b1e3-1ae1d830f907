<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Complete Document Workflow Test</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Test 1: Check database and file system
echo "<h3>1. System Status Check</h3>";

// Check if firm_documents table exists
$table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<p style='color: green;'>✅ Database table exists</p>";
} else {
    echo "<p style='color: red;'>❌ Database table missing</p>";
    echo "<p><a href='fix_documents_table.php'>Fix Database</a></p>";
}

// Check upload directory
$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
if (file_exists($upload_dir)) {
    echo "<p style='color: green;'>✅ Upload directory exists: $upload_dir</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Upload directory will be created on first upload</p>";
}

// Test 2: Current document status
echo "<h3>2. Current Document Status</h3>";
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY document_type";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

$document_counts = ['uploaded' => 0, 'pending' => 0, 'total' => 0];

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Document Type</th><th>Status</th><th>File Exists</th><th>Actions</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        $status = $doc['status'] ?? 'unknown';
        
        if ($status === 'uploaded' && $file_exists) {
            $document_counts['uploaded']++;
        } else {
            $document_counts['pending']++;
        }
        $document_counts['total']++;
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($status) . "</td>";
        echo "<td style='color: " . ($file_exists ? 'green' : 'red') . ";'>" . ($file_exists ? '✅ Yes' : '❌ No') . "</td>";
        echo "<td>";
        
        if ($file_exists && $status === 'uploaded') {
            echo "<a href='view_document.php?type=" . $doc['document_type'] . "' target='_blank' style='margin-right: 5px;'>Test View</a>";
            echo "<a href='download_document.php?type=" . $doc['document_type'] . "'>Test Download</a>";
        } else {
            echo "<span style='color: orange;'>Upload needed</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>No documents found. Upload some documents to test.</p>";
}

// Display current counts
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Current Document Counts:</h4>";
echo "<p><strong>Uploaded:</strong> " . $document_counts['uploaded'] . "</p>";
echo "<p><strong>Pending:</strong> " . $document_counts['pending'] . "</p>";
echo "<p><strong>Total:</strong> " . $document_counts['total'] . "</p>";
echo "</div>";

// Test 3: Upload functionality test
echo "<h3>3. Upload Functionality Test</h3>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Test Upload Process:</h4>";
echo "<form action='upload_document.php' method='POST' enctype='multipart/form-data' style='margin: 10px 0;'>";
echo "<p>";
echo "<label><strong>Document Type:</strong></label><br>";
echo "<select name='document_type' required style='padding: 5px; margin: 5px 0;'>";
echo "<option value=''>Select document type</option>";
echo "<option value='memart'>MEMART</option>";
echo "<option value='cac_status'>CAC Status Report</option>";
echo "<option value='tax_clearance'>Tax Clearance Certificate</option>";
echo "<option value='utility_bill'>Utility Bill</option>";
echo "<option value='incorporation_cert'>Certificate of Incorporation</option>";
echo "</select>";
echo "</p>";
echo "<p>";
echo "<label><strong>File:</strong></label><br>";
echo "<input type='file' name='document' accept='.pdf,.jpg,.jpeg,.png,.doc,.docx' required style='margin: 5px 0;'>";
echo "</p>";
echo "<p>";
echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Test Upload</button>";
echo "</p>";
echo "</form>";
echo "</div>";

// Test 4: API endpoints test
echo "<h3>4. API Endpoints Test</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Test API Endpoints:</h4>";

$endpoints = [
    'upload_document.php' => 'Document Upload API',
    'view_document.php' => 'Document View API',
    'download_document.php' => 'Document Download API'
];

foreach ($endpoints as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description - File exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $description - File missing</p>";
    }
}
echo "</div>";

// Test 5: Workflow instructions
echo "<h3>5. Complete Workflow Test Instructions</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>To test the complete workflow:</h4>";
echo "<ol>";
echo "<li><strong>Upload a document</strong> using the form above</li>";
echo "<li><strong>Check the dashboard</strong> to see updated counters</li>";
echo "<li><strong>Test view functionality</strong> by clicking 'Test View' links</li>";
echo "<li><strong>Test download functionality</strong> by clicking 'Test Download' links</li>";
echo "<li><strong>Verify counters update</strong> automatically after upload</li>";
echo "</ol>";
echo "</div>";

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
    <h3 style="color: #155724;">Testing Complete!</h3>
    <p style="color: #155724;">Use the tools above to test the complete document workflow.</p>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="test_upload.html" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Upload Test Page</a>
        <a href="fix_empty_status.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Fix Status Issues</a>
    </p>
</div>

<script>
// Auto-refresh counters every 5 seconds to show real-time updates
setInterval(function() {
    // This would be used in the dashboard to auto-refresh counters
    console.log('Counter refresh interval - would update document counts here');
}, 5000);
</script>
