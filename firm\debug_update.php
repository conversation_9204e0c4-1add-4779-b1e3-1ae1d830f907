<?php
session_start();
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated', 'debug' => 'No session']);
    exit();
}

require_once '../includes/config.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    $debug_info = [
        'session_firm_id' => $_SESSION['firm_id'],
        'input_received' => $input,
        'raw_input' => file_get_contents('php://input')
    ];
    
    if (!$input || !isset($input['section']) || !isset($input['data'])) {
        echo json_encode([
            'success' => false, 
            'message' => 'Invalid input data',
            'debug' => $debug_info
        ]);
        exit();
    }
    
    $section = $input['section'];
    $data = $input['data'];
    $firm_id = $_SESSION['firm_id'];
    
    $debug_info['parsed_section'] = $section;
    $debug_info['parsed_data'] = $data;
    $debug_info['firm_id'] = $firm_id;
    
    // Validate section
    $allowed_sections = ['address', 'contact', 'ceo'];
    if (!in_array($section, $allowed_sections)) {
        echo json_encode([
            'success' => false, 
            'message' => 'Invalid section',
            'debug' => $debug_info
        ]);
        exit();
    }
    
    // Test database connection
    if (!$conn) {
        echo json_encode([
            'success' => false, 
            'message' => 'Database connection failed',
            'debug' => $debug_info
        ]);
        exit();
    }
    
    // Check if firm exists
    $check_sql = "SELECT id, name FROM tax_firms WHERE id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $firm_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    $firm_info = $result->fetch_assoc();
    
    $debug_info['firm_exists'] = $firm_info ? true : false;
    $debug_info['firm_info'] = $firm_info;
    
    if (!$firm_info) {
        echo json_encode([
            'success' => false, 
            'message' => 'Firm not found',
            'debug' => $debug_info
        ]);
        exit();
    }
    
    // Prepare update query based on section
    switch ($section) {
        case 'address':
            $sql = "UPDATE tax_firms SET address = ?, city = ?, state = ?, postal_code = ? WHERE id = ?";
            $params = [
                $data['address'] ?? '',
                $data['city'] ?? '',
                $data['state'] ?? '',
                $data['postal_code'] ?? '',
                $firm_id
            ];
            $types = "ssssi";
            break;
            
        case 'contact':
            $sql = "UPDATE tax_firms SET email = ?, alternative_email = ?, phone = ?, website = ? WHERE id = ?";
            $params = [
                $data['email'] ?? '',
                $data['alternative_email'] ?? '',
                $data['phone'] ?? '',
                $data['website'] ?? '',
                $firm_id
            ];
            $types = "ssssi";
            break;
            
        case 'ceo':
            $sql = "UPDATE tax_firms SET ceo_name = ?, ceo_email = ?, ceo_phone = ? WHERE id = ?";
            $params = [
                $data['ceo_name'] ?? '',
                $data['ceo_email'] ?? '',
                $data['ceo_phone'] ?? '',
                $firm_id
            ];
            $types = "sssi";
            break;
    }
    
    $debug_info['sql'] = $sql;
    $debug_info['params'] = $params;
    $debug_info['types'] = $types;
    
    // Execute update
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        echo json_encode([
            'success' => false, 
            'message' => 'Database prepare error: ' . $conn->error,
            'debug' => $debug_info
        ]);
        exit();
    }
    
    $stmt->bind_param($types, ...$params);
    
    if (!$stmt->execute()) {
        echo json_encode([
            'success' => false, 
            'message' => 'Database execute error: ' . $stmt->error,
            'debug' => $debug_info
        ]);
        exit();
    }
    
    $debug_info['affected_rows'] = $stmt->affected_rows;
    
    echo json_encode([
        'success' => true,
        'message' => 'Profile updated successfully',
        'debug' => $debug_info
    ]);
    
    $stmt->close();
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => isset($debug_info) ? $debug_info : ['error' => 'Exception before debug setup']
    ]);
}

$conn->close();
?>
