<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/notification_system.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$success_message = '';
$error_message = '';

// Get application ID from URL
$application_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Process status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = $_POST['status'];
    $reason = $_POST['reason'] ?? '';
    
    // Update status and send notification
    if (update_application_status($application_id, $new_status, $reason, $_SESSION['admin_id'])) {
        $success_message = "Application status updated successfully. Notification email has been sent to the applicant.";
    } else {
        $error_message = "Failed to update application status.";
    }
}

// Get application details
$sql = "SELECT p.*, 
        CASE 
            WHEN p.registration_status = 'Pending' THEN 'Pending Review'
            WHEN p.registration_status = 'Info_Required' THEN 'Additional Information Required'
            WHEN p.registration_status = 'Active' THEN 'Approved' 
            WHEN p.registration_status = 'Rejected' THEN 'Rejected' 
            ELSE p.registration_status 
        END as display_status
        FROM tax_practitioners p
        WHERE p.id = ?";

$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $application_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // Application not found
    header("Location: applications.php");
    exit();
}

$application = $result->fetch_assoc();

// Include header
$page_title = "Review Application";
include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include '../includes/admin_sidebar.php'; ?>
        
        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Review Application</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="applications.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Applications
                    </a>
                </div>
            </div>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Applicant Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>Name:</strong> <?php echo htmlspecialchars($application['first_name'] . ' ' . $application['last_name']); ?></p>
                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($application['email']); ?></p>
                                    <p><strong>Phone:</strong> <?php echo htmlspecialchars($application['phone']); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Registration ID:</strong> <?php echo htmlspecialchars($application['registration_number']); ?></p>
                                    <p><strong>Submission Date:</strong> <?php echo date('F j, Y', strtotime($application['registration_date'])); ?></p>
                                    <p><strong>Current Status:</strong> 
                                        <span class="badge bg-<?php 
                                            echo $application['display_status'] == 'Approved' ? 'success' : 
                                                ($application['display_status'] == 'Rejected' ? 'danger' : 
                                                    ($application['display_status'] == 'Pending Review' ? 'warning' : 
                                                        ($application['display_status'] == 'Additional Information Required' ? 'info' : 'secondary'))); 
                                        ?>">
                                            <?php echo htmlspecialchars($application['display_status']); ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                            
                            <h6 class="mb-3">Qualifications & Experience</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>Qualification:</strong> <?php echo htmlspecialchars($application['qualification'] ?? 'N/A'); ?></p>
                                    <p><strong>Specialization:</strong> <?php echo htmlspecialchars($application['specialization'] ?? 'N/A'); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Years of Experience:</strong> <?php echo htmlspecialchars($application['years_of_experience'] ?? 'N/A'); ?></p>
                                    <p><strong>License Number:</strong> <?php echo htmlspecialchars($application['license_number'] ?? 'N/A'); ?></p>
                                </div>
                            </div>
                            
                            <!-- Additional application details can be added here -->
                        </div>
                    </div>
                    
                    <!-- Documents section can be added here -->
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Update Application Status</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="Pending" <?php echo $application['registration_status'] == 'Pending' ? 'selected' : ''; ?>>Pending Review</option>
                                        <option value="Info_Required" <?php echo $application['registration_status'] == 'Info_Required' ? 'selected' : ''; ?>>Additional Information Required</option>
                                        <option value="Active" <?php echo $application['registration_status'] == 'Active' ? 'selected' : ''; ?>>Approve</option>
                                        <option value="Rejected" <?php echo $application['registration_status'] == 'Rejected' ? 'selected' : ''; ?>>Reject</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3" id="reasonContainer">
                                    <label for="reason" class="form-label">Reason / Comments</label>
                                    <textarea class="form-control" id="reason" name="reason" rows="4"><?php echo htmlspecialchars($application['rejection_reason'] ?? ''); ?></textarea>
                                    <div class="form-text">Provide a reason for rejection or additional information required.</div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" name="update_status" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Status
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Status History</h5>
                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush">
                                <?php
                                // Get status history
                                $history_sql = "SELECT * FROM status_change_log WHERE application_id = ? ORDER BY change_date DESC";
                                $history_stmt = $conn->prepare($history_sql);
                                $history_stmt->bind_param("i", $application_id);
                                $history_stmt->execute();
                                $history_result = $history_stmt->get_result();
                                
                                if ($history_result->num_rows > 0) {
                                    while ($log = $history_result->fetch_assoc()) {
                                        echo '<li class="list-group-item">';
                                        echo '<div class="d-flex justify-content-between">';
                                        echo '<span><strong>' . htmlspecialchars($log['new_status']) . '</strong></span>';
                                        echo '<small class="text-muted">' . date('M j, Y g:i A', strtotime($log['change_date'])) . '</small>';
                                        echo '</div>';
                                        if (!empty($log['reason'])) {
                                            echo '<small class="text-muted">' . htmlspecialchars($log['reason']) . '</small>';
                                        }
                                        echo '</li>';
                                    }
                                } else {
                                    echo '<li class="list-group-item text-center text-muted">No status changes recorded</li>';
                                }
                                ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const reasonContainer = document.getElementById('reasonContainer');
    
    // Show/hide reason field based on status
    function toggleReasonField() {
        const selectedStatus = statusSelect.value;
        if (selectedStatus === 'Rejected' || selectedStatus === 'Info_Required') {
            reasonContainer.style.display = 'block';
        } else {
            reasonContainer.style.display = 'none';
        }
    }
    
    // Initial toggle
    toggleReasonField();
    
    // Toggle on change
    statusSelect.addEventListener('change', toggleReasonField);
});
</script>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="rejectModalLabel">Reject Application</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="process_status_update.php" method="post">
        <div class="modal-body">
          <input type="hidden" name="application_id" value="<?php echo $application_id; ?>">
          <input type="hidden" name="action" value="reject">
          
          <div class="mb-3">
            <label for="rejection_reason" class="form-label">Rejection Reason:</label>
            <select class="form-select mb-2" id="rejection_reason_category" name="rejection_reason_category" required>
              <option value="">Select a reason category</option>
              <option value="Incomplete Documentation">Incomplete Documentation</option>
              <option value="Qualification Issues">Qualification Issues</option>
              <option value="Experience Requirements">Experience Requirements</option>
              <option value="Verification Failed">Verification Failed</option>
              <option value="Other">Other</option>
            </select>
          </div>
          
          <div class="mb-3">
            <label for="rejection_details" class="form-label">Detailed Explanation:</label>
            <textarea class="form-control" id="rejection_details" name="rejection_details" rows="4" required 
                      placeholder="Provide specific details about why this application is being rejected..."></textarea>
          </div>
          
          <div class="mb-3">
            <label for="next_steps" class="form-label">Recommended Next Steps:</label>
            <textarea class="form-control" id="next_steps" name="next_steps" rows="3" 
                      placeholder="Provide guidance on what the applicant should do next..."></textarea>
            <small class="text-muted">Leave blank to use default guidance based on rejection reason.</small>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-danger">Confirm Rejection</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Request Additional Information Modal -->
<div class="modal fade" id="infoRequestModal" tabindex="-1" aria-labelledby="infoRequestModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-info text-white">
        <h5 class="modal-title" id="infoRequestModalLabel">Request Additional Information</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="process_status_update.php" method="post">
        <div class="modal-body">
          <input type="hidden" name="application_id" value="<?php echo $application_id; ?>">
          <input type="hidden" name="action" value="request_info">
          
          <div class="mb-3">
            <label class="form-label">Select sections requiring updates:</label>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="sections_to_update[]" value="personal" id="section_personal">
              <label class="form-check-label" for="section_personal">Personal Information</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="sections_to_update[]" value="contact" id="section_contact">
              <label class="form-check-label" for="section_contact">Contact Information</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="sections_to_update[]" value="qualifications" id="section_qualifications">
              <label class="form-check-label" for="section_qualifications">Qualifications</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="sections_to_update[]" value="experience" id="section_experience">
              <label class="form-check-label" for="section_experience">Professional Experience</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="sections_to_update[]" value="documents" id="section_documents">
              <label class="form-check-label" for="section_documents">Supporting Documents</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="sections_to_update[]" value="references" id="section_references">
              <label class="form-check-label" for="section_references">Professional References</label>
            </div>
          </div>
          
          <div class="mb-3">
            <label for="info_request_details" class="form-label">Detailed Instructions:</label>
            <textarea class="form-control" id="info_request_details" name="info_request_details" rows="4" required 
                      placeholder="Provide specific details about what information needs to be updated or added..."></textarea>
          </div>
          
          <div class="mb-3">
            <label for="info_request_deadline" class="form-label">Response Deadline:</label>
            <input type="date" class="form-control" id="info_request_deadline" name="info_request_deadline" 
                   value="<?php echo date('Y-m-d', strtotime('+14 days')); ?>" required>
            <small class="text-muted">Default is 14 days from today.</small>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-info">Send Request</button>
        </div>
      </form>
    </div>
  </div>
</div>

<?php include '../includes/admin_footer.php'; ?>

