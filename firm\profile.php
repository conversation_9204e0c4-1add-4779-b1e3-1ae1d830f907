<?php
// Add debugging at the top
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once '../includes/config.php';

// Check if firm is logged in
if (!isset($_SESSION['firm_id'])) {
    header("Location: login.php");
    exit();
}

$firm_id = $_SESSION['firm_id'];
$firm_name = $_SESSION['firm_name'];

// Get firm data
$sql = "SELECT * FROM tax_firms WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $firm_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    die("Firm not found");
}

$firm_data = $result->fetch_assoc();

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $name = trim($_POST['name']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $tax_id = trim($_POST['tax_id']);
    $business_type = trim($_POST['business_type']);
    
    // Validate input
    $errors = [];
    
    if (empty($name)) {
        $errors[] = "Firm name is required";
    }
    
    if (empty($phone)) {
        $errors[] = "Phone number is required";
    }
    
    if (empty($address)) {
        $errors[] = "Address is required";
    }
    
    // If no errors, update profile
    if (empty($errors)) {
        $update_sql = "UPDATE tax_firms SET 
                      name = ?, 
                      phone = ?, 
                      address = ?, 
                      tax_id = ?, 
                      business_type = ? 
                      WHERE id = ?";
        
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("sssssi", $name, $phone, $address, $tax_id, $business_type, $firm_id);
        
        if ($update_stmt->execute()) {
            // Update session variable
            $_SESSION['firm_name'] = $name;
            
            $success_message = "Profile updated successfully";
            
            // Refresh firm data
            $stmt->execute();
            $result = $stmt->get_result();
            $firm_data = $result->fetch_assoc();
        } else {
            $error_message = "Error updating profile: " . $conn->error;
        }
    } else {
        $error_message = implode("<br>", $errors);
    }
}

include '../includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Firm Profile</li>
                </ol>
            </nav>
            
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-building me-2"></i>Firm Profile</h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success">
                            <?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger">
                            <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post" action="">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Firm Name</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($firm_data['name']); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" value="<?php echo htmlspecialchars($firm_data['email']); ?>" readonly>
                                <small class="text-muted">Email cannot be changed</small>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($firm_data['phone']); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="registration_number" class="form-label">Registration Number</label>
                                <input type="text" class="form-control" id="registration_number" value="<?php echo htmlspecialchars($firm_data['registration_number']); ?>" readonly>
                                <small class="text-muted">Registration number cannot be changed</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($firm_data['address']); ?></textarea>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="tax_id" class="form-label">Tax ID</label>
                                <input type="text" class="form-control" id="tax_id" name="tax_id" value="<?php echo htmlspecialchars($firm_data['tax_id']); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="business_type" class="form-label">Business Type</label>
                                <select class="form-select" id="business_type" name="business_type">
                                    <option value="Accounting Firm" <?php echo ($firm_data['business_type'] == 'Accounting Firm') ? 'selected' : ''; ?>>Accounting Firm</option>
                                    <option value="Tax Consultancy" <?php echo ($firm_data['business_type'] == 'Tax Consultancy') ? 'selected' : ''; ?>>Tax Consultancy</option>
                                    <option value="Law Firm" <?php echo ($firm_data['business_type'] == 'Law Firm') ? 'selected' : ''; ?>>Law Firm</option>
                                    <option value="Financial Services" <?php echo ($firm_data['business_type'] == 'Financial Services') ? 'selected' : ''; ?>>Financial Services</option>
                                    <option value="Other" <?php echo ($firm_data['business_type'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="registration_date" class="form-label">Registration Date</label>
                                <input type="text" class="form-control" id="registration_date" value="<?php echo date('F d, Y', strtotime($firm_data['registration_date'])); ?>" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="status" class="form-label">Account Status</label>
                                <input type="text" class="form-control" id="status" value="<?php echo htmlspecialchars($firm_data['registration_status']); ?>" readonly>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="dashboard.php" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>