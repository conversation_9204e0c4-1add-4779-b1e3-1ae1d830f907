<?php if ($application_data['registration_status'] == 'Rejected'): ?>
    <div class="card border-danger mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-times-circle me-2"></i>Application Rejected</h5>
        </div>
        <div class="card-body">
            <?php 
                // Parse the rejection reason JSON if it exists
                $rejection_info = [];
                if (!empty($application_data['rejection_reason'])) {
                    if (substr($application_data['rejection_reason'], 0, 1) === '{') {
                        // It's JSON format
                        $rejection_info = json_decode($application_data['rejection_reason'], true) ?? [];
                    } else {
                        // It's plain text (old format)
                        $rejection_info = ['reason' => $application_data['rejection_reason']];
                    }
                }
                
                $reason = $rejection_info['reason'] ?? 'Your application did not meet the required criteria.';
                $next_steps = $rejection_info['next_steps'] ?? 'Please review the rejection reason and consider submitting a new application.';
            ?>
            
            <h6 class="text-danger mb-3">Reason for Rejection:</h6>
            <p><?php echo htmlspecialchars($reason); ?></p>
            
            <h6 class="text-primary mb-3">Recommended Next Steps:</h6>
            <p><?php echo htmlspecialchars($next_steps); ?></p>
            
            <div class="mt-4">
                <a href="new_application.php" class="btn btn-primary">
                    <i class="fas fa-file-alt me-2"></i>Start New Application
                </a>
                <a href="contact_support.php" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-question-circle me-2"></i>Contact Support
                </a>
            </div>
        </div>
    </div>
<?php endif; ?>