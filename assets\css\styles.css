:root {
    --primary-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --accent-gradient: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    --dark-gradient: linear-gradient(135deg, #14532d 0%, #166534 100%);
    --light-green-gradient: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-soft: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-glow: 0 25px 50px -12px rgba(34, 197, 94, 0.25);
    --green-shadow: 0 10px 20px rgba(34, 197, 94, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Animated background elements */
.bg-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    animation: float 15s infinite ease-in-out;
}

.floating-shape:nth-child(1) {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-shape:nth-child(2) {
    width: 200px;
    height: 200px;
    top: 60%;
    left: 70%;
    animation-delay: -5s;
}

.floating-shape:nth-child(3) {
    width: 150px;
    height: 150px;
    top: 30%;
    left: 80%;
    animation-delay: -10s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
}

/* Admin container */
.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Admin header */
.admin-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-soft);
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo img {
    height: 50px;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.admin-nav ul {
    display: flex;
    list-style: none;
    gap: 1.5rem;
}

.admin-nav a {
    color: #4b5563;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.admin-nav a:hover {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.admin-nav a i {
    margin-right: 0.5rem;
}

/* Main content */
.admin-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-soft);
    padding: 2rem;
    margin-bottom: 2rem;
}

.admin-content h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

/* Dashboard stats */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #4b5563;
    margin-bottom: 0.5rem;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
}

/* Tables */
.table-container {
    overflow-x: auto;
    margin-bottom: 2rem;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th, table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #4b5563;
}

table tr:hover {
    background-color: #f9fafb;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 6px rgba(34, 197, 94, 0.2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(34, 197, 94, 0.3);
}

.btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(16, 185, 129, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(239, 68, 68, 0.3);
}

/* Forms */
.form-group {
    margin-bottom: 2rem;
    position: relative;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.8rem;
    font-size: 0.95rem;
}

.form-control {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f9fafb;
    color: #1f2937;
}

.form-control:focus {
    outline: none;
    border-color: #22c55e;
    background: white;
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.1);
    transform: translateY(-1px);
}

/* Alerts */
.alert {
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    border: none;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
}

.alert-success {
    background: var(--light-green-gradient);
    color: #14532d;
    border: 1px solid #22c55e;
}

.alert-success::before {
    background: var(--success-gradient);
}

.alert-danger {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #991b1b;
    border: 1px solid #ef4444;
}

.alert-danger::before {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* Admin footer */
.admin-footer {
    text-align: center;
    color: white;
    padding: 1rem 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .admin-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
    }
}
