<?php
require_once '../includes/config.php';
require_once '../includes/enhanced_certificate_generator.php';

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Handle GET requests for individual approvals
if ($_SERVER["REQUEST_METHOD"] == "GET" && isset($_GET['id']) && isset($_GET['action'])) {
    $practitioner_id = (int)$_GET['id'];
    $action = $_GET['action'];

    if ($action === 'approve') {
        // Update practitioner status to Active
        $sql = "UPDATE tax_practitioners SET registration_status = 'Active' WHERE id = ? AND registration_status = 'Pending'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $practitioner_id);

        if ($stmt->execute() && $stmt->affected_rows > 0) {
            // Generate certificate for approved practitioner
            $certificate = generatePractitionerCertificate($practitioner_id);

            if ($certificate) {
                // Send certificate notification email
                sendCertificateNotification($certificate);

                $_SESSION['success_message'] = "Registration approved successfully. Certificate generated and sent to practitioner.";
            } else {
                $_SESSION['warning_message'] = "Registration approved but certificate generation failed. Please generate manually.";
            }
        } else {
            $_SESSION['error_message'] = "Failed to approve registration. Please try again.";
        }
    } elseif ($action === 'reject') {
        // Update practitioner status to Rejected
        $sql = "UPDATE tax_practitioners SET registration_status = 'Rejected' WHERE id = ? AND registration_status = 'Pending'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $practitioner_id);

        if ($stmt->execute() && $stmt->affected_rows > 0) {
            $_SESSION['success_message'] = "Registration rejected successfully.";
        } else {
            $_SESSION['error_message'] = "Failed to reject registration. Please try again.";
        }
    }

    // Redirect back to pending registrations
    header("Location: pending_registrations.php");
    exit();
}

// Handle legacy POST requests (if any)
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['registration_id'])) {
    $registration_id = $_POST['registration_id'];

    // This is for backward compatibility - redirect to GET method
    header("Location: approve_registration.php?id=$registration_id&action=approve");
    exit();
}

// If no valid action, redirect to pending registrations
header("Location: pending_registrations.php");
exit();
?>
