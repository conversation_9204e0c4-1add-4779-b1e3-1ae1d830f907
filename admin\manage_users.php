<?php
require_once '../includes/config.php';
require_once '../includes/auth_check.php';
require_once '../includes/azure_ad_integration.php';
require_once '../includes/role_permissions.php';

// Check if user has appropriate permissions
if (!has_permission('manage_users')) {
    $_SESSION['alert'] = [
        'type' => 'danger',
        'message' => 'You do not have permission to manage users.'
    ];
    header('Location: dashboard.php');
    exit;
}

// Check if Azure AD libraries are installed
$azure_enabled = function_exists('check_azure_libraries') && check_azure_libraries();

// Process user invitation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'invite') {
    $email = trim($_POST['email']);
    $role = $_POST['role'];
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Please enter a valid email address.'
        ];
    } elseif (empty($role)) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Please select a role for the user.'
        ];
    } elseif (!$azure_enabled) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Azure AD integration is not properly configured. Required libraries are missing. Please run "composer require microsoft/microsoft-graph" in the project root.'
        ];
    } else {
        // Check if user already exists
        $check_sql = "SELECT id FROM administrators WHERE email = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $_SESSION['alert'] = [
                'type' => 'danger',
                'message' => 'A user with this email already exists.'
            ];
        } else {
            // Invite user via Azure AD
            $result = invite_azure_user($email, $role);
            
            if ($result) {
                $_SESSION['alert'] = [
                    'type' => 'success',
                    'message' => 'Invitation sent successfully to ' . $email
                ];
            } else {
                $_SESSION['alert'] = [
                    'type' => 'danger',
                    'message' => 'Failed to send invitation. Please check Azure AD configuration.'
                ];
            }
        }
    }
}

// Process user role update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_role') {
    $user_id = $_POST['user_id'];
    $new_role = $_POST['new_role'];
    
    // Update user role
    $update_sql = "UPDATE administrators SET role = ? WHERE id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("si", $new_role, $user_id);
    
    if ($update_stmt->execute()) {
        // Log the action
        log_admin_action($_SESSION['admin_id'], 'update_role', "Updated user ID $user_id role to $new_role");
        
        $_SESSION['alert'] = [
            'type' => 'success',
            'message' => 'User role updated successfully.'
        ];
    } else {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Failed to update user role: ' . $conn->error
        ];
    }
}

// Process user deactivation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'deactivate') {
    $user_id = $_POST['user_id'];
    
    // Check if trying to deactivate self
    if ($user_id == $_SESSION['admin_id']) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'You cannot deactivate your own account.'
        ];
    } else {
        // Deactivate user
        $update_sql = "UPDATE administrators SET is_active = 0 WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("i", $user_id);
        
        if ($update_stmt->execute()) {
            // Log the action
            log_admin_action($_SESSION['admin_id'], 'deactivate_user', "Deactivated user ID $user_id");
            
            $_SESSION['alert'] = [
                'type' => 'success',
                'message' => 'User deactivated successfully.'
            ];
        } else {
            $_SESSION['alert'] = [
                'type' => 'danger',
                'message' => 'Failed to deactivate user: ' . $conn->error
            ];
        }
    }
}

// Process user activation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'activate') {
    $user_id = $_POST['user_id'];
    
    // Activate user
    $update_sql = "UPDATE administrators SET is_active = 1 WHERE id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("i", $user_id);
    
    if ($update_stmt->execute()) {
        // Log the action
        log_admin_action($_SESSION['admin_id'], 'activate_user', "Activated user ID $user_id");
        
        $_SESSION['alert'] = [
            'type' => 'success',
            'message' => 'User activated successfully.'
        ];
    } else {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Failed to activate user: ' . $conn->error
        ];
    }
}

// Get all users
$sql = "SELECT a.*, 
        (SELECT COUNT(*) FROM admin_activity_log WHERE admin_id = a.id) as activity_count 
        FROM administrators a 
        ORDER BY a.is_active DESC, a.last_login DESC";
$result = $conn->query($sql);
$users = [];

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
}

// Get available roles
$available_roles = get_available_roles();

// Page title
$page_title = "Manage Users";
include '../includes/admin_header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><i class="fas fa-users me-2"></i> Manage Users</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Manage Users</li>
    </ol>
    
    <?php if (isset($_SESSION['alert'])): ?>
        <div class="alert alert-<?php echo $_SESSION['alert']['type']; ?> alert-dismissible fade show">
            <?php echo $_SESSION['alert']['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['alert']); ?>
    <?php endif; ?>
    
    <?php if (!$azure_enabled): ?>
        <div class="alert alert-warning">
            <strong>Warning:</strong> Azure AD integration is not properly configured. Required libraries are missing. 
            Please run <code>composer require microsoft/microsoft-graph</code> in the project root to enable Azure AD integration.
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-user-plus me-1"></i>
            Invite New User
        </div>
        <div class="card-body">
            <form method="post" action="">
                <input type="hidden" name="action" value="invite">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="col-md-4">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">Select Role</option>
                            <?php foreach ($available_roles as $role): ?>
                                <option value="<?php echo $role; ?>"><?php echo get_role_name($role); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary" <?php echo $azure_enabled ? '' : 'disabled'; ?>>
                            <i class="fas fa-paper-plane me-1"></i> Send Invitation
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            User Accounts
        </div>
        <div class="card-body">
            <table id="usersTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Last Login</th>
                        <th>Status</th>
                        <th>Activity</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td><?php echo get_role_name($user['role']); ?></td>
                            <td>
                                <?php echo $user['last_login'] ? date('M d, Y H:i', strtotime($user['last_login'])) : 'Never'; ?>
                            </td>
                            <td>
                                <?php if ($user['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="user_activity.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-info">
                                    <?php echo $user['activity_count']; ?> actions
                                </a>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <!-- Change Role -->
                                        <li>
                                            <a href="#" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#changeRoleModal<?php echo $user['id']; ?>">
                                                <i class="fas fa-user-tag me-1"></i> Change Role
                                            </a>
                                        </li>
                                        
                                        <!-- Activate/Deactivate -->
                                        <?php if ($user['id'] != $_SESSION['admin_id']): ?>
                                            <?php if ($user['is_active']): ?>
                                                <li>
                                                    <a href="#" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#deactivateModal<?php echo $user['id']; ?>">
                                                        <i class="fas fa-user-slash me-1"></i> Deactivate
                                                    </a>
                                                </li>
                                            <?php else: ?>
                                                <li>
                                                    <a href="#" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#activateModal<?php echo $user['id']; ?>">
                                                        <i class="fas fa-user-check me-1"></i> Activate
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                                
                                <!-- Change Role Modal -->
                                <div class="modal fade" id="changeRoleModal<?php echo $user['id']; ?>" tabindex="-1" aria-labelledby="changeRoleModalLabel<?php echo $user['id']; ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="changeRoleModalLabel<?php echo $user['id']; ?>">Change Role</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <form method="post" action="">
                                                <input type="hidden" name="action" value="update_role">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <div class="modal-body">
                                                    <p>Change role for <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>:</p>
                                                    <div class="mb-3">
                                                        <label for="new_role<?php echo $user['id']; ?>" class="form-label">New Role</label>
                                                        <select class="form-select" id="new_role<?php echo $user['id']; ?>" name="new_role" required>
                                                            <?php foreach ($available_roles as $role): ?>
                                                                <option value="<?php echo $role; ?>" <?php echo ($user['role'] === $role) ? 'selected' : ''; ?>>
                                                                    <?php echo get_role_name($role); ?>
                                                                </option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <button type="submit" class="btn btn-primary">Save Changes</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Deactivate Modal -->
                                <div class="modal fade" id="deactivateModal<?php echo $user['id']; ?>" tabindex="-1" aria-labelledby="deactivateModalLabel<?php echo $user['id']; ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deactivateModalLabel<?php echo $user['id']; ?>">Deactivate User</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to deactivate <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>?
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form method="post" action="" class="d-inline">
                                                    <input type="hidden" name="action" value="deactivate">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="btn btn-danger">Deactivate</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Activate Modal -->
                                <div class="modal fade" id="activateModal<?php echo $user['id']; ?>" tabindex="-1" aria-labelledby="activateModalLabel<?php echo $user['id']; ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="activateModalLabel<?php echo $user['id']; ?>">Activate User</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to activate <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>?
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form method="post" action="" class="d-inline">
                                                    <input type="hidden" name="action" value="activate">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="btn btn-success">Activate</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php include '../includes/admin_footer.php'; ?>
