<?php
require_once '../includes/config.php';
require_once '../includes/auth_check.php';
require_once '../includes/functions.php';

// Check if user has appropriate permissions
if (!has_permission('view_reports')) {
    $_SESSION['alert'] = [
        'type' => 'danger',
        'message' => 'You do not have permission to access the reporting system.'
    ];
    header('Location: dashboard.php');
    exit;
}

// Get summary statistics
$stats = [
    'total' => get_application_count(),
    'pending' => get_application_count('Pending'),
    'approved' => get_application_count('Approved'),
    'rejected' => get_application_count('Rejected'),
    'info_required' => get_application_count('Info_Required'),
    'under_review' => get_application_count('Under_Review')
];

// Get recent activity
$recent_activity = get_recent_status_changes(10);

// Page title
$page_title = "Reports Dashboard";
include '../includes/admin_header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><i class="fas fa-chart-bar me-2"></i><?php echo $page_title; ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Reports</li>
    </ol>

    <!-- Summary Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Total Applications</div>
                            <div class="display-6"><?php echo $stats['total']; ?></div>
                        </div>
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="application_list.php">View All</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Pending</div>
                            <div class="display-6"><?php echo $stats['pending']; ?></div>
                        </div>
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="application_list.php?status=Pending">View Pending</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Approved</div>
                            <div class="display-6"><?php echo $stats['approved']; ?></div>
                        </div>
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="application_list.php?status=Approved">View Approved</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Rejected</div>
                            <div class="display-6"><?php echo $stats['rejected']; ?></div>
                        </div>
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="application_list.php?status=Rejected">View Rejected</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Info Required</div>
                            <div class="display-6"><?php echo $stats['info_required']; ?></div>
                        </div>
                        <i class="fas fa-info-circle fa-2x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="application_list.php?status=Info_Required">View Details</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card bg-secondary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Under Review</div>
                            <div class="display-6"><?php echo $stats['under_review']; ?></div>
                        </div>
                        <i class="fas fa-search fa-2x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="application_list.php?status=Under_Review">View Details</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Selection -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-file-alt me-1"></i>
                    Available Reports
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-history me-2"></i>Historical Status Report</h5>
                                    <p class="card-text">View historical data of applications by status over time.</p>
                                </div>
                                <div class="card-footer">
                                    <a href="report_historical_status.php" class="btn btn-primary">Generate Report</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-chart-pie me-2"></i>Status Distribution</h5>
                                    <p class="card-text">View the distribution of applications by current status.</p>
                                </div>
                                <div class="card-footer">
                                    <a href="report_status_distribution.php" class="btn btn-primary">Generate Report</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-calendar-alt me-2"></i>Processing Time Analysis</h5>
                                    <p class="card-text">Analyze the average processing time for applications.</p>
                                </div>
                                <div class="card-footer">
                                    <a href="report_processing_time.php" class="btn btn-primary">Generate Report</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-history me-1"></i>
                    Recent Status Changes
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Application ID</th>
                                <th>Applicant</th>
                                <th>Previous Status</th>
                                <th>New Status</th>
                                <th>Changed By</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_activity as $activity): ?>
                            <tr>
                                <td><?php echo $activity['application_id']; ?></td>
                                <td><?php echo get_applicant_name($activity['application_id']); ?></td>
                                <td><span class="badge bg-secondary"><?php echo $activity['previous_status']; ?></span></td>
                                <td><span class="badge bg-<?php echo get_status_color($activity['new_status']); ?>"><?php echo $activity['new_status']; ?></span></td>
                                <td><?php echo get_admin_name($activity['changed_by']); ?></td>
                                <td><?php echo date('M d, Y H:i', strtotime($activity['change_date'])); ?></td>
                                <td>
                                    <a href="review_application.php?id=<?php echo $activity['application_id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer">
                    <a href="status_change_log.php" class="btn btn-sm btn-primary">View Full History</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/admin_footer.php'; ?>