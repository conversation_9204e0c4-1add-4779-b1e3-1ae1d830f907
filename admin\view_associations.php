<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_ids = $_POST['selected_associations'] ?? [];
    
    if (!empty($selected_ids) && in_array($action, ['activate', 'deactivate', 'terminate'])) {
        $success_count = 0;
        $error_count = 0;
        
        foreach ($selected_ids as $id) {
            $new_status = '';
            $end_date = null;
            
            switch ($action) {
                case 'activate':
                    $new_status = 'active';
                    break;
                case 'deactivate':
                    $new_status = 'inactive';
                    break;
                case 'terminate':
                    $new_status = 'inactive';
                    $end_date = date('Y-m-d');
                    break;
            }
            
            if ($end_date) {
                $update_sql = "UPDATE practitioner_firm_associations SET status = ?, end_date = ? WHERE id = ?";
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("ssi", $new_status, $end_date, $id);
            } else {
                $update_sql = "UPDATE practitioner_firm_associations SET status = ? WHERE id = ?";
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("si", $new_status, $id);
            }
            
            if ($stmt && $stmt->execute()) {
                $success_count++;
                
                // Log the action
                if (function_exists('log_admin_action')) {
                    log_admin_action($_SESSION['admin_id'], 'bulk_' . $action . '_association', "Bulk {$action}ed association ID: {$id}");
                }
            } else {
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            $_SESSION['alert'] = [
                'type' => 'success',
                'message' => "Successfully {$action}ed {$success_count} association(s)."
            ];
        }
        
        if ($error_count > 0) {
            $_SESSION['alert'] = [
                'type' => 'warning',
                'message' => "Failed to process {$error_count} association(s)."
            ];
        }
        
        header("Location: view_associations.php");
        exit();
    }
}

// Build query filters
$status_filter = $_GET['status'] ?? 'active';
$search_term = $_GET['search'] ?? '';
$firm_filter = $_GET['firm_id'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

$where_conditions = [];
$params = [];
$param_types = "";

// Filter by status
if ($status_filter !== 'all') {
    $where_conditions[] = "pfa.status = ?";
    $params[] = $status_filter;
    $param_types .= "s";
}

// Search functionality
if (!empty($search_term)) {
    $where_conditions[] = "(tp.full_name LIKE ? OR tp.email LIKE ? OR tp.registration_number LIKE ? OR tf.name LIKE ?)";
    $search_param = "%{$search_term}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= "ssss";
}

// Filter by firm
if (!empty($firm_filter)) {
    $where_conditions[] = "pfa.firm_id = ?";
    $params[] = $firm_filter;
    $param_types .= "i";
}

// Date range filter
if (!empty($date_from)) {
    $where_conditions[] = "pfa.start_date >= ?";
    $params[] = $date_from;
    $param_types .= "s";
}

if (!empty($date_to)) {
    $where_conditions[] = "pfa.start_date <= ?";
    $params[] = $date_to;
    $param_types .= "s";
}

$where_clause = !empty($where_conditions) ? implode(" AND ", $where_conditions) : "1=1";

// Pagination
$per_page = 20;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $per_page;

// Count total records
$count_sql = "SELECT COUNT(*) as total 
              FROM practitioner_firm_associations pfa
              JOIN tax_practitioners tp ON pfa.practitioner_id = tp.id
              JOIN tax_firms tf ON pfa.firm_id = tf.id
              WHERE {$where_clause}";
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// Get associations
$sql = "SELECT pfa.id, pfa.start_date, pfa.end_date, pfa.status,
               tp.id as practitioner_id, tp.full_name as practitioner_name, 
               tp.email as practitioner_email, tp.registration_number as practitioner_reg,
               tp.qualification, tp.specialization, tp.registration_status as practitioner_status,
               tf.id as firm_id, tf.name as firm_name, tf.email as firm_email,
               tf.registration_number as firm_reg, tf.business_type, tf.registration_status as firm_status
        FROM practitioner_firm_associations pfa
        JOIN tax_practitioners tp ON pfa.practitioner_id = tp.id
        JOIN tax_firms tf ON pfa.firm_id = tf.id
        WHERE {$where_clause}
        ORDER BY pfa.start_date DESC 
        LIMIT ? OFFSET ?";

$stmt = $conn->prepare($sql);
$params[] = $per_page;
$params[] = $offset;
$param_types .= "ii";
$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

// Get firms for filter dropdown
$firms_sql = "SELECT id, name FROM tax_firms WHERE registration_status = 'Active' ORDER BY name";
$firms_result = $conn->query($firms_sql);

$page_title = "View Associations";
include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Practitioner-Firm Associations</h1>
                    <p class="text-muted">View and manage associations between practitioners and firms</p>
                </div>
                <div>
                    <span class="badge bg-info fs-6"><?php echo $total_records; ?> 
                        <?php echo $status_filter === 'all' ? 'Total' : ucfirst($status_filter); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (isset($_SESSION['alert'])): ?>
    <div class="alert alert-<?php echo $_SESSION['alert']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['alert']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['alert']); endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <?php
        // Get statistics
        $stats_sql = "SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
                        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive,
                        COUNT(CASE WHEN end_date IS NULL THEN 1 END) as ongoing
                      FROM practitioner_firm_associations";
        $stats_result = $conn->query($stats_sql);
        $stats = $stats_result->fetch_assoc();
        ?>
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Total Associations</h6>
                            <h2 class="mb-0"><?php echo $stats['total']; ?></h2>
                        </div>
                        <i class="fas fa-link fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Active</h6>
                            <h2 class="mb-0"><?php echo $stats['active']; ?></h2>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-secondary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Inactive</h6>
                            <h2 class="mb-0"><?php echo $stats['inactive']; ?></h2>
                        </div>
                        <i class="fas fa-pause-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Ongoing</h6>
                            <h2 class="mb-0"><?php echo $stats['ongoing']; ?></h2>
                        </div>
                        <i class="fas fa-infinity fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="">
                <div class="row g-3">
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Statuses</option>
                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="firm_id" class="form-label">Firm</label>
                        <select class="form-select" id="firm_id" name="firm_id">
                            <option value="">All Firms</option>
                            <?php while ($firm = $firms_result->fetch_assoc()): ?>
                            <option value="<?php echo $firm['id']; ?>" <?php echo $firm_filter == $firm['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($firm['name']); ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">Start Date From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="<?php echo htmlspecialchars($date_from); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">Start Date To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="<?php echo htmlspecialchars($date_to); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($search_term); ?>" 
                               placeholder="Name, email, reg#">
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Associations Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>Associations
                </h5>
                <div>
                    <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('activate')" id="bulkActivateBtn" disabled>
                        <i class="fas fa-play me-1"></i>Activate Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('deactivate')" id="bulkDeactivateBtn" disabled>
                        <i class="fas fa-pause me-1"></i>Deactivate Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('terminate')" id="bulkTerminateBtn" disabled>
                        <i class="fas fa-stop me-1"></i>Terminate Selected
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if ($result->num_rows > 0): ?>
            <form id="bulkActionForm" method="POST">
                <input type="hidden" name="bulk_action" id="bulkActionInput">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Practitioner</th>
                                <th>Firm</th>
                                <th>Association Period</th>
                                <th>Status</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" name="selected_associations[]" value="<?php echo $row['id']; ?>" 
                                           class="form-check-input association-checkbox">
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($row['practitioner_name']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-id-card me-1"></i><?php echo htmlspecialchars($row['practitioner_reg']); ?>
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($row['practitioner_email']); ?>
                                        </small>
                                        <?php if (!empty($row['specialization'])): ?>
                                        <br>
                                        <small class="text-info">
                                            <i class="fas fa-star me-1"></i><?php echo htmlspecialchars($row['specialization']); ?>
                                        </small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($row['firm_name']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-id-badge me-1"></i><?php echo htmlspecialchars($row['firm_reg']); ?>
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-building me-1"></i><?php echo htmlspecialchars($row['business_type'] ?? 'N/A'); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>Start:</strong> <?php echo date('M d, Y', strtotime($row['start_date'])); ?>
                                        <br>
                                        <strong>End:</strong> 
                                        <?php if ($row['end_date']): ?>
                                            <?php echo date('M d, Y', strtotime($row['end_date'])); ?>
                                        <?php else: ?>
                                            <span class="text-success">Ongoing</span>
                                        <?php endif; ?>
                                        <br>
                                        <small class="text-muted">
                                            Duration: 
                                            <?php 
                                            $start = new DateTime($row['start_date']);
                                            $end = $row['end_date'] ? new DateTime($row['end_date']) : new DateTime();
                                            $interval = $start->diff($end);
                                            echo $interval->format('%y years, %m months');
                                            ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $status_class = $row['status'] === 'active' ? 'success' : 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_class; ?>">
                                        <?php echo ucfirst($row['status']); ?>
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        P: <span class="badge bg-<?php echo $row['practitioner_status'] === 'Active' ? 'success' : 'warning'; ?> badge-sm">
                                            <?php echo $row['practitioner_status']; ?>
                                        </span>
                                        F: <span class="badge bg-<?php echo $row['firm_status'] === 'Active' ? 'success' : 'warning'; ?> badge-sm">
                                            <?php echo $row['firm_status']; ?>
                                        </span>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="view_association_details.php?id=<?php echo $row['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit_association.php?id=<?php echo $row['id']; ?>" 
                                           class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </form>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Associations Found</h5>
                <p class="text-muted">No associations match your current filter criteria.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <nav aria-label="Associations pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Previous</a>
            </li>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
            </li>
            <?php endfor; ?>
            
            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Next</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<script>
// Bulk action functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.association-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActionButtons();
});

document.querySelectorAll('.association-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActionButtons);
});

function updateBulkActionButtons() {
    const checkedBoxes = document.querySelectorAll('.association-checkbox:checked');
    const bulkButtons = ['bulkActivateBtn', 'bulkDeactivateBtn', 'bulkTerminateBtn'];
    
    bulkButtons.forEach(buttonId => {
        document.getElementById(buttonId).disabled = checkedBoxes.length === 0;
    });
}

function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.association-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        alert('Please select at least one association.');
        return;
    }
    
    const actionText = action.charAt(0).toUpperCase() + action.slice(1);
    if (confirm(`Are you sure you want to ${action} ${checkedBoxes.length} selected association(s)?`)) {
        document.getElementById('bulkActionInput').value = action;
        document.getElementById('bulkActionForm').submit();
    }
}

// Auto-hide alerts
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('show')) {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }
    });
}, 5000);
</script>

<?php include '../includes/admin_footer.php'; ?>
