<?php
session_start();
require_once '../../includes/config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

$firm_id = $_SESSION['firm_id'];
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getSecretaries($conn, $firm_id);
        break;
    case 'POST':
        addSecretary($conn, $firm_id);
        break;
    case 'PUT':
        updateSecretary($conn, $firm_id);
        break;
    case 'DELETE':
        deleteSecretary($conn, $firm_id);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

function getSecretaries($conn, $firm_id) {
    $status = $_GET['status'] ?? 'all';
    
    $sql = "SELECT * FROM firm_secretaries WHERE firm_id = ?";
    $params = [$firm_id];
    $types = "i";
    
    if ($status !== 'all') {
        $sql .= " AND status = ?";
        $params[] = $status;
        $types .= "s";
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $secretaries = [];
    while ($row = $result->fetch_assoc()) {
        $secretaries[] = $row;
    }
    
    // Get counts for each status
    $counts = [];
    $status_types = ['verified', 'pending', 'rejected'];
    
    foreach ($status_types as $status_type) {
        $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM firm_secretaries WHERE firm_id = ? AND status = ?");
        $count_stmt->bind_param("is", $firm_id, $status_type);
        $count_stmt->execute();
        $count_result = $count_stmt->get_result();
        $count_row = $count_result->fetch_assoc();
        $counts[$status_type] = $count_row['count'];
    }
    
    echo json_encode([
        'secretaries' => $secretaries,
        'counts' => $counts
    ]);
}

function addSecretary($conn, $firm_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $required_fields = ['name', 'qualification'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $sql = "INSERT INTO firm_secretaries (firm_id, name, email, phone, address, qualification, professional_body, membership_number, appointment_date, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isssssssss", 
        $firm_id,
        $input['name'],
        $input['email'] ?? null,
        $input['phone'] ?? null,
        $input['address'] ?? null,
        $input['qualification'],
        $input['professional_body'] ?? null,
        $input['membership_number'] ?? null,
        $input['appointment_date'] ?? date('Y-m-d'),
        $input['status'] ?? 'pending'
    );
    
    if ($stmt->execute()) {
        $new_id = $conn->insert_id;
        echo json_encode(['success' => true, 'id' => $new_id, 'message' => 'Company Secretary added successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to add company secretary']);
    }
}

function updateSecretary($conn, $firm_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Secretary ID is required']);
        return;
    }
    
    $sql = "UPDATE firm_secretaries SET name = ?, email = ?, phone = ?, address = ?, qualification = ?, professional_body = ?, membership_number = ?, appointment_date = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND firm_id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssssssii",
        $input['name'],
        $input['email'],
        $input['phone'],
        $input['address'],
        $input['qualification'],
        $input['professional_body'],
        $input['membership_number'],
        $input['appointment_date'],
        $input['status'],
        $input['id'],
        $firm_id
    );
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Company Secretary updated successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Company Secretary not found']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update company secretary']);
    }
}

function deleteSecretary($conn, $firm_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Secretary ID is required']);
        return;
    }
    
    $sql = "DELETE FROM firm_secretaries WHERE id = ? AND firm_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $input['id'], $firm_id);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Company Secretary deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Company Secretary not found']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete company secretary']);
    }
}

$conn->close();
?>
