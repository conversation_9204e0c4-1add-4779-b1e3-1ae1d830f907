<?php
session_start();
require_once '../includes/azure_ad_config.php';
require_once '../includes/db_connection.php';

try {
    // Handle Azure AD callback
    $userProfile = AzureADAuth::handleCallback();
    
    // Check if user exists in local database
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? OR azure_id = ?");
    $stmt->execute([$userProfile['mail'] ?? $userProfile['userPrincipalName'], $userProfile['id']]);
    $existingUser = $stmt->fetch();
    
    if ($existingUser) {
        // Update existing user with Azure AD info
        $stmt = $pdo->prepare("UPDATE users SET azure_id = ?, last_login = NOW() WHERE id = ?");
        $stmt->execute([$userProfile['id'], $existingUser['id']]);
        
        $_SESSION['user_id'] = $existingUser['id'];
        $_SESSION['username'] = $existingUser['username'];
        $_SESSION['role'] = $existingUser['role'];
        $_SESSION['auth_method'] = 'azure_ad';
        
    } else {
        // Create new user from Azure AD profile
        $username = $userProfile['mail'] ?? $userProfile['userPrincipalName'];
        $email = $userProfile['mail'] ?? $userProfile['userPrincipalName'];
        $fullName = $userProfile['displayName'];
        
        $stmt = $pdo->prepare("INSERT INTO users (username, email, full_name, azure_id, role, created_at) VALUES (?, ?, ?, ?, 'user', NOW())");
        $stmt->execute([$username, $email, $fullName, $userProfile['id']]);
        
        $userId = $pdo->lastInsertId();
        
        $_SESSION['user_id'] = $userId;
        $_SESSION['username'] = $username;
        $_SESSION['role'] = 'user';
        $_SESSION['auth_method'] = 'azure_ad';
    }
    
    // Redirect to dashboard
    header('Location: dashboard.php');
    exit;
    
} catch (Exception $e) {
    // Handle authentication error
    $_SESSION['error'] = 'Azure AD authentication failed: ' . $e->getMessage();
    header('Location: login.php');
    exit;
}
?>
