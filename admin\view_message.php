<?php
session_start();
require_once '../includes/config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['user_type'])) {
    header('Location: ../login.php');
    exit();
}

$message_id = (int)($_GET['id'] ?? 0);

if ($message_id === 0) {
    header('Location: contact_messages.php');
    exit();
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = $_POST['status'];
    $admin_notes = $_POST['admin_notes'] ?? '';
    
    $allowed_statuses = ['new', 'read', 'replied', 'closed'];
    if (in_array($new_status, $allowed_statuses)) {
        $update_sql = "UPDATE contact_messages SET status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ssi", $new_status, $admin_notes, $message_id);
        $update_stmt->execute();
        $update_stmt->close();
        
        $success_message = "Message status updated successfully!";
    }
}

// Get message details
$message_sql = "SELECT * FROM contact_messages WHERE id = ?";
$message_stmt = $conn->prepare($message_sql);
$message_stmt->bind_param("i", $message_id);
$message_stmt->execute();
$message_result = $message_stmt->get_result();

if ($message_result->num_rows === 0) {
    header('Location: contact_messages.php');
    exit();
}

$message = $message_result->fetch_assoc();
$message_stmt->close();

// Mark as read if it's new
if ($message['status'] === 'new') {
    $read_sql = "UPDATE contact_messages SET status = 'read', updated_at = NOW() WHERE id = ?";
    $read_stmt = $conn->prepare($read_sql);
    $read_stmt->bind_param("i", $message_id);
    $read_stmt->execute();
    $read_stmt->close();
    $message['status'] = 'read';
}

// Subject display names
$subject_names = [
    'document_issue' => 'Document Upload Issue',
    'document_correction' => 'Document Information Correction',
    'technical_support' => 'Technical Support',
    'general_inquiry' => 'General Inquiry',
    'feedback' => 'Feedback'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Message #<?php echo $message['id']; ?> - Admin Panel</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .back-link {
            color: #007bff;
            text-decoration: none;
            margin-bottom: 15px;
            display: inline-block;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .message-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .message-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .message-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
        }

        .meta-label {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .meta-value {
            font-size: 1rem;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-block;
        }

        .status-new { background: #fff3cd; color: #856404; }
        .status-read { background: #d1ecf1; color: #0c5460; }
        .status-replied { background: #d4edda; color: #155724; }
        .status-closed { background: #f8d7da; color: #721c24; }

        .message-content {
            padding: 20px;
        }

        .message-text {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            font-size: 1rem;
            line-height: 1.6;
        }

        .admin-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            font-family: inherit;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 1rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .firm-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .firm-info h4 {
            margin-bottom: 10px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="contact_messages.php" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Messages
            </a>
            <h1>Message #<?php echo $message['id']; ?></h1>
        </div>

        <?php if (isset($success_message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <div class="message-card">
            <div class="message-header">
                <div class="message-meta">
                    <div class="meta-item">
                        <span class="meta-label">From</span>
                        <span class="meta-value">
                            <strong><?php echo htmlspecialchars($message['firm_name']); ?></strong><br>
                            <a href="mailto:<?php echo htmlspecialchars($message['firm_email']); ?>">
                                <?php echo htmlspecialchars($message['firm_email']); ?>
                            </a>
                        </span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Subject</span>
                        <span class="meta-value"><?php echo $subject_names[$message['subject']] ?? $message['subject']; ?></span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Status</span>
                        <span class="meta-value">
                            <span class="status-badge status-<?php echo $message['status']; ?>">
                                <?php echo ucfirst($message['status']); ?>
                            </span>
                        </span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Date Received</span>
                        <span class="meta-value"><?php echo date('F j, Y \a\t g:i A', strtotime($message['created_at'])); ?></span>
                    </div>
                </div>
            </div>

            <div class="message-content">
                <h3 style="margin-bottom: 15px;">Message:</h3>
                <div class="message-text"><?php echo htmlspecialchars($message['message']); ?></div>
            </div>
        </div>

        <div class="admin-panel">
            <h3 style="margin-bottom: 20px;">Admin Actions</h3>
            
            <form method="POST">
                <div class="form-group">
                    <label for="status">Update Status:</label>
                    <select name="status" id="status" required>
                        <option value="new" <?php echo $message['status'] === 'new' ? 'selected' : ''; ?>>New</option>
                        <option value="read" <?php echo $message['status'] === 'read' ? 'selected' : ''; ?>>Read</option>
                        <option value="replied" <?php echo $message['status'] === 'replied' ? 'selected' : ''; ?>>Replied</option>
                        <option value="closed" <?php echo $message['status'] === 'closed' ? 'selected' : ''; ?>>Closed</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="admin_notes">Admin Notes (Internal):</label>
                    <textarea name="admin_notes" id="admin_notes" placeholder="Add internal notes about this message..."><?php echo htmlspecialchars($message['admin_notes'] ?? ''); ?></textarea>
                </div>

                <button type="submit" name="update_status" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Status
                </button>
                
                <a href="mailto:<?php echo htmlspecialchars($message['firm_email']); ?>?subject=Re: <?php echo urlencode($subject_names[$message['subject']] ?? $message['subject']); ?>&body=Dear <?php echo urlencode($message['firm_name']); ?>,%0A%0AThank you for contacting us regarding your documents.%0A%0A" 
                   class="btn btn-success" style="margin-left: 10px;">
                    <i class="fas fa-reply"></i> Reply via Email
                </a>
            </form>
        </div>
    </div>
</body>
</html>

<?php
$conn->close();
?>
