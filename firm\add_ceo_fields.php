<?php
// <PERSON><PERSON><PERSON> to add CEO fields to tax_firms table if they don't exist
require_once '../includes/config.php';

try {
    // Check if CEO fields exist
    $check_columns = $conn->query("SHOW COLUMNS FROM tax_firms LIKE 'ceo_%'");
    $existing_columns = [];
    
    while ($row = $check_columns->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
    }
    
    // Check for all required fields for the profile
    $all_required_fields = [
        'ceo_name' => 'VARCHAR(255) DEFAULT NULL',
        'ceo_email' => 'VARCHAR(255) DEFAULT NULL',
        'ceo_phone' => 'VARCHAR(255) DEFAULT NULL',
        'alternative_email' => 'VARCHAR(255) DEFAULT NULL',
        'website' => 'VARCHAR(255) DEFAULT NULL',
        'city' => 'VARCHAR(100) DEFAULT NULL',
        'state' => 'VARCHAR(100) DEFAULT NULL',
        'postal_code' => 'VARCHAR(20) DEFAULT NULL'
    ];

    // Get all existing columns
    $all_columns_result = $conn->query("SHOW COLUMNS FROM tax_firms");
    $existing_all_columns = [];

    while ($row = $all_columns_result->fetch_assoc()) {
        $existing_all_columns[] = $row['Field'];
    }

    $missing_fields = array_diff(array_keys($all_required_fields), $existing_all_columns);

    if (!empty($missing_fields)) {
        echo "<h3>Adding missing fields to tax_firms table...</h3>";

        foreach ($missing_fields as $field) {
            $sql = "ALTER TABLE tax_firms ADD COLUMN $field " . $all_required_fields[$field];

            if ($conn->query($sql)) {
                echo "<p style='color: green;'>✅ Added column: $field</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to add column $field: " . $conn->error . "</p>";
            }
        }

        echo "<p><strong>Missing fields have been added to the database.</strong></p>";
    } else {
        echo "<p style='color: green;'>✅ All required fields already exist in the database.</p>";
    }
    
    // Show current table structure
    echo "<h3>Current tax_firms table structure:</h3>";
    $result = $conn->query("DESCRIBE tax_firms");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

$conn->close();
?>
