<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Fixing Document Status Issues</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Get all documents for this firm
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY document_type";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

echo "<h3>Current Document Status:</h3>";

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Document Type</th><th>File Name</th><th>Current Status</th><th>File Exists</th><th>Action Needed</th><th>Fix</th></tr>";
    
    $fixes_needed = [];
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        $file_status = $file_exists ? '✅ Yes' : '❌ No';
        $file_color = $file_exists ? 'green' : 'red';
        
        // Determine what action is needed
        $action_needed = '';
        $fix_button = '';
        
        if ($file_exists && $doc['status'] !== 'uploaded') {
            $action_needed = 'Update status to "uploaded"';
            $fix_button = "<a href='?fix=" . $doc['id'] . "&action=set_uploaded' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Fix Status</a>";
            $fixes_needed[] = $doc['id'];
        } elseif (!$file_exists && $doc['status'] === 'uploaded') {
            $action_needed = 'File missing - set status to "pending"';
            $fix_button = "<a href='?fix=" . $doc['id'] . "&action=set_pending' style='background: #ffc107; color: black; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Fix Status</a>";
        } elseif ($file_exists && $doc['status'] === 'uploaded') {
            $action_needed = '✅ Status correct';
            $fix_button = '<span style="color: green;">OK</span>';
        } else {
            $action_needed = '⏳ Pending upload';
            $fix_button = '<span style="color: orange;">Waiting</span>';
        }
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td style='color: $file_color;'>$file_status</td>";
        echo "<td>$action_needed</td>";
        echo "<td>$fix_button</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Handle fix requests
    if (isset($_GET['fix']) && isset($_GET['action'])) {
        $fix_id = (int)$_GET['fix'];
        $action = $_GET['action'];
        
        if ($action === 'set_uploaded') {
            $update_sql = "UPDATE firm_documents SET status = 'uploaded' WHERE id = ? AND firm_id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ii", $fix_id, $firm_id);
            
            if ($update_stmt->execute()) {
                echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px; color: #155724;'>";
                echo "✅ Successfully updated document status to 'uploaded'";
                echo "</div>";
                echo "<script>setTimeout(() => window.location.href = 'fix_document_status.php', 1000);</script>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; color: #721c24;'>";
                echo "❌ Failed to update status: " . $update_stmt->error;
                echo "</div>";
            }
        } elseif ($action === 'set_pending') {
            $update_sql = "UPDATE firm_documents SET status = 'pending' WHERE id = ? AND firm_id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ii", $fix_id, $firm_id);
            
            if ($update_stmt->execute()) {
                echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px; color: #856404;'>";
                echo "⚠️ Successfully updated document status to 'pending'";
                echo "</div>";
                echo "<script>setTimeout(() => window.location.href = 'fix_document_status.php', 1000);</script>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; color: #721c24;'>";
                echo "❌ Failed to update status: " . $update_stmt->error;
                echo "</div>";
            }
        }
    }
    
    // Auto-fix all button
    if (!empty($fixes_needed) && !isset($_GET['fix'])) {
        echo "<div style='margin: 20px 0; padding: 15px; background: #fff3cd; border-radius: 5px;'>";
        echo "<h4 style='color: #856404;'>Quick Fix Available</h4>";
        echo "<p style='color: #856404;'>Some documents have files but incorrect status. Click below to fix all at once:</p>";
        echo "<a href='?fix_all=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Fix All Status Issues</a>";
        echo "</div>";
    }
    
    // Handle fix all
    if (isset($_GET['fix_all'])) {
        echo "<h3>Fixing All Status Issues:</h3>";
        
        // Get all documents that need fixing
        $fix_all_sql = "SELECT id, document_type, file_path, status FROM firm_documents WHERE firm_id = ?";
        $fix_all_stmt = $conn->prepare($fix_all_sql);
        $fix_all_stmt->bind_param("i", $firm_id);
        $fix_all_stmt->execute();
        $fix_all_result = $fix_all_stmt->get_result();
        
        $fixed_count = 0;
        
        while ($doc = $fix_all_result->fetch_assoc()) {
            $file_exists = file_exists($doc['file_path']);
            
            if ($file_exists && $doc['status'] !== 'uploaded') {
                // Update to uploaded
                $update_sql = "UPDATE firm_documents SET status = 'uploaded' WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("i", $doc['id']);
                
                if ($update_stmt->execute()) {
                    echo "<p style='color: green;'>✅ Fixed: " . $doc['document_type'] . " → uploaded</p>";
                    $fixed_count++;
                } else {
                    echo "<p style='color: red;'>❌ Failed to fix: " . $doc['document_type'] . "</p>";
                }
            } elseif (!$file_exists && $doc['status'] === 'uploaded') {
                // Update to pending
                $update_sql = "UPDATE firm_documents SET status = 'pending' WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("i", $doc['id']);
                
                if ($update_stmt->execute()) {
                    echo "<p style='color: orange;'>⚠️ Fixed: " . $doc['document_type'] . " → pending (file missing)</p>";
                    $fixed_count++;
                } else {
                    echo "<p style='color: red;'>❌ Failed to fix: " . $doc['document_type'] . "</p>";
                }
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; margin: 15px 0; border-radius: 5px;'>";
        echo "<h4 style='color: #155724;'>✅ Fix Complete!</h4>";
        echo "<p style='color: #155724;'>Fixed $fixed_count document status issues.</p>";
        echo "<p><a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Go to Dashboard</a></p>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: orange;'>⚠️ No documents found for this firm</p>";
    echo "<p><a href='create_test_documents.php'>Create Test Documents</a></p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
    <h3>Quick Actions:</h3>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="check_documents.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Check Documents</a>
        <a href="create_test_documents.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Create Test Docs</a>
    </p>
</div>
