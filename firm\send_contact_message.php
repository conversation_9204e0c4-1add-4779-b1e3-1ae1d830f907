<?php
session_start();
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

$firm_id = $_SESSION['firm_id'];

// Check if required data is provided
if (!isset($_POST['subject']) || !isset($_POST['message'])) {
    echo json_encode(['success' => false, 'message' => 'Subject and message are required']);
    exit;
}

$subject = trim($_POST['subject']);
$message = trim($_POST['message']);

// Validate input
if (empty($subject) || empty($message)) {
    echo json_encode(['success' => false, 'message' => 'Subject and message cannot be empty']);
    exit;
}

// Validate subject
$allowed_subjects = ['document_issue', 'document_correction', 'technical_support', 'general_inquiry', 'feedback'];
if (!in_array($subject, $allowed_subjects)) {
    echo json_encode(['success' => false, 'message' => 'Invalid subject selected']);
    exit;
}

try {
    // Get firm information
    $firm_sql = "SELECT company_name, email FROM firms WHERE id = ?";
    $firm_stmt = $conn->prepare($firm_sql);
    $firm_stmt->bind_param("i", $firm_id);
    $firm_stmt->execute();
    $firm_result = $firm_stmt->get_result();
    
    if ($firm_result->num_rows === 0) {
        throw new Exception('Firm not found');
    }
    
    $firm_data = $firm_result->fetch_assoc();
    $firm_stmt->close();
    
    // Create contact messages table if it doesn't exist
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS contact_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firm_id INT NOT NULL,
        firm_name VARCHAR(255) NOT NULL,
        firm_email VARCHAR(255) NOT NULL,
        subject VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_firm_id (firm_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    )";
    
    if (!$conn->query($create_table_sql)) {
        throw new Exception('Failed to create contact messages table: ' . $conn->error);
    }
    
    // Insert the contact message
    $insert_sql = "INSERT INTO contact_messages (firm_id, firm_name, firm_email, subject, message) 
                   VALUES (?, ?, ?, ?, ?)";
    
    $insert_stmt = $conn->prepare($insert_sql);
    if (!$insert_stmt) {
        throw new Exception('Database prepare failed: ' . $conn->error);
    }
    
    $insert_stmt->bind_param("issss", 
        $firm_id, 
        $firm_data['company_name'], 
        $firm_data['email'], 
        $subject, 
        $message
    );
    
    if (!$insert_stmt->execute()) {
        throw new Exception('Failed to save message: ' . $insert_stmt->error);
    }
    
    $message_id = $conn->insert_id;
    $insert_stmt->close();
    
    // Log the contact message
    error_log("Contact message received: ID=$message_id, Firm=$firm_id, Subject=$subject");

    // Subject display names
    $subject_names = [
        'document_issue' => 'Document Upload Issue',
        'document_correction' => 'Document Information Correction',
        'technical_support' => 'Technical Support',
        'general_inquiry' => 'General Inquiry',
        'feedback' => 'Feedback'
    ];

    $subject_display = $subject_names[$subject] ?? $subject;

    // Send email notification to administrators
    sendAdminNotification($message_id, $firm_data, $subject_display, $message);

    // Create browser notification for admin dashboard (if implemented)
    createAdminNotification($message_id, $firm_data['company_name'], $subject_display);
    
    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Your message has been sent successfully! We will review it and get back to you soon.',
        'message_id' => $message_id,
        'subject' => $subject_display
    ]);
    
} catch (Exception $e) {
    error_log("Contact message error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while sending your message. Please try again later.'
    ]);
}

// Function to send email notification to administrators
function sendAdminNotification($message_id, $firm_data, $subject_display, $message_content) {
    // Admin email addresses (you should configure these)
    $admin_emails = [
        '<EMAIL>',
        '<EMAIL>'
        // Add more admin emails as needed
    ];

    $firm_name = $firm_data['company_name'];
    $firm_email = $firm_data['email'];

    // Email subject
    $email_subject = "New Contact Message: $subject_display from $firm_name";

    // Email body
    $email_body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 20px; border: 1px solid #ddd; }
            .message-box { background: white; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .footer { background: #6c757d; color: white; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; }
            .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>New Contact Message Received</h2>
                <p>Message ID: #$message_id</p>
            </div>

            <div class='content'>
                <h3>Firm Information:</h3>
                <ul>
                    <li><strong>Company Name:</strong> $firm_name</li>
                    <li><strong>Email:</strong> $firm_email</li>
                    <li><strong>Subject:</strong> $subject_display</li>
                    <li><strong>Date:</strong> " . date('F j, Y \a\t g:i A') . "</li>
                </ul>

                <h3>Message:</h3>
                <div class='message-box'>
                    " . nl2br(htmlspecialchars($message_content)) . "
                </div>

                <p>
                    <a href='" . getBaseUrl() . "/admin/view_message.php?id=$message_id' class='btn'>View Full Message</a>
                </p>
            </div>

            <div class='footer'>
                <p>Tax Registration System - Admin Notification</p>
            </div>
        </div>
    </body>
    </html>
    ";

    // Email headers
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: Tax Registration System <<EMAIL>>',
        'Reply-To: ' . $firm_email,
        'X-Mailer: PHP/' . phpversion()
    ];

    // Send email to each admin
    foreach ($admin_emails as $admin_email) {
        $sent = mail($admin_email, $email_subject, $email_body, implode("\r\n", $headers));
        if ($sent) {
            error_log("Admin notification sent to: $admin_email for message ID: $message_id");
        } else {
            error_log("Failed to send admin notification to: $admin_email for message ID: $message_id");
        }
    }
}

// Function to create in-app notification for admin dashboard
function createAdminNotification($message_id, $firm_name, $subject_display) {
    global $conn;

    // Create admin_notifications table if it doesn't exist
    $create_notifications_table = "
    CREATE TABLE IF NOT EXISTS admin_notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(50) NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        reference_id INT,
        reference_type VARCHAR(50),
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_is_read (is_read),
        INDEX idx_created_at (created_at)
    )";

    $conn->query($create_notifications_table);

    // Insert notification
    $notification_title = "New Contact Message from $firm_name";
    $notification_message = "Subject: $subject_display";

    $insert_notification = "INSERT INTO admin_notifications (type, title, message, reference_id, reference_type)
                           VALUES ('contact_message', ?, ?, ?, 'contact_message')";
    $notification_stmt = $conn->prepare($insert_notification);
    $notification_stmt->bind_param("ssi", $notification_title, $notification_message, $message_id);
    $notification_stmt->execute();
    $notification_stmt->close();
}

// Function to get base URL for email links
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = dirname(dirname($script)); // Go up two levels from firm/send_contact_message.php
    return $protocol . '://' . $host . $path;
}

$conn->close();
?>
