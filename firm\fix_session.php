<?php
session_start();
require_once '../includes/config.php';

echo "<h2>Session Fix Tool</h2>";

// Show current session
echo "<h3>Current Session:</h3>";
if (!empty($_SESSION)) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Key</th><th>Value</th></tr>";
    foreach ($_SESSION as $key => $value) {
        echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>No session data found!</p>";
}

// Show available firms
echo "<h3>Available Firms in Database:</h3>";
$firms_sql = "SELECT id, name, email, registration_number, registration_status FROM tax_firms ORDER BY id";
$firms_result = $conn->query($firms_sql);

if ($firms_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Registration #</th><th>Status</th><th>Action</th></tr>";
    
    while ($firm = $firms_result->fetch_assoc()) {
        $is_current = (isset($_SESSION['firm_id']) && $_SESSION['firm_id'] == $firm['id']);
        $row_style = $is_current ? "background-color: #d4edda;" : "";
        
        echo "<tr style='$row_style'>";
        echo "<td>" . $firm['id'] . ($is_current ? " (Current)" : "") . "</td>";
        echo "<td>" . htmlspecialchars($firm['name']) . "</td>";
        echo "<td>" . htmlspecialchars($firm['email']) . "</td>";
        echo "<td>" . htmlspecialchars($firm['registration_number'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($firm['registration_status']) . "</td>";
        echo "<td>";
        if (!$is_current) {
            echo "<a href='?fix_session=" . $firm['id'] . "' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Fix Session</a>";
        } else {
            echo "<span style='color: green;'>✅ Current</span>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>No firms found in database!</p>";
}

// Handle session fix
if (isset($_GET['fix_session'])) {
    $correct_firm_id = (int)$_GET['fix_session'];
    
    // Get the correct firm data
    $fix_sql = "SELECT * FROM tax_firms WHERE id = ?";
    $fix_stmt = $conn->prepare($fix_sql);
    $fix_stmt->bind_param("i", $correct_firm_id);
    $fix_stmt->execute();
    $fix_result = $fix_stmt->get_result();
    $correct_firm = $fix_result->fetch_assoc();
    
    if ($correct_firm) {
        // Update session with correct data
        $_SESSION['firm_id'] = $correct_firm['id'];
        $_SESSION['firm_name'] = $correct_firm['name'];
        $_SESSION['firm_email'] = $correct_firm['email'];
        $_SESSION['firm_registration_number'] = $correct_firm['registration_number'];
        $_SESSION['firm_registration_status'] = $correct_firm['registration_status'];
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ Session Fixed Successfully!</h4>";
        echo "<p style='color: #155724; margin: 5px 0;'><strong>Updated session to:</strong></p>";
        echo "<ul style='color: #155724; margin: 5px 0;'>";
        echo "<li><strong>Firm ID:</strong> " . $correct_firm['id'] . "</li>";
        echo "<li><strong>Name:</strong> " . htmlspecialchars($correct_firm['name']) . "</li>";
        echo "<li><strong>Email:</strong> " . htmlspecialchars($correct_firm['email']) . "</li>";
        echo "<li><strong>Registration #:</strong> " . htmlspecialchars($correct_firm['registration_number'] ?? 'N/A') . "</li>";
        echo "</ul>";
        echo "<p style='margin: 10px 0 0 0;'>";
        echo "<a href='dashboard.php' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Go to Dashboard</a>";
        echo "<a href='fix_session.php' style='background: #6c757d; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>Refresh This Page</a>";
        echo "</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #f5c6cb;'>";
        echo "<p style='color: #721c24; margin: 0;'>❌ Could not find firm with ID: $correct_firm_id</p>";
        echo "</div>";
    }
}

// Check if session is now correct
if (isset($_SESSION['firm_id'])) {
    $session_firm_id = $_SESSION['firm_id'];
    
    // Verify this firm exists
    $verify_sql = "SELECT id, name FROM tax_firms WHERE id = ?";
    $verify_stmt = $conn->prepare($verify_sql);
    $verify_stmt->bind_param("i", $session_firm_id);
    $verify_stmt->execute();
    $verify_result = $verify_stmt->get_result();
    $verify_firm = $verify_result->fetch_assoc();
    
    echo "<h3>Session Status:</h3>";
    if ($verify_firm) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
        echo "<p style='color: #155724; margin: 0;'>✅ Session is correct! Firm ID " . $session_firm_id . " exists in database.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
        echo "<p style='color: #721c24; margin: 0;'>❌ Session firm ID " . $session_firm_id . " does not exist in database!</p>";
        echo "</div>";
    }
}

$conn->close();
?>

<style>
table { border-collapse: collapse; margin: 10px 0; width: 100%; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; font-weight: bold; }
body { font-family: Arial, sans-serif; margin: 20px; }
</style>

<div style="margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px;">
    <h3>Quick Actions:</h3>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Dashboard</a>
        <a href="test_profile_data.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Test Profile Data</a>
        <a href="debug_session.php" style="background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Debug Session</a>
    </p>
</div>
