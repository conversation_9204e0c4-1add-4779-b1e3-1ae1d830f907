        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTables if table exists
            if ($('#usersTable').length) {
                $('#usersTable').DataTable({
                    "pageLength": 25,
                    "order": [[ 4, "desc" ]], // Sort by status column
                    "columnDefs": [
                        { "orderable": false, "targets": [6] } // Disable sorting on Actions column
                    ],
                    "language": {
                        "search": "Search users:",
                        "lengthMenu": "Show _MENU_ users per page",
                        "info": "Showing _START_ to _END_ of _TOTAL_ users",
                        "paginate": {
                            "first": "First",
                            "last": "Last",
                            "next": "Next",
                            "previous": "Previous"
                        }
                    }
                });
            }
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
    </script>
</body>
</html>
