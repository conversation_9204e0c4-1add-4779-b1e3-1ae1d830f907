<?php
require_once '../includes/config.php';

// Check if user is logged in as admin
session_start();
if (!isset($_SESSION['admin_id'])) {
    echo "Access denied. Please log in as an administrator.";
    exit;
}

// Create administrators table if it doesn't exist
$create_admin_table = "CREATE TABLE IF NOT EXISTS `administrators` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','super_admin','readonly') NOT NULL DEFAULT 'admin',
  `last_login` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

// Create admin activity log table
$create_log_table = "CREATE TABLE IF NOT EXISTS `admin_activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

// Execute queries
$success = true;
$messages = [];

if ($conn->query($create_admin_table)) {
    $messages[] = "Administrators table created or already exists.";
} else {
    $success = false;
    $messages[] = "Error creating administrators table: " . $conn->error;
}

if ($conn->query($create_log_table)) {
    $messages[] = "Admin activity log table created or already exists.";
} else {
    $success = false;
    $messages[] = "Error creating admin activity log table: " . $conn->error;
}

// Output results
echo "<h1>Database Setup</h1>";
echo "<div style='margin: 20px; padding: 20px; border: 1px solid " . ($success ? "green" : "red") . ";'>";
foreach ($messages as $message) {
    echo "<p>" . $message . "</p>";
}
echo "</div>";

if ($success) {
    echo "<p>All tables created successfully. <a href='dashboard.php'>Return to Dashboard</a></p>";
} else {
    echo "<p>There were errors creating the tables. Please check the database configuration.</p>";
}
?>