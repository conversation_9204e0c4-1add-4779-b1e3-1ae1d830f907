<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>🔍 Upload Issue Diagnosis</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Step 1: Check database connection and table
echo "<h3>1. Database Connection & Table Check</h3>";

if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
}

// Check table structure
$table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<p style='color: green;'>✅ firm_documents table exists</p>";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE firm_documents");
    echo "<h4>Table Structure:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ firm_documents table does not exist</p>";
    
    // Create the table
    $create_table = "
    CREATE TABLE IF NOT EXISTS firm_documents (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firm_id INT NOT NULL,
        document_type VARCHAR(50) NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        status ENUM('pending', 'uploaded', 'rejected') DEFAULT 'pending',
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_firm_type (firm_id, document_type)
    )";
    
    if ($conn->query($create_table)) {
        echo "<p style='color: green;'>✅ Created firm_documents table</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create table: " . $conn->error . "</p>";
    }
}

// Step 2: Check current documents
echo "<h3>2. Current Documents in Database</h3>";
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY upload_date DESC";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Type</th><th>File Name</th><th>Status</th><th>Upload Date</th><th>File Exists</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        echo "<tr>";
        echo "<td>" . $doc['id'] . "</td>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td style='color: " . ($file_exists ? 'green' : 'red') . ";'>" . ($file_exists ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ No documents found in database</p>";
}

// Step 3: Test upload functionality
echo "<h3>3. Upload Functionality Test</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_upload'])) {
    echo "<h4>Processing Test Upload...</h4>";
    
    $document_type = $_POST['document_type'];
    echo "<p>Document Type: $document_type</p>";
    
    // Create upload directory
    $upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
    echo "<p>Upload Directory: $upload_dir</p>";
    
    if (!file_exists($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p style='color: green;'>✅ Created upload directory</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create upload directory</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Upload directory exists</p>";
    }
    
    // Create test file
    $test_filename = $document_type . '_test_' . time() . '.txt';
    $test_filepath = $upload_dir . $test_filename;
    $test_content = "Test document for $document_type\nUploaded at: " . date('Y-m-d H:i:s') . "\nFirm ID: $firm_id";
    
    if (file_put_contents($test_filepath, $test_content)) {
        $file_size = filesize($test_filepath);
        echo "<p style='color: green;'>✅ Test file created: $test_filename ($file_size bytes)</p>";
        
        // Check if document already exists
        $check_sql = "SELECT id FROM firm_documents WHERE firm_id = ? AND document_type = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("is", $firm_id, $document_type);
        $check_stmt->execute();
        $existing = $check_stmt->get_result()->fetch_assoc();
        $check_stmt->close();
        
        if ($existing) {
            echo "<p>Existing document found, updating...</p>";
            
            // Update existing document
            $update_sql = "UPDATE firm_documents SET 
                           file_name = ?, 
                           file_path = ?, 
                           file_size = ?, 
                           upload_date = NOW(),
                           status = 'uploaded'
                           WHERE firm_id = ? AND document_type = ?";
            
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("sssis", $test_filename, $test_filepath, $file_size, $firm_id, $document_type);
            
            if ($update_stmt->execute()) {
                echo "<p style='color: green;'>✅ Database record updated successfully</p>";
                echo "<p>Affected rows: " . $update_stmt->affected_rows . "</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to update database: " . $update_stmt->error . "</p>";
            }
            $update_stmt->close();
        } else {
            echo "<p>No existing document, inserting new...</p>";
            
            // Insert new document
            $insert_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status, upload_date) 
                           VALUES (?, ?, ?, ?, ?, 'uploaded', NOW())";
            
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("isssi", $firm_id, $document_type, $test_filename, $test_filepath, $file_size);
            
            if ($insert_stmt->execute()) {
                echo "<p style='color: green;'>✅ Database record inserted successfully</p>";
                echo "<p>Insert ID: " . $conn->insert_id . "</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to insert into database: " . $insert_stmt->error . "</p>";
            }
            $insert_stmt->close();
        }
        
        // Verify the record was saved
        echo "<h4>Verification:</h4>";
        $verify_sql = "SELECT * FROM firm_documents WHERE firm_id = ? AND document_type = ?";
        $verify_stmt = $conn->prepare($verify_sql);
        $verify_stmt->bind_param("is", $firm_id, $document_type);
        $verify_stmt->execute();
        $verify_result = $verify_stmt->get_result();
        
        if ($verify_result->num_rows > 0) {
            $doc = $verify_result->fetch_assoc();
            echo "<p style='color: green;'>✅ Record found in database:</p>";
            echo "<ul>";
            echo "<li>ID: " . $doc['id'] . "</li>";
            echo "<li>Type: " . $doc['document_type'] . "</li>";
            echo "<li>File: " . $doc['file_name'] . "</li>";
            echo "<li>Status: " . $doc['status'] . "</li>";
            echo "<li>Date: " . $doc['upload_date'] . "</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ Record not found in database after insert/update</p>";
        }
        $verify_stmt->close();
        
    } else {
        echo "<p style='color: red;'>❌ Failed to create test file</p>";
    }
}

// Step 4: Check upload script
echo "<h3>4. Upload Script Check</h3>";
$upload_script = 'upload_document.php';
if (file_exists($upload_script)) {
    echo "<p style='color: green;'>✅ Upload script exists: $upload_script</p>";
    
    // Check if it's readable
    if (is_readable($upload_script)) {
        echo "<p style='color: green;'>✅ Upload script is readable</p>";
    } else {
        echo "<p style='color: red;'>❌ Upload script is not readable</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Upload script not found: $upload_script</p>";
}

// Step 5: Check dashboard query
echo "<h3>5. Dashboard Query Test</h3>";
$dashboard_sql = "SELECT document_type, file_name, file_path, status, upload_date FROM firm_documents WHERE firm_id = ?";
$dashboard_stmt = $conn->prepare($dashboard_sql);
$dashboard_stmt->bind_param("i", $firm_id);
$dashboard_stmt->execute();
$dashboard_result = $dashboard_stmt->get_result();

$document_statuses = [];
while ($doc = $dashboard_result->fetch_assoc()) {
    $document_statuses[$doc['document_type']] = $doc;
}

echo "<p>Documents found by dashboard query: " . count($document_statuses) . "</p>";

if (!empty($document_statuses)) {
    foreach ($document_statuses as $type => $doc) {
        $has_file = ($doc['status'] === 'uploaded');
        echo "<p><strong>$type:</strong> Status = " . $doc['status'] . ", Has File = " . ($has_file ? 'Yes' : 'No') . "</p>";
    }
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px;">
    <h3>🧪 Test Upload</h3>
    <form method="POST">
        <p>
            <label><strong>Document Type:</strong></label><br>
            <select name="document_type" required style="padding: 5px; margin: 5px 0;">
                <option value="">Select document type</option>
                <option value="memart">MEMART</option>
                <option value="cac_status">CAC Status Report</option>
                <option value="tax_clearance">Tax Clearance Certificate</option>
                <option value="utility_bill">Utility Bill</option>
                <option value="incorporation_cert">Certificate of Incorporation</option>
            </select>
        </p>
        <p>
            <button type="submit" name="test_upload" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Test Upload Process</button>
        </p>
    </form>
</div>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h3 style="color: #856404;">📋 Next Steps</h3>
    <ol style="color: #856404;">
        <li>Use the test upload form above to create a test document</li>
        <li>Check if the record appears in the database table</li>
        <li>Refresh this page to see if the document persists</li>
        <li>Go to the dashboard to see if it shows as uploaded</li>
        <li>If issues persist, check the browser console and server logs</li>
    </ol>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="?clear_all=1" style="background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;" onclick="return confirm('Clear all test documents?')">Clear All</a>
    </p>
</div>

<?php
// Handle clear all
if (isset($_GET['clear_all'])) {
    $clear_sql = "DELETE FROM firm_documents WHERE firm_id = ?";
    $clear_stmt = $conn->prepare($clear_sql);
    $clear_stmt->bind_param("i", $firm_id);
    if ($clear_stmt->execute()) {
        echo "<script>alert('All documents cleared'); window.location.href = 'diagnose_upload_issue.php';</script>";
    }
}
?>
