<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../includes/config.php';

// Set up a test session if not already logged in
if (!isset($_SESSION['firm_id'])) {
    // Get the first firm for testing
    $firm_result = $conn->query("SELECT * FROM tax_firms LIMIT 1");
    if ($firm_result && $firm_result->num_rows > 0) {
        $firm = $firm_result->fetch_assoc();
        $_SESSION['firm_id'] = $firm['id'];
        $_SESSION['firm_name'] = $firm['name'];
        $_SESSION['firm_email'] = $firm['email'];
        echo "<p>Set up test session for firm: " . $firm['name'] . " (ID: " . $firm['id'] . ")</p>";
    } else {
        die("No firms found in database. Please create a firm first.");
    }
}

echo "<h2>Debug Shareholders API</h2>";

// Test the exact same code as the API
$input = [
    'name' => 'Debug Test Shareholder',
    'email' => '<EMAIL>',
    'phone' => '+***********-4567',
    'address' => 'Debug Test Address',
    'number_of_shares' => 1000,
    'share_percentage' => 15.75,
    'share_class' => 'Ordinary',
    'appointment_date' => date('Y-m-d'),
    'status' => 'pending'
];

echo "<h3>Input Data:</h3>";
echo "<pre>" . print_r($input, true) . "</pre>";

$firm_id = $_SESSION['firm_id'];

// Check required fields
$required_fields = ['name', 'number_of_shares', 'share_percentage'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || empty($input[$field])) {
        echo "<p style='color: red;'>Missing required field: $field</p>";
        exit;
    }
}
echo "<p style='color: green;'>✅ All required fields present</p>";

// Prepare SQL
$sql = "INSERT INTO firm_shareholders (firm_id, name, email, phone, address, number_of_shares, share_percentage, share_class, appointment_date, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

echo "<h3>SQL Query:</h3>";
echo "<pre>$sql</pre>";

$stmt = $conn->prepare($sql);
if (!$stmt) {
    echo "<p style='color: red;'>❌ Failed to prepare statement: " . $conn->error . "</p>";
    exit;
}
echo "<p style='color: green;'>✅ Statement prepared successfully</p>";

// Bind parameters
echo "<h3>Binding Parameters:</h3>";
echo "<p>Types: isssidsss</p>";
echo "<p>Values:</p>";
echo "<ul>";
echo "<li>firm_id (i): " . $firm_id . "</li>";
echo "<li>name (s): " . $input['name'] . "</li>";
echo "<li>email (s): " . ($input['email'] ?? 'null') . "</li>";
echo "<li>phone (s): " . ($input['phone'] ?? 'null') . "</li>";
echo "<li>address (s): " . ($input['address'] ?? 'null') . "</li>";
echo "<li>number_of_shares (i): " . $input['number_of_shares'] . "</li>";
echo "<li>share_percentage (d): " . $input['share_percentage'] . "</li>";
echo "<li>share_class (s): " . ($input['share_class'] ?? 'Ordinary') . "</li>";
echo "<li>appointment_date (s): " . ($input['appointment_date'] ?? date('Y-m-d')) . "</li>";
echo "<li>status (s): " . ($input['status'] ?? 'pending') . "</li>";
echo "</ul>";

$bind_result = $stmt->bind_param("isssidsss", 
    $firm_id,
    $input['name'],
    $input['email'] ?? null,
    $input['phone'] ?? null,
    $input['address'] ?? null,
    $input['number_of_shares'],
    $input['share_percentage'],
    $input['share_class'] ?? 'Ordinary',
    $input['appointment_date'] ?? date('Y-m-d'),
    $input['status'] ?? 'pending'
);

if (!$bind_result) {
    echo "<p style='color: red;'>❌ Failed to bind parameters: " . $stmt->error . "</p>";
    exit;
}
echo "<p style='color: green;'>✅ Parameters bound successfully</p>";

// Execute
echo "<h3>Executing Query:</h3>";
$execute_result = $stmt->execute();

if ($execute_result) {
    $new_id = $conn->insert_id;
    echo "<p style='color: green;'>✅ Query executed successfully! New ID: $new_id</p>";
    
    // Verify the record was inserted
    $verify_sql = "SELECT * FROM firm_shareholders WHERE id = ?";
    $verify_stmt = $conn->prepare($verify_sql);
    $verify_stmt->bind_param("i", $new_id);
    $verify_stmt->execute();
    $verify_result = $verify_stmt->get_result();
    
    if ($verify_result->num_rows > 0) {
        $record = $verify_result->fetch_assoc();
        echo "<h3>Inserted Record:</h3>";
        echo "<pre>" . print_r($record, true) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ Failed to execute query: " . $stmt->error . "</p>";
    echo "<p>MySQL Error Code: " . $stmt->errno . "</p>";
}

$stmt->close();
$conn->close();
?>

<p style="margin-top: 20px;">
    <a href="dashboard.php">← Back to Dashboard</a> | 
    <a href="test_shareholders_api.php">Test API Page</a>
</p>
 