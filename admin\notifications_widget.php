<?php
// This widget can be included in admin dashboard pages
require_once '../includes/config.php';

// Get unread contact messages count
$unread_messages_sql = "SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'";
$unread_result = $conn->query($unread_messages_sql);
$unread_count = $unread_result->fetch_assoc()['count'];

// Get recent contact messages
$recent_messages_sql = "SELECT id, firm_name, subject, created_at, status 
                        FROM contact_messages 
                        ORDER BY created_at DESC 
                        LIMIT 5";
$recent_result = $conn->query($recent_messages_sql);

// Subject display names
$subject_names = [
    'document_issue' => 'Document Upload Issue',
    'document_correction' => 'Document Information Correction',
    'technical_support' => 'Technical Support',
    'general_inquiry' => 'General Inquiry',
    'feedback' => 'Feedback'
];
?>

<style>
.notifications-widget {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.widget-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: between;
    align-items: center;
}

.widget-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.notification-badge {
    background: #dc3545;
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: 10px;
}

.widget-content {
    padding: 0;
}

.message-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: background-color 0.2s;
}

.message-item:hover {
    background: #f8f9fa;
}

.message-item:last-child {
    border-bottom: none;
}

.message-info {
    flex: 1;
}

.message-firm {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.message-subject {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.message-time {
    color: #adb5bd;
    font-size: 0.8rem;
}

.message-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 15px;
}

.status-new { background: #fff3cd; color: #856404; }
.status-read { background: #d1ecf1; color: #0c5460; }
.status-replied { background: #d4edda; color: #155724; }
.status-closed { background: #f8d7da; color: #721c24; }

.widget-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.view-all-btn {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.view-all-btn:hover {
    text-decoration: underline;
}

.no-messages {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.no-messages i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
    color: #adb5bd;
}
</style>

<div class="notifications-widget">
    <div class="widget-header">
        <h3 class="widget-title">
            <i class="fas fa-envelope"></i> Contact Messages
            <?php if ($unread_count > 0): ?>
                <span class="notification-badge"><?php echo $unread_count; ?></span>
            <?php endif; ?>
        </h3>
    </div>
    
    <div class="widget-content">
        <?php if ($recent_result->num_rows > 0): ?>
            <?php while ($message = $recent_result->fetch_assoc()): ?>
                <div class="message-item">
                    <div class="message-info">
                        <div class="message-firm"><?php echo htmlspecialchars($message['firm_name']); ?></div>
                        <div class="message-subject">
                            <?php echo $subject_names[$message['subject']] ?? $message['subject']; ?>
                        </div>
                        <div class="message-time">
                            <?php echo timeAgo($message['created_at']); ?>
                        </div>
                    </div>
                    <span class="message-status status-<?php echo $message['status']; ?>">
                        <?php echo ucfirst($message['status']); ?>
                    </span>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="no-messages">
                <i class="fas fa-inbox"></i>
                <p>No contact messages yet</p>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="widget-footer">
        <a href="contact_messages.php" class="view-all-btn">
            View All Messages
            <?php if ($unread_count > 0): ?>
                (<?php echo $unread_count; ?> unread)
            <?php endif; ?>
        </a>
    </div>
</div>

<?php
// Helper function to display time ago
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'Just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    
    return date('M j, Y', strtotime($datetime));
}
?>
