<?php
require_once '../includes/config.php';

// Admin firm details
$name = "Admin Tax Firm";
$email = "<EMAIL>";
$phone = "1234567890";
$address = "Admin Headquarters, 123 Main St";
$password = "admin123"; // You should use a strong password in production
$tax_id = "ADMIN-TAX-ID-001";
$business_type = "Government";

// Generate registration number
$prefix = "TF";
$year = date("Y");
$random = mt_rand(1000, 9999);
$registration_number = $prefix . $year . $random;

// Hash password
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

// Insert into database with Active status
$sql = "INSERT INTO tax_firms (name, email, phone, address, password, registration_number, tax_id, business_type, registration_status) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'Active')";

$stmt = $conn->prepare($sql);
$stmt->bind_param("ssssssss", $name, $email, $phone, $address, $hashed_password, $registration_number, $tax_id, $business_type);

if ($stmt->execute()) {
    echo "Admin firm created successfully!<br>";
    echo "Email: $email<br>";
    echo "Password: $password<br>";
    echo "Registration Number: $registration_number<br>";
} else {
    echo "Error creating admin firm: " . $conn->error;
}
?>