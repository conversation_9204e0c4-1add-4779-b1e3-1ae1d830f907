<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit();
}

require_once '../includes/config.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['section']) || !isset($input['data'])) {
        throw new Exception('Invalid input data');
    }
    
    $section = $input['section'];
    $data = $input['data'];
    $firm_id = $_SESSION['firm_id'];
    
    // Validate section
    $allowed_sections = ['address', 'contact', 'ceo'];
    if (!in_array($section, $allowed_sections)) {
        throw new Exception('Invalid section');
    }
    
    // Prepare update query based on section
    switch ($section) {
        case 'address':
            $sql = "UPDATE tax_firms SET address = ?, city = ?, state = ?, postal_code = ? WHERE id = ?";
            $params = [
                $data['address'] ?? '',
                $data['city'] ?? '',
                $data['state'] ?? '',
                $data['postal_code'] ?? '',
                $firm_id
            ];
            $types = "ssssi";
            break;
            
        case 'contact':
            $sql = "UPDATE tax_firms SET email = ?, alternative_email = ?, phone = ?, website = ? WHERE id = ?";
            $params = [
                $data['email'] ?? '',
                $data['alternative_email'] ?? '',
                $data['phone'] ?? '',
                $data['website'] ?? '',
                $firm_id
            ];
            $types = "ssssi";
            
            // Update session email if changed
            if (!empty($data['email'])) {
                $_SESSION['firm_email'] = $data['email'];
            }
            break;
            
        case 'ceo':
            $sql = "UPDATE tax_firms SET ceo_name = ?, ceo_email = ?, ceo_phone = ? WHERE id = ?";
            $params = [
                $data['ceo_name'] ?? '',
                $data['ceo_email'] ?? '',
                $data['ceo_phone'] ?? '',
                $firm_id
            ];
            $types = "sssi";
            break;
            
        default:
            throw new Exception('Invalid section');
    }
    
    // Execute update
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }
    
    $stmt->bind_param($types, ...$params);
    
    if (!$stmt->execute()) {
        throw new Exception('Database execute error: ' . $stmt->error);
    }

    // Debug: Log the update operation
    error_log("Profile update - Section: $section, Firm ID: $firm_id, Affected rows: " . $stmt->affected_rows);
    error_log("Update data: " . print_r($data, true));

    if ($stmt->affected_rows === 0) {
        // No rows affected could mean no changes or invalid ID
        // Check if firm exists
        $check_sql = "SELECT id, name FROM tax_firms WHERE id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("i", $firm_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        $firm_info = $result->fetch_assoc();

        if (!$firm_info) {
            // Get all available firms for debugging
            $all_firms_sql = "SELECT id, name, email FROM tax_firms LIMIT 5";
            $all_firms_result = $conn->query($all_firms_sql);
            $available_firms = [];
            while ($row = $all_firms_result->fetch_assoc()) {
                $available_firms[] = $row;
            }

            throw new Exception("Firm not found. Session firm_id: $firm_id. Available firms: " . json_encode($available_firms));
        }

        // Firm exists but no changes made (probably same data)
        echo json_encode([
            'success' => true,
            'message' => 'Profile updated successfully (no changes detected)',
            'firm_name' => $firm_info['name']
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'Profile updated successfully'
        ]);
    }
    
    $stmt->close();
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
