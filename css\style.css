/* General Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #f4f4f4;
    color: #333;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.main-content {
    background: #fff;
    padding: 20px;
    margin: 20px auto;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Header */
header {
    background: #35424a;
    color: white;
    padding: 20px 0;
    margin-bottom: 20px;
}

header h1 {
    margin: 0;
}

header nav {
    float: right;
    margin-top: 10px;
}

header nav ul {
    list-style: none;
}

header nav ul li {
    display: inline;
    margin-left: 20px;
}

header nav ul li a {
    color: white;
    text-decoration: none;
}

/* Forms */
.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button, .btn {
    display: inline-block;
    background: #35424a;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
}

button:hover, .btn:hover {
    background: #29353d;
}

.btn-success {
    background: #4CAF50;
}

.btn-danger {
    background: #f44336;
}

/* Sections */
.section {
    background: #f9f9f9;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}

/* Messages */
.success, .error {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
}

.success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

th, td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Footer */
footer {
    background: #35424a;
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: 20px;
}