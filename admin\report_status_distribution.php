<?php
require_once '../includes/config.php';
require_once '../includes/auth_check.php';
require_once '../includes/functions.php';

// Check if user has appropriate permissions
if (!has_permission('view_reports')) {
    $_SESSION['alert'] = [
        'type' => 'danger',
        'message' => 'You do not have permission to access the reporting system.'
    ];
    header('Location: dashboard.php');
    exit;
}

// Process filter parameters
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-90 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$export_format = isset($_GET['export']) ? $_GET['export'] : '';

// Get status distribution data
$status_data = get_status_distribution($start_date, $end_date);
$status_timeline = get_status_timeline($start_date, $end_date);

// Handle export if requested
if ($export_format) {
    export_report_data($status_data, 'status_distribution_report', $export_format);
    exit;
}

// Page title
$page_title = "Status Distribution Report";
include '../includes/admin_header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><i class="fas fa-chart-pie me-2"></i><?php echo $page_title; ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="reports_dashboard.php">Reports</a></li>
        <li class="breadcrumb-item active">Status Distribution</li>
    </ol>

    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Options
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">Apply Filters</button>
                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">Reset</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Export Options -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-download me-1"></i>
            Export Options
        </div>
        <div class="card-body">
            <div class="btn-group" role="group">
                <a href="<?php echo $_SERVER['REQUEST_URI'] . (strpos($_SERVER['REQUEST_URI'], '?') ? '&' : '?') . 'export=pdf'; ?>" class="btn btn-danger">
                    <i class="fas fa-file-pdf me-1"></i> Export as PDF
                </a>
                <a href="<?php echo $_SERVER['REQUEST_URI'] . (strpos($_SERVER['REQUEST_URI'], '?') ? '&' : '?') . 'export=excel'; ?>" class="btn btn-success">
                    <i class="fas fa-file-excel me-1"></i> Export as Excel
                </a>
                <a href="<?php echo $_SERVER['REQUEST_URI'] . (strpos($_SERVER['REQUEST_URI'], '?') ? '&' : '?') . 'export=csv'; ?>" class="btn btn-info">
                    <i class="fas fa-file-csv me-1"></i> Export as CSV
                </a>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Current Status Distribution
                </div>
                <div class="card-body">
                    <canvas id="statusPieChart" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-line me-1"></i>
                    Status Timeline
                </div>
                <div class="card-body">
                    <canvas id="statusTimelineChart" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Distribution Table -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Status Distribution Data (<?php echo date('M d, Y', strtotime($start_date)); ?> - <?php echo date('M d, Y', strtotime($end_date)); ?>)
        </div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Count</th>
                        <th>Percentage</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $total = 0;
                    if (!empty($status_data)) {
                        $total = array_sum(array_column($status_data, 'count'));
                    }
                    foreach ($status_data as $status):
                    ?>
                    <tr>
                        <td>
                            <span class="badge bg-<?php echo get_status_color($status['status']); ?> me-2">
                                <?php echo $status['status']; ?>
                            </span>
                        </td>
                        <td><?php echo $status['count']; ?></td>
                        <td><?php echo $total > 0 ? number_format(($status['count'] / $total) * 100, 1) : '0'; ?>%</td>
                        <td>
                            <a href="application_list.php?status=<?php echo $status['status']; ?>" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye me-1"></i> View Applications
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr class="fw-bold">
                        <td>Total</td>
                        <td><?php echo $total; ?></td>
                        <td>100%</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Initialize Charts
    document.addEventListener('DOMContentLoaded', function() {
        // Status Pie Chart
        var statusCtx = document.getElementById('statusPieChart').getContext('2d');
        var statusPieChart = new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: [
                    <?php foreach ($status_data as $status): ?>
                    '<?php echo $status['status']; ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    data: [
                        <?php foreach ($status_data as $status): ?>
                        <?php echo $status['count']; ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: [
                        '#ffc107', // Pending (warning)
                        '#6c757d', // Under Review (secondary)
                        '#17a2b8', // Info Required (info)
                        '#28a745', // Approved (success)
                        '#dc3545', // Rejected (danger)
                        '#6610f2'  // Other (purple)
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.raw || 0;
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = Math.round((value / total) * 100);
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Status Timeline Chart
        var timelineCtx = document.getElementById('statusTimelineChart').getContext('2d');
        var statusTimelineChart = new Chart(timelineCtx, {
            type: 'line',
            data: {
                labels: [
                    <?php foreach ($status_timeline as $date => $counts): ?>
                    '<?php echo date('M d', strtotime($date)); ?>',
                    <?php endforeach; ?>
                ],
                datasets: [
                    {
                        label: 'Pending',
                        data: [
                            <?php foreach ($status_timeline as $date => $counts): ?>
                            <?php echo $counts['Pending'] ?? 0; ?>,
                            <?php endforeach; ?>
                        ],
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.1
                    },
                    {
                        label: 'Under Review',
                        data: [
                            <?php foreach ($status_timeline as $date => $counts): ?>
                            <?php echo $counts['Under_Review'] ?? 0; ?>,
                            <?php endforeach; ?>
                        ],
                        borderColor: '#6c757d',
                        backgroundColor: 'rgba(108, 117, 125, 0.1)',
                        tension: 0.1
                    },
                    {
                        label: 'Approved',
                        data: [
                            <?php foreach ($status_timeline as $date => $counts): ?>
                            <?php echo $counts['Active'] ?? 0; ?>,
                            <?php endforeach; ?>
                        ],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.1
                    },
                    {
                        label: 'Rejected',
                        data: [
                            <?php foreach ($status_timeline as $date => $counts): ?>
                            <?php echo $counts['Rejected'] ?? 0; ?>,
                            <?php endforeach; ?>
                        ],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Status Changes Over Time'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    });

    // Reset filters function
    function resetFilters() {
        document.getElementById('start_date').value = '<?php echo date('Y-m-d', strtotime('-90 days')); ?>';
        document.getElementById('end_date').value = '<?php echo date('Y-m-d'); ?>';
        document.querySelector('form').submit();
    }
</script>

<?php include '../includes/admin_footer.php'; ?>
                