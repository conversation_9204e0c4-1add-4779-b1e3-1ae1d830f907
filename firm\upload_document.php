<?php
session_start();
require_once '../includes/config.php';

header('Content-Type: application/json');

// Check if user is logged in as a firm
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

$firm_id = $_SESSION['firm_id'];

// Debug: Log received data
error_log("Upload attempt - POST data: " . print_r($_POST, true));
error_log("Upload attempt - FILES data: " . print_r($_FILES, true));

// Check if file and document type are provided
if ((!isset($_FILES['document']) && !isset($_FILES['document_file'])) || !isset($_POST['document_type'])) {
    echo json_encode(['success' => false, 'message' => 'Missing file or document type']);
    exit;
}

// Handle both field names for compatibility
$file = $_FILES['document'] ?? $_FILES['document_file'];
$document_type = $_POST['document_type'];

// Validate file upload
if ($file['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'File upload error']);
    exit;
}

// Validate file size (10MB max)
$max_size = 10 * 1024 * 1024; // 10MB
if ($file['size'] > $max_size) {
    echo json_encode(['success' => false, 'message' => 'File size exceeds 10MB limit']);
    exit;
}

// Validate file type
$allowed_types = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png'
];

$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mime_type = finfo_file($finfo, $file['tmp_name']);
finfo_close($finfo);

if (!in_array($mime_type, $allowed_types)) {
    echo json_encode(['success' => false, 'message' => 'Invalid file type. Only PDF, DOC, DOCX, JPG, JPEG, and PNG files are allowed']);
    exit;
}

// Validate document type
$valid_document_types = [
    'memart', 'cac_status', 'utility_bill', 'director_change', 
    'secretary_change', 'shareholder_change', 'address_change', 
    'board_resolution', 'notification_letter'
];

if (!in_array($document_type, $valid_document_types)) {
    echo json_encode(['success' => false, 'message' => 'Invalid document type']);
    exit;
}

// Create upload directory if it doesn't exist
$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
if (!file_exists($upload_dir)) {
    if (!mkdir($upload_dir, 0755, true)) {
        echo json_encode(['success' => false, 'message' => 'Failed to create upload directory']);
        exit;
    }
}

// Generate unique filename
$file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
$unique_filename = $document_type . '_' . time() . '_' . uniqid() . '.' . $file_extension;
$file_path = $upload_dir . $unique_filename;

// Move uploaded file
if (!move_uploaded_file($file['tmp_name'], $file_path)) {
    echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file']);
    exit;
}

try {
    // Check if document already exists for this firm and type
    $check_sql = "SELECT id FROM firm_documents WHERE firm_id = ? AND document_type = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("is", $firm_id, $document_type);
    $check_stmt->execute();
    $existing = $check_stmt->get_result()->fetch_assoc();
    $check_stmt->close();

    if ($existing) {
        // Update existing document
        $update_sql = "UPDATE firm_documents SET
                       file_name = ?,
                       file_path = ?,
                       file_size = ?,
                       upload_date = NOW(),
                       status = 'uploaded'
                       WHERE firm_id = ? AND document_type = ?";
        
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("sssis",
            $file['name'],
            $file_path,
            $file['size'],
            $firm_id,
            $document_type
        );
        
        if ($update_stmt->execute()) {
            $update_stmt->close();

            // Log successful update
            error_log("Document updated successfully: firm_id=$firm_id, type=$document_type, file=" . $file['name']);

            echo json_encode([
                'success' => true,
                'message' => 'Document updated successfully',
                'document_type' => $document_type,
                'file_name' => $file['name'],
                'upload_date' => date('M j, Y')
            ]);
        } else {
            $update_stmt->close();
            // Delete uploaded file if database update fails
            unlink($file_path);
            error_log("Failed to update document record: " . $update_stmt->error);
            echo json_encode(['success' => false, 'message' => 'Failed to update document record: ' . $update_stmt->error]);
        }
    } else {
        // Insert new document
        $insert_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status, upload_date)
                       VALUES (?, ?, ?, ?, ?, 'uploaded', NOW())";

        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("isssi",
            $firm_id,
            $document_type,
            $file['name'],
            $file_path,
            $file['size']
        );
        
        if ($insert_stmt->execute()) {
            $insert_stmt->close();

            // Log successful insert
            error_log("Document inserted successfully: firm_id=$firm_id, type=$document_type, file=" . $file['name']);

            echo json_encode([
                'success' => true,
                'message' => 'Document uploaded successfully',
                'document_type' => $document_type,
                'file_name' => $file['name'],
                'upload_date' => date('M j, Y')
            ]);
        } else {
            $insert_stmt->close();
            // Delete uploaded file if database insert fails
            unlink($file_path);
            error_log("Failed to insert document record: " . $insert_stmt->error);
            echo json_encode(['success' => false, 'message' => 'Failed to save document record: ' . $insert_stmt->error]);
        }
    }

} catch (Exception $e) {
    // Delete uploaded file if any error occurs
    if (file_exists($file_path)) {
        unlink($file_path);
    }
    
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>
