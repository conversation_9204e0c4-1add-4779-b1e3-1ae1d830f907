<?php
/**
 * Azure AD Configuration Setup
 * 
 * This script helps you configure Azure AD settings for the tax registration system.
 * Run this once to set up your Azure AD integration.
 */

session_start();
require_once '../includes/config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$message = '';
$error = '';

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $tenant_id = trim($_POST['tenant_id']);
    $client_id = trim($_POST['client_id']);
    $client_secret = trim($_POST['client_secret']);
    $redirect_uri = trim($_POST['redirect_uri']);
    $scopes = trim($_POST['scopes']);
    
    // Validate input
    if (empty($tenant_id) || empty($client_id) || empty($client_secret) || empty($redirect_uri)) {
        $error = "All fields are required.";
    } else {
        // Check if system_config table exists
        $table_check = $conn->query("SHOW TABLES LIKE 'system_config'");
        if ($table_check->num_rows == 0) {
            // Create system_config table
            $create_table = "CREATE TABLE system_config (
                id INT AUTO_INCREMENT PRIMARY KEY,
                config_group VARCHAR(50) NOT NULL,
                config_key VARCHAR(100) NOT NULL,
                config_value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_config (config_group, config_key)
            )";
            
            if (!$conn->query($create_table)) {
                $error = "Failed to create system_config table: " . $conn->error;
            }
        }
        
        if (empty($error)) {
            // Insert or update Azure AD configuration
            $configs = [
                'tenant_id' => $tenant_id,
                'client_id' => $client_id,
                'client_secret' => $client_secret,
                'redirect_uri' => $redirect_uri,
                'scopes' => $scopes ?: 'openid profile email User.Read'
            ];
            
            $success_count = 0;
            foreach ($configs as $key => $value) {
                $sql = "INSERT INTO system_config (config_group, config_key, config_value, description) 
                        VALUES ('azure_ad', ?, ?, ?) 
                        ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), updated_at = NOW()";
                $stmt = $conn->prepare($sql);
                $description = "Azure AD " . ucfirst(str_replace('_', ' ', $key));
                $stmt->bind_param("sss", $key, $value, $description);
                
                if ($stmt->execute()) {
                    $success_count++;
                }
            }
            
            if ($success_count == count($configs)) {
                $message = "Azure AD configuration saved successfully!";
            } else {
                $error = "Some configuration settings failed to save.";
            }
        }
    }
}

// Get current configuration
$current_config = [];
$config_query = "SELECT config_key, config_value FROM system_config WHERE config_group = 'azure_ad'";
$result = $conn->query($config_query);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $current_config[$row['config_key']] = $row['config_value'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>Azure AD Configuration - Tax Registration System</title>
    <link href="../assets/css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.1.0/js/all.js" crossorigin="anonymous"></script>
</head>
<body class="sb-nav-fixed">
    <nav class="sb-topnav navbar navbar-expand navbar-dark bg-dark">
        <a class="navbar-brand ps-3" href="dashboard.php">Tax Registration System</a>
        <button class="btn btn-link btn-sm order-1 order-lg-0 me-4 me-lg-0" id="sidebarToggle" href="#!"><i class="fas fa-bars"></i></button>
        <ul class="navbar-nav ms-auto ms-md-0 me-3 me-lg-4">
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" id="navbarDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fas fa-user fa-fw"></i></a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                    <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                </ul>
            </li>
        </ul>
    </nav>
    
    <div id="layoutSidenav">
        <div id="layoutSidenav_content">
            <main>
                <div class="container-fluid px-4">
                    <h1 class="mt-4">Azure AD Configuration</h1>
                    <ol class="breadcrumb mb-4">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Azure AD Configuration</li>
                    </ol>
                    
                    <?php if (!empty($message)): ?>
                        <div class="alert alert-success"><?php echo $message; ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fab fa-microsoft me-1"></i>
                            Azure AD Settings
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <strong>Setup Instructions:</strong>
                                <ol>
                                    <li>Go to the <a href="https://portal.azure.com" target="_blank">Azure Portal</a></li>
                                    <li>Navigate to Azure Active Directory > App registrations</li>
                                    <li>Create a new app registration or use an existing one</li>
                                    <li>Copy the Application (client) ID and Directory (tenant) ID</li>
                                    <li>Create a client secret in Certificates & secrets</li>
                                    <li>Add the redirect URI in Authentication settings</li>
                                    <li>Fill in the form below with your Azure AD details</li>
                                </ol>
                            </div>
                            
                            <form method="post" action="">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input class="form-control" id="tenant_id" name="tenant_id" type="text" 
                                                   value="<?php echo htmlspecialchars($current_config['tenant_id'] ?? ''); ?>" 
                                                   placeholder="Tenant ID" required />
                                            <label for="tenant_id">Tenant ID</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input class="form-control" id="client_id" name="client_id" type="text" 
                                                   value="<?php echo htmlspecialchars($current_config['client_id'] ?? ''); ?>" 
                                                   placeholder="Client ID" required />
                                            <label for="client_id">Client ID</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-floating">
                                        <input class="form-control" id="client_secret" name="client_secret" type="password" 
                                               value="<?php echo htmlspecialchars($current_config['client_secret'] ?? ''); ?>" 
                                               placeholder="Client Secret" required />
                                        <label for="client_secret">Client Secret</label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-floating">
                                        <input class="form-control" id="redirect_uri" name="redirect_uri" type="url" 
                                               value="<?php echo htmlspecialchars($current_config['redirect_uri'] ?? 'http://localhost/tax_registration_system/admin/azure_login.php'); ?>" 
                                               placeholder="Redirect URI" required />
                                        <label for="redirect_uri">Redirect URI</label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-floating">
                                        <input class="form-control" id="scopes" name="scopes" type="text" 
                                               value="<?php echo htmlspecialchars($current_config['scopes'] ?? 'openid profile email User.Read'); ?>" 
                                               placeholder="Scopes" />
                                        <label for="scopes">Scopes (optional)</label>
                                    </div>
                                </div>
                                
                                <div class="mt-4 mb-0">
                                    <button type="submit" class="btn btn-primary">Save Configuration</button>
                                    <a href="dashboard.php" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/scripts.js"></script>
</body>
</html>
