<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/auth_check.php';
require_once '../includes/citn_integration.php';

// Check admin authentication
require_admin_auth();

// Initialize CITN integration
$citn = new CITNIntegration($conn);

// Handle export requests
if (isset($_GET['export'])) {
    $export_type = $_GET['export'];
    $format = $_GET['format'] ?? 'csv';
    
    switch ($export_type) {
        case 'verification_summary':
            exportVerificationSummary($conn, $format);
            break;
        case 'verification_logs':
            exportVerificationLogs($conn, $format);
            break;
        case 'practitioners_with_citn':
            exportPractitionersWithCITN($conn, $format);
            break;
    }
    exit;
}

// Get date range for reports
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');

// Get comprehensive statistics
$stats = getCITNComprehensiveStats($conn, $start_date, $end_date);

// Get verification trends
$trends = getCITNVerificationTrends($conn, $start_date, $end_date);

// Get membership type distribution
$membership_distribution = getCITNMembershipDistribution($conn);

// Get verification success rate by month
$monthly_success_rate = getMonthlyVerificationSuccessRate($conn);

$page_title = "CITN Reports & Analytics";
include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">CITN Reports & Analytics</h1>
                    <p class="text-muted">Comprehensive reporting on CITN verification activities</p>
                </div>
                <div>
                    <a href="citn_management.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-cog me-1"></i>Management
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?export=verification_summary&format=csv">Verification Summary (CSV)</a></li>
                            <li><a class="dropdown-item" href="?export=verification_logs&format=csv">Verification Logs (CSV)</a></li>
                            <li><a class="dropdown-item" href="?export=practitioners_with_citn&format=csv">Practitioners with CITN (CSV)</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i>Apply Filter
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i>Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_practitioners']); ?></h4>
                            <p class="mb-0">Total Practitioners</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['citn_verified']); ?></h4>
                            <p class="mb-0">CITN Verified</p>
                            <small><?php echo $stats['verification_rate']; ?>% of total</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['citn_pending']); ?></h4>
                            <p class="mb-0">Pending Verification</p>
                            <small><?php echo $stats['pending_rate']; ?>% of with CITN</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['verification_attempts_today']); ?></h4>
                            <p class="mb-0">Verifications Today</p>
                            <small><?php echo number_format($stats['avg_daily_verifications'], 1); ?> avg/day</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Verification Trends Chart -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">CITN Verification Trends</h5>
                </div>
                <div class="card-body">
                    <canvas id="verificationTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Status Distribution Chart -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Verification Status Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="statusDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Membership Type Distribution -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">CITN Membership Types</h5>
                </div>
                <div class="card-body">
                    <canvas id="membershipTypeChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Monthly Success Rate -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Monthly Verification Success Rate</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlySuccessChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Detailed Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Metric</th>
                                    <th>Count</th>
                                    <th>Percentage</th>
                                    <th>Change from Previous Period</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Total Practitioners</td>
                                    <td><?php echo number_format($stats['total_practitioners']); ?></td>
                                    <td>100%</td>
                                    <td><span class="badge bg-info">Baseline</span></td>
                                </tr>
                                <tr>
                                    <td>Practitioners with CITN Numbers</td>
                                    <td><?php echo number_format($stats['practitioners_with_citn']); ?></td>
                                    <td><?php echo number_format($stats['citn_coverage_rate'], 1); ?>%</td>
                                    <td>
                                        <?php if ($stats['citn_coverage_change'] > 0): ?>
                                            <span class="badge bg-success">+<?php echo number_format($stats['citn_coverage_change'], 1); ?>%</span>
                                        <?php elseif ($stats['citn_coverage_change'] < 0): ?>
                                            <span class="badge bg-danger"><?php echo number_format($stats['citn_coverage_change'], 1); ?>%</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">No change</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td>CITN Verified</td>
                                    <td><?php echo number_format($stats['citn_verified']); ?></td>
                                    <td><?php echo number_format($stats['verification_rate'], 1); ?>%</td>
                                    <td>
                                        <?php if ($stats['verification_rate_change'] > 0): ?>
                                            <span class="badge bg-success">+<?php echo number_format($stats['verification_rate_change'], 1); ?>%</span>
                                        <?php elseif ($stats['verification_rate_change'] < 0): ?>
                                            <span class="badge bg-danger"><?php echo number_format($stats['verification_rate_change'], 1); ?>%</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">No change</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Pending Verification</td>
                                    <td><?php echo number_format($stats['citn_pending']); ?></td>
                                    <td><?php echo number_format($stats['pending_rate'], 1); ?>%</td>
                                    <td><span class="badge bg-warning">Requires Action</span></td>
                                </tr>
                                <tr>
                                    <td>Invalid CITN Numbers</td>
                                    <td><?php echo number_format($stats['citn_invalid']); ?></td>
                                    <td><?php echo number_format($stats['invalid_rate'], 1); ?>%</td>
                                    <td><span class="badge bg-danger">Needs Review</span></td>
                                </tr>
                                <tr>
                                    <td>Expired Memberships</td>
                                    <td><?php echo number_format($stats['citn_expired']); ?></td>
                                    <td><?php echo number_format($stats['expired_rate'], 1); ?>%</td>
                                    <td><span class="badge bg-warning">Renewal Required</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Performance Metrics -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">API Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success"><?php echo number_format($stats['api_success_rate'], 1); ?>%</h4>
                                <p class="mb-0">API Success Rate</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info"><?php echo number_format($stats['avg_response_time'], 2); ?>s</h4>
                                <p class="mb-0">Avg Response Time</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?php echo number_format($stats['total_api_calls']); ?></h4>
                                <p class="mb-0">Total API Calls</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning"><?php echo number_format($stats['failed_api_calls']); ?></h4>
                                <p class="mb-0">Failed API Calls</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Verification Trends Chart
const trendsCtx = document.getElementById('verificationTrendsChart').getContext('2d');
new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: [<?php echo "'" . implode("','", array_keys($trends)) . "'"; ?>],
        datasets: [{
            label: 'Verified',
            data: [<?php echo implode(',', array_column($trends, 'verified')); ?>],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.1
        }, {
            label: 'Pending',
            data: [<?php echo implode(',', array_column($trends, 'pending')); ?>],
            borderColor: '#ffc107',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            tension: 0.1
        }, {
            label: 'Invalid',
            data: [<?php echo implode(',', array_column($trends, 'invalid')); ?>],
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'CITN Verification Trends Over Time'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Status Distribution Chart
const statusCtx = document.getElementById('statusDistributionChart').getContext('2d');
new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Verified', 'Pending', 'Invalid', 'Expired', 'Not Provided'],
        datasets: [{
            data: [
                <?php echo $stats['citn_verified']; ?>,
                <?php echo $stats['citn_pending']; ?>,
                <?php echo $stats['citn_invalid']; ?>,
                <?php echo $stats['citn_expired']; ?>,
                <?php echo $stats['total_practitioners'] - $stats['practitioners_with_citn']; ?>
            ],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#fd7e14',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Membership Type Chart
const membershipCtx = document.getElementById('membershipTypeChart').getContext('2d');
new Chart(membershipCtx, {
    type: 'bar',
    data: {
        labels: [<?php echo "'" . implode("','", array_keys($membership_distribution)) . "'"; ?>],
        datasets: [{
            label: 'Count',
            data: [<?php echo implode(',', array_values($membership_distribution)); ?>],
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#17a2b8'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Monthly Success Rate Chart
const monthlyCtx = document.getElementById('monthlySuccessChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: [<?php echo "'" . implode("','", array_keys($monthly_success_rate)) . "'"; ?>],
        datasets: [{
            label: 'Success Rate (%)',
            data: [<?php echo implode(',', array_values($monthly_success_rate)); ?>],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// Reset filters function
function resetFilters() {
    document.getElementById('start_date').value = '<?php echo date('Y-m-d', strtotime('-30 days')); ?>';
    document.getElementById('end_date').value = '<?php echo date('Y-m-d'); ?>';
    document.querySelector('form').submit();
}
</script>

<?php
// Helper functions for data retrieval
function getCITNComprehensiveStats($conn, $start_date, $end_date) {
    // Implementation would include complex queries to get all statistics
    // This is a simplified version
    $sql = "SELECT 
            COUNT(*) as total_practitioners,
            COUNT(CASE WHEN citn_number IS NOT NULL AND citn_number != '' THEN 1 END) as practitioners_with_citn,
            COUNT(CASE WHEN citn_verification_status = 'verified' THEN 1 END) as citn_verified,
            COUNT(CASE WHEN citn_verification_status = 'pending' THEN 1 END) as citn_pending,
            COUNT(CASE WHEN citn_verification_status = 'invalid' THEN 1 END) as citn_invalid,
            COUNT(CASE WHEN citn_verification_status = 'expired' THEN 1 END) as citn_expired
            FROM tax_practitioners";
    
    $result = $conn->query($sql);
    $stats = $result->fetch_assoc();
    
    // Calculate rates and additional metrics
    $stats['verification_rate'] = $stats['practitioners_with_citn'] > 0 ? 
        ($stats['citn_verified'] / $stats['practitioners_with_citn']) * 100 : 0;
    $stats['citn_coverage_rate'] = $stats['total_practitioners'] > 0 ? 
        ($stats['practitioners_with_citn'] / $stats['total_practitioners']) * 100 : 0;
    $stats['pending_rate'] = $stats['practitioners_with_citn'] > 0 ? 
        ($stats['citn_pending'] / $stats['practitioners_with_citn']) * 100 : 0;
    $stats['invalid_rate'] = $stats['practitioners_with_citn'] > 0 ? 
        ($stats['citn_invalid'] / $stats['practitioners_with_citn']) * 100 : 0;
    $stats['expired_rate'] = $stats['practitioners_with_citn'] > 0 ? 
        ($stats['citn_expired'] / $stats['practitioners_with_citn']) * 100 : 0;
    
    // Mock additional metrics (would be calculated from actual data)
    $stats['verification_attempts_today'] = 15;
    $stats['avg_daily_verifications'] = 12.5;
    $stats['citn_coverage_change'] = 2.3;
    $stats['verification_rate_change'] = 1.8;
    $stats['api_success_rate'] = 98.5;
    $stats['avg_response_time'] = 1.2;
    $stats['total_api_calls'] = 1250;
    $stats['failed_api_calls'] = 18;
    
    return $stats;
}

function getCITNVerificationTrends($conn, $start_date, $end_date) {
    // Mock data - would be replaced with actual database queries
    return [
        '2024-01-01' => ['verified' => 10, 'pending' => 5, 'invalid' => 2],
        '2024-01-02' => ['verified' => 12, 'pending' => 3, 'invalid' => 1],
        '2024-01-03' => ['verified' => 8, 'pending' => 7, 'invalid' => 3],
        '2024-01-04' => ['verified' => 15, 'pending' => 4, 'invalid' => 1],
        '2024-01-05' => ['verified' => 11, 'pending' => 6, 'invalid' => 2]
    ];
}

function getCITNMembershipDistribution($conn) {
    $sql = "SELECT citn_membership_type, COUNT(*) as count 
            FROM tax_practitioners 
            WHERE citn_verification_status = 'verified' 
            AND citn_membership_type IS NOT NULL 
            GROUP BY citn_membership_type";
    
    $result = $conn->query($sql);
    $distribution = [];
    
    while ($row = $result->fetch_assoc()) {
        $distribution[$row['citn_membership_type']] = (int)$row['count'];
    }
    
    // Add mock data if empty
    if (empty($distribution)) {
        $distribution = [
            'Associate Member' => 45,
            'Fellow' => 23,
            'Student Member' => 12,
            'Corporate Member' => 8
        ];
    }
    
    return $distribution;
}

function getMonthlyVerificationSuccessRate($conn) {
    // Mock data - would be replaced with actual database queries
    return [
        'Jan 2024' => 95.2,
        'Feb 2024' => 97.1,
        'Mar 2024' => 94.8,
        'Apr 2024' => 98.3,
        'May 2024' => 96.7,
        'Jun 2024' => 99.1
    ];
}

// Export functions
function exportVerificationSummary($conn, $format) {
    // Implementation for exporting verification summary
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="citn_verification_summary.csv"');
    // Export logic here
}

function exportVerificationLogs($conn, $format) {
    // Implementation for exporting verification logs
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="citn_verification_logs.csv"');
    // Export logic here
}

function exportPractitionersWithCITN($conn, $format) {
    // Implementation for exporting practitioners with CITN
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="practitioners_with_citn.csv"');
    // Export logic here
}

include '../includes/admin_footer.php';
?>
