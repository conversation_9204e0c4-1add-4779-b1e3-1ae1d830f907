-- First, add the password column to the tax_firms table if it doesn't exist
ALTER TABLE tax_firms ADD COLUMN IF NOT EXISTS password VARCHAR(255) NOT NULL;

-- Then, add the business_type column if it doesn't exist
ALTER TABLE tax_firms ADD COLUMN IF NOT EXISTS business_type VARCHAR(100);

-- Add the tax_id column if it doesn't exist
ALTER TABLE tax_firms ADD COLUMN IF NOT EXISTS tax_id VARCHAR(50);

-- Now insert the admin firm
INSERT INTO tax_firms (
    name, 
    email, 
    phone, 
    address, 
    password, 
    registration_number, 
    tax_id, 
    business_type, 
    registration_status
) VALUES (
    'Admin Tax Firm',
    '<EMAIL>',
    '1234567890',
    'Admin Headquarters, 123 Main St',
    '$2y$10$nOW7Fz7/K7KRIiddtFagLuKa2H3cEcY0WwLFVdOfR5q73fB2FNfam',
    'TF2023ADMIN',
    'ADMIN-TAX-ID-001',
    'Government',
    'Active'
);
