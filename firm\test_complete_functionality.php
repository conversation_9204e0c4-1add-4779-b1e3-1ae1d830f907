<?php
session_start();
require_once '../config/db_connect.php';

// Set up test session if not logged in
if (!isset($_SESSION['firm_id'])) {
    $_SESSION['firm_id'] = 1;
    $_SESSION['firm_name'] = "Test Tax Consulting Firm";
    $_SESSION['firm_email'] = "<EMAIL>";
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✓ Test session created automatically";
    echo "</div>";
}

echo "<h1>Supporting Documents - Complete Functionality Test</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
if ($conn->connect_error) {
    echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✗ Database connection failed: " . $conn->connect_error;
    echo "</div>";
    exit;
} else {
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✓ Database connected successfully";
    echo "</div>";
}

// Test 2: Check firm_documents table
echo "<h2>2. Database Table Test</h2>";
$result = $conn->query("SHOW TABLES LIKE 'firm_documents'");
if ($result->num_rows > 0) {
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✓ firm_documents table exists";
    echo "</div>";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE firm_documents");
    echo "<details><summary>Table Structure</summary>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Key</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr><td>" . $row['Field'] . "</td><td>" . $row['Type'] . "</td><td>" . $row['Key'] . "</td></tr>";
    }
    echo "</table></details>";
} else {
    echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✗ firm_documents table does not exist - Creating it...";
    echo "</div>";
    
    $sql = "CREATE TABLE IF NOT EXISTS firm_documents (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firm_id INT NOT NULL,
        document_type VARCHAR(50) NOT NULL,
        original_filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        admin_notes TEXT NULL,
        reviewed_by INT NULL,
        reviewed_at TIMESTAMP NULL,
        UNIQUE KEY unique_firm_document (firm_id, document_type)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "✓ Table created successfully";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "✗ Error creating table: " . $conn->error;
        echo "</div>";
    }
}

// Test 3: Check uploads directory
echo "<h2>3. File System Test</h2>";
$upload_dir = '../uploads/firm_documents/';
if (!file_exists($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "✓ Created uploads directory: $upload_dir";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "✗ Failed to create uploads directory";
        echo "</div>";
    }
} else {
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✓ Uploads directory exists";
    echo "</div>";
}

if (is_writable($upload_dir)) {
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✓ Uploads directory is writable";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✗ Uploads directory is not writable";
    echo "</div>";
}

// Test 4: Check key files
echo "<h2>4. File Existence Test</h2>";
$files = [
    'supporting_documents.php' => 'Main supporting documents page',
    'upload_document.php' => 'File upload handler',
    'view_document.php' => 'Secure file viewer'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "✓ $file exists ($description)";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "✗ $file missing ($description)";
        echo "</div>";
    }
}

// Test 5: Session Test
echo "<h2>5. Session Test</h2>";
echo "<div style='background: #e2e3e5; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
echo "<strong>Current Session:</strong><br>";
echo "Firm ID: " . ($_SESSION['firm_id'] ?? 'Not set') . "<br>";
echo "Firm Name: " . ($_SESSION['firm_name'] ?? 'Not set') . "<br>";
echo "Firm Email: " . ($_SESSION['firm_email'] ?? 'Not set');
echo "</div>";

// Test 6: Check existing documents
echo "<h2>6. Existing Documents Test</h2>";
$firm_id = $_SESSION['firm_id'];
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ?";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<div style='background: #d1ecf1; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>Found " . $docs_result->num_rows . " uploaded documents:</strong><br>";
    while ($doc = $docs_result->fetch_assoc()) {
        echo "- " . $doc['document_type'] . ": " . $doc['original_filename'] . " (uploaded: " . $doc['uploaded_at'] . ")<br>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "No documents uploaded yet";
    echo "</div>";
}
$docs_stmt->close();

// Navigation Links
echo "<h2>7. Test Navigation</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>Available Pages:</h3>";
echo "<p><a href='dashboard.php' style='color: #007bff; text-decoration: none; margin-right: 20px;'>📊 Dashboard</a></p>";
echo "<p><a href='supporting_documents.php' style='color: #28a745; text-decoration: none; margin-right: 20px;'>📁 Supporting Documents</a></p>";
echo "<p><a href='test_supporting_docs.php' style='color: #6c757d; text-decoration: none; margin-right: 20px;'>🔧 Debug Page</a></p>";
echo "</div>";

// Final Status
echo "<h2>8. Overall Status</h2>";
$all_good = $conn && file_exists('supporting_documents.php') && file_exists('upload_document.php') && is_writable($upload_dir);

if ($all_good) {
    echo "<div style='background: #d4edda; padding: 20px; margin: 10px 0; border-radius: 5px; text-align: center;'>";
    echo "<h3 style='color: #155724; margin: 0;'>🎉 ALL SYSTEMS READY!</h3>";
    echo "<p style='margin: 10px 0;'>Supporting Documents functionality should work perfectly.</p>";
    echo "<a href='supporting_documents.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>Go to Supporting Documents</a>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; margin: 10px 0; border-radius: 5px; text-align: center;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>⚠️ ISSUES DETECTED</h3>";
    echo "<p style='margin: 10px 0;'>Please fix the issues above before using Supporting Documents.</p>";
    echo "</div>";
}

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2 { color: #333; }
details { margin: 10px 0; }
table { width: 100%; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
