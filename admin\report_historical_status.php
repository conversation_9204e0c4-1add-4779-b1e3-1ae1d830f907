<?php
require_once '../includes/config.php';
require_once '../includes/auth_check.php';
require_once '../includes/functions.php';

// Check if user has appropriate permissions
if (!has_permission('view_reports')) {
    $_SESSION['alert'] = [
        'type' => 'danger',
        'message' => 'You do not have permission to access the reporting system.'
    ];
    header('Location: dashboard.php');
    exit;
}

// Process filter parameters
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$status = isset($_GET['status']) ? $_GET['status'] : '';
$export_format = isset($_GET['export']) ? $_GET['export'] : '';

// Get historical data
$historical_data = get_historical_status_data($start_date, $end_date, $status);

// Handle export if requested
if ($export_format) {
    export_report_data($historical_data, 'historical_status_report', $export_format);
    exit;
}

// Page title
$page_title = "Historical Status Report";
include '../includes/admin_header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><i class="fas fa-history me-2"></i><?php echo $page_title; ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="reports_dashboard.php">Reports</a></li>
        <li class="breadcrumb-item active">Historical Status</li>
    </ol>

    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Options
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="" <?php echo $status == '' ? 'selected' : ''; ?>>All Statuses</option>
                        <option value="Pending" <?php echo $status == 'Pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="Under_Review" <?php echo $status == 'Under_Review' ? 'selected' : ''; ?>>Under Review</option>
                        <option value="Info_Required" <?php echo $status == 'Info_Required' ? 'selected' : ''; ?>>Info Required</option>
                        <option value="Approved" <?php echo $status == 'Approved' ? 'selected' : ''; ?>>Approved</option>
                        <option value="Rejected" <?php echo $status == 'Rejected' ? 'selected' : ''; ?>>Rejected</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">Apply Filters</button>
                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">Reset</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Export Options -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-download me-1"></i>
            Export Options
        </div>
        <div class="card-body">
            <div class="btn-group" role="group">
                <a href="<?php echo $_SERVER['REQUEST_URI'] . (strpos($_SERVER['REQUEST_URI'], '?') ? '&' : '?') . 'export=pdf'; ?>" class="btn btn-danger">
                    <i class="fas fa-file-pdf me-1"></i> Export as PDF
                </a>
                <a href="<?php echo $_SERVER['REQUEST_URI'] . (strpos($_SERVER['REQUEST_URI'], '?') ? '&' : '?') . 'export=excel'; ?>" class="btn btn-success">
                    <i class="fas fa-file-excel me-1"></i> Export as Excel
                </a>
                <a href="<?php echo $_SERVER['REQUEST_URI'] . (strpos($_SERVER['REQUEST_URI'], '?') ? '&' : '?') . 'export=csv'; ?>" class="btn btn-info">
                    <i class="fas fa-file-csv me-1"></i> Export as CSV
                </a>
            </div>
        </div>
    </div>

    <!-- Report Data -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Historical Status Data (<?php echo date('M d, Y', strtotime($start_date)); ?> - <?php echo date('M d, Y', strtotime($end_date)); ?>)
        </div>
        <div class="card-body">
            <table id="historicalDataTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Application ID</th>
                        <th>Applicant Name</th>
                        <th>Previous Status</th>
                        <th>New Status</th>
                        <th>Changed By</th>
                        <th>Change Date</th>
                        <th>Reason</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($historical_data as $record): ?>
                    <tr>
                        <td><?php echo $record['application_id']; ?></td>
                        <td><?php echo get_applicant_name($record['application_id']); ?></td>
                        <td><span class="badge bg-secondary"><?php echo $record['previous_status']; ?></span></td>
                        <td><span class="badge bg-<?php echo get_status_color($record['new_status']); ?>"><?php echo $record['new_status']; ?></span></td>
                        <td><?php echo get_admin_name($record['changed_by']); ?></td>
                        <td><?php echo date('M d, Y H:i', strtotime($record['change_date'])); ?></td>
                        <td><?php echo !empty($record['reason']) ? substr($record['reason'], 0, 50) . (strlen($record['reason']) > 50 ? '...' : '') : ''; ?></td>
                        <td>
                            <a href="review_application.php?id=<?php echo $record['application_id']; ?>" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye"></i> View
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    // Initialize DataTable
    $(document).ready(function() {
        $('#historicalDataTable').DataTable({
            order: [[5, 'desc']], // Sort by change date descending
            pageLength: 25,
            responsive: true
        });
    });

    // Reset filters function
    function resetFilters() {
        document.getElementById('start_date').value = '<?php echo date('Y-m-d', strtotime('-30 days')); ?>';
        document.getElementById('end_date').value = '<?php echo date('Y-m-d'); ?>';
        document.getElementById('status').value = '';
        document.querySelector('form').submit();
    }
</script>

<?php include '../includes/admin_footer.php'; ?>