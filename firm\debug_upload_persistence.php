<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Upload Persistence Debug</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Check what happens during upload process
echo "<h3>1. Current Database State</h3>";

$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY upload_date DESC";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Document Type</th><th>File Name</th><th>File Path</th><th>Status</th><th>Upload Date</th><th>File Exists</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        $file_color = $file_exists ? 'green' : 'red';
        $file_status = $file_exists ? '✅ Yes' : '❌ No';
        
        echo "<tr>";
        echo "<td>" . $doc['id'] . "</td>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_path']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td style='color: $file_color;'>$file_status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>No documents found in database</p>";
}

// Check upload directory
echo "<h3>2. File System Check</h3>";
$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';

if (file_exists($upload_dir)) {
    echo "<p style='color: green;'>✅ Upload directory exists: $upload_dir</p>";
    
    $files = scandir($upload_dir);
    $files = array_diff($files, array('.', '..'));
    
    if (count($files) > 0) {
        echo "<h4>Files in directory:</h4>";
        echo "<ul>";
        foreach ($files as $file) {
            $file_path = $upload_dir . $file;
            $file_size = filesize($file_path);
            $file_time = date('Y-m-d H:i:s', filemtime($file_path));
            echo "<li><strong>$file</strong> (" . number_format($file_size) . " bytes, modified: $file_time)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Directory is empty</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Upload directory does not exist</p>";
}

// Test the dashboard query
echo "<h3>3. Dashboard Query Test</h3>";
$dashboard_sql = "SELECT document_type, file_name, status, upload_date FROM firm_documents WHERE firm_id = ?";
$dashboard_stmt = $conn->prepare($dashboard_sql);
$dashboard_stmt->bind_param("i", $firm_id);
$dashboard_stmt->execute();
$dashboard_result = $dashboard_stmt->get_result();

$document_statuses = [];
while ($doc = $dashboard_result->fetch_assoc()) {
    $document_statuses[$doc['document_type']] = $doc;
}

echo "<p><strong>Documents found by dashboard query:</strong> " . count($document_statuses) . "</p>";

if (!empty($document_statuses)) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Document Type</th><th>Status</th><th>Upload Date</th></tr>";
    
    foreach ($document_statuses as $type => $doc) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($type) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test the getDocumentStatus function logic
echo "<h3>4. Document Status Function Test</h3>";

function testGetDocumentStatus($type, $statuses) {
    if (isset($statuses[$type])) {
        $doc = $statuses[$type];
        $file_exists = file_exists($doc['file_path'] ?? '');
        return [
            'status' => $doc['status'],
            'file_name' => $doc['file_name'],
            'upload_date' => $doc['upload_date'] ? date('M j, Y', strtotime($doc['upload_date'])) : null,
            'has_file' => ($doc['status'] === 'uploaded' && $file_exists),
            'file_exists' => $file_exists,
            'file_path' => $doc['file_path'] ?? null
        ];
    }
    return [
        'status' => 'pending',
        'file_name' => null,
        'upload_date' => null,
        'has_file' => false,
        'file_exists' => false,
        'file_path' => null
    ];
}

$test_types = ['memart', 'cac_status', 'tax_clearance', 'utility_bill', 'incorporation_cert'];

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Document Type</th><th>Status</th><th>Has File</th><th>File Exists</th><th>Upload Date</th></tr>";

foreach ($test_types as $type) {
    $status = testGetDocumentStatus($type, $document_statuses);
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($type) . "</td>";
    echo "<td>" . htmlspecialchars($status['status']) . "</td>";
    echo "<td style='color: " . ($status['has_file'] ? 'green' : 'red') . ";'>" . ($status['has_file'] ? 'Yes' : 'No') . "</td>";
    echo "<td style='color: " . ($status['file_exists'] ? 'green' : 'red') . ";'>" . ($status['file_exists'] ? 'Yes' : 'No') . "</td>";
    echo "<td>" . htmlspecialchars($status['upload_date'] ?? 'N/A') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check for database transaction issues
echo "<h3>5. Database Transaction Test</h3>";

// Test inserting a dummy record
$test_type = 'test_document_' . time();
$test_filename = 'test_file.pdf';
$test_path = $upload_dir . $test_filename;
$test_size = 1024;

// Create a test file
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}
file_put_contents($test_path, 'Test content');

// Insert test record
$test_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status) 
             VALUES (?, ?, ?, ?, ?, 'uploaded')";
$test_stmt = $conn->prepare($test_sql);
$test_stmt->bind_param("isssi", $firm_id, $test_type, $test_filename, $test_path, $test_size);

if ($test_stmt->execute()) {
    $test_id = $conn->insert_id;
    echo "<p style='color: green;'>✅ Test record inserted successfully (ID: $test_id)</p>";
    
    // Try to retrieve it
    $retrieve_sql = "SELECT * FROM firm_documents WHERE id = ?";
    $retrieve_stmt = $conn->prepare($retrieve_sql);
    $retrieve_stmt->bind_param("i", $test_id);
    $retrieve_stmt->execute();
    $retrieve_result = $retrieve_stmt->get_result();
    
    if ($retrieve_result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Test record retrieved successfully</p>";
        
        // Clean up test record
        $cleanup_sql = "DELETE FROM firm_documents WHERE id = ?";
        $cleanup_stmt = $conn->prepare($cleanup_sql);
        $cleanup_stmt->bind_param("i", $test_id);
        $cleanup_stmt->execute();
        
        unlink($test_path);
        echo "<p style='color: blue;'>🧹 Test record cleaned up</p>";
    } else {
        echo "<p style='color: red;'>❌ Test record could not be retrieved</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Failed to insert test record: " . $test_stmt->error . "</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h3 style="color: #856404;">🔍 Debugging Complete</h3>
    <p style="color: #856404;">This page shows the current state of your document system. If uploads are disappearing after refresh, check:</p>
    <ul style="color: #856404;">
        <li>Are files being saved to the correct directory?</li>
        <li>Are database records being created with correct status?</li>
        <li>Is the dashboard query finding the uploaded documents?</li>
        <li>Are there any file path mismatches?</li>
    </ul>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="test_upload.html" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Test Upload</a>
    </p>
</div>
