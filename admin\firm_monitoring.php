<?php
session_start();
// Check if logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit;
}
require_once '../includes/config.php';
require_once '../includes/functions.php';
$page_title = "Firm Activity Monitoring";

// Get filter parameters
$selected_firm = $_GET['firm_id'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$activity_type = $_GET['activity_type'] ?? '';

// Get all firms for dropdown
$firms_sql = "SELECT id, name FROM tax_firms ORDER BY name";
$firms_result = $conn->query($firms_sql);

include '../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-eye me-2"></i>Firm Activity Monitoring</h2>
        <div>
            <a href="dashboard.php" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
            <a href="logout.php" class="btn btn-outline-danger">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="firm_id" class="form-label">Select Firm</label>
                    <select class="form-select" id="firm_id" name="firm_id">
                        <option value="">All Firms</option>
                        <?php while ($firm = $firms_result->fetch_assoc()): ?>
                            <option value="<?php echo $firm['id']; ?>" <?php echo $selected_firm == $firm['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($firm['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                </div>
                <div class="col-md-3">
                    <label for="activity_type" class="form-label">Activity Type</label>
                    <select class="form-select" id="activity_type" name="activity_type">
                        <option value="">All Activities</option>
                        <option value="documents" <?php echo $activity_type == 'documents' ? 'selected' : ''; ?>>Document Uploads</option>
                        <option value="shareholders" <?php echo $activity_type == 'shareholders' ? 'selected' : ''; ?>>Shareholder Changes</option>
                        <option value="directors" <?php echo $activity_type == 'directors' ? 'selected' : ''; ?>>Director Changes</option>
                        <option value="secretaries" <?php echo $activity_type == 'secretaries' ? 'selected' : ''; ?>>Secretary Changes</option>
                        <option value="tax_clearance" <?php echo $activity_type == 'tax_clearance' ? 'selected' : ''; ?>>Tax Clearance Records</option>
                        <option value="profile" <?php echo $activity_type == 'profile' ? 'selected' : ''; ?>>Profile Updates</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block w-100">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Activity Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-file-upload fa-2x mb-2"></i>
                    <h4>
                        <?php
                        $doc_sql = "SELECT COUNT(*) as count FROM firm_documents WHERE 1=1";
                        if ($selected_firm) $doc_sql .= " AND firm_id = $selected_firm";
                        if ($date_from && $date_to) $doc_sql .= " AND DATE(upload_date) BETWEEN '$date_from' AND '$date_to'";
                        $doc_result = $conn->query($doc_sql);
                        echo $doc_result ? $doc_result->fetch_assoc()['count'] : 0;
                        ?>
                    </h4>
                    <p class="mb-0">Documents</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4>
                        <?php
                        $sh_sql = "SELECT COUNT(*) as count FROM firm_shareholders WHERE 1=1";
                        if ($selected_firm) $sh_sql .= " AND firm_id = $selected_firm";
                        if ($date_from && $date_to) $sh_sql .= " AND DATE(created_at) BETWEEN '$date_from' AND '$date_to'";
                        $sh_result = $conn->query($sh_sql);
                        echo $sh_result ? $sh_result->fetch_assoc()['count'] : 0;
                        ?>
                    </h4>
                    <p class="mb-0">Shareholders</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-tie fa-2x mb-2"></i>
                    <h4>
                        <?php
                        $dir_sql = "SELECT COUNT(*) as count FROM firm_directors WHERE 1=1";
                        if ($selected_firm) $dir_sql .= " AND firm_id = $selected_firm";
                        if ($date_from && $date_to) $dir_sql .= " AND DATE(created_at) BETWEEN '$date_from' AND '$date_to'";
                        $dir_result = $conn->query($dir_sql);
                        echo $dir_result ? $dir_result->fetch_assoc()['count'] : 0;
                        ?>
                    </h4>
                    <p class="mb-0">Directors</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-graduate fa-2x mb-2"></i>
                    <h4>
                        <?php
                        $sec_sql = "SELECT COUNT(*) as count FROM firm_secretaries WHERE 1=1";
                        if ($selected_firm) $sec_sql .= " AND firm_id = $selected_firm";
                        if ($date_from && $date_to) $sec_sql .= " AND DATE(created_at) BETWEEN '$date_from' AND '$date_to'";
                        $sec_result = $conn->query($sec_sql);
                        echo $sec_result ? $sec_result->fetch_assoc()['count'] : 0;
                        ?>
                    </h4>
                    <p class="mb-0">Secretaries</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <i class="fas fa-certificate fa-2x mb-2"></i>
                    <h4>
                        <?php
                        $tax_sql = "SELECT COUNT(*) as count FROM firm_tax_clearance WHERE 1=1";
                        if ($selected_firm) $tax_sql .= " AND firm_id = $selected_firm";
                        if ($date_from && $date_to) $tax_sql .= " AND DATE(created_at) BETWEEN '$date_from' AND '$date_to'";
                        $tax_result = $conn->query($tax_sql);
                        echo $tax_result ? $tax_result->fetch_assoc()['count'] : 0;
                        ?>
                    </h4>
                    <p class="mb-0">Tax Records</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-building fa-2x mb-2"></i>
                    <h4>
                        <?php
                        $firm_sql = "SELECT COUNT(*) as count FROM tax_firms WHERE 1=1";
                        if ($selected_firm) $firm_sql .= " AND id = $selected_firm";
                        if ($date_from && $date_to) $firm_sql .= " AND DATE(registration_date) BETWEEN '$date_from' AND '$date_to'";
                        $firm_result = $conn->query($firm_sql);
                        echo $firm_result ? $firm_result->fetch_assoc()['count'] : 0;
                        ?>
                    </h4>
                    <p class="mb-0">Firms</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Timeline -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity Timeline</h5>
        </div>
        <div class="card-body">
            <div class="timeline">
                <?php
                // Build comprehensive activity query
                $activities = [];
                
                // Document uploads
                if (!$activity_type || $activity_type == 'documents') {
                    $doc_query = "SELECT 'document' as type, f.name as firm_name, f.id as firm_id, 
                                 d.document_type, d.file_name, d.upload_date as activity_date, 
                                 'Document uploaded' as action, d.status
                                 FROM firm_documents d 
                                 JOIN tax_firms f ON d.firm_id = f.id 
                                 WHERE 1=1";
                    if ($selected_firm) $doc_query .= " AND d.firm_id = $selected_firm";
                    if ($date_from && $date_to) $doc_query .= " AND DATE(d.upload_date) BETWEEN '$date_from' AND '$date_to'";
                    
                    $doc_result = $conn->query($doc_query);
                    if ($doc_result) {
                        while ($row = $doc_result->fetch_assoc()) {
                            $activities[] = $row;
                        }
                    }
                }
                
                // Shareholder changes
                if (!$activity_type || $activity_type == 'shareholders') {
                    $sh_query = "SELECT 'shareholder' as type, f.name as firm_name, f.id as firm_id,
                                s.name as person_name, s.created_at as activity_date,
                                'Shareholder added' as action, s.status, s.share_percentage
                                FROM firm_shareholders s
                                JOIN tax_firms f ON s.firm_id = f.id
                                WHERE 1=1";
                    if ($selected_firm) $sh_query .= " AND s.firm_id = $selected_firm";
                    if ($date_from && $date_to) $sh_query .= " AND DATE(s.created_at) BETWEEN '$date_from' AND '$date_to'";
                    
                    $sh_result = $conn->query($sh_query);
                    if ($sh_result) {
                        while ($row = $sh_result->fetch_assoc()) {
                            $activities[] = $row;
                        }
                    }
                }
                
                // Director changes
                if (!$activity_type || $activity_type == 'directors') {
                    $dir_query = "SELECT 'director' as type, f.name as firm_name, f.id as firm_id,
                                 d.name as person_name, d.created_at as activity_date,
                                 'Director added' as action, d.status, d.position
                                 FROM firm_directors d
                                 JOIN tax_firms f ON d.firm_id = f.id
                                 WHERE 1=1";
                    if ($selected_firm) $dir_query .= " AND d.firm_id = $selected_firm";
                    if ($date_from && $date_to) $dir_query .= " AND DATE(d.created_at) BETWEEN '$date_from' AND '$date_to'";
                    
                    $dir_result = $conn->query($dir_query);
                    if ($dir_result) {
                        while ($row = $dir_result->fetch_assoc()) {
                            $activities[] = $row;
                        }
                    }
                }
                
                // Tax clearance records
                if (!$activity_type || $activity_type == 'tax_clearance') {
                    $tax_query = "SELECT 'tax_clearance' as type, f.name as firm_name, f.id as firm_id,
                                 t.certificate_number, t.created_at as activity_date,
                                 'Tax clearance record added' as action, t.status, t.tax_year
                                 FROM firm_tax_clearance t
                                 JOIN tax_firms f ON t.firm_id = f.id
                                 WHERE 1=1";
                    if ($selected_firm) $tax_query .= " AND t.firm_id = $selected_firm";
                    if ($date_from && $date_to) $tax_query .= " AND DATE(t.created_at) BETWEEN '$date_from' AND '$date_to'";
                    
                    $tax_result = $conn->query($tax_query);
                    if ($tax_result) {
                        while ($row = $tax_result->fetch_assoc()) {
                            $activities[] = $row;
                        }
                    }
                }
                
                // Sort activities by date (newest first)
                usort($activities, function($a, $b) {
                    return strtotime($b['activity_date']) - strtotime($a['activity_date']);
                });
                
                // Display activities
                if (empty($activities)) {
                    echo '<div class="text-center text-muted py-4">';
                    echo '<i class="fas fa-inbox fa-3x mb-3"></i>';
                    echo '<p>No activities found for the selected criteria.</p>';
                    echo '</div>';
                } else {
                    foreach (array_slice($activities, 0, 50) as $activity) {
                        $icon_class = '';
                        $badge_class = '';
                        
                        switch ($activity['type']) {
                            case 'document':
                                $icon_class = 'fas fa-file-upload text-primary';
                                $badge_class = 'bg-primary';
                                break;
                            case 'shareholder':
                                $icon_class = 'fas fa-users text-success';
                                $badge_class = 'bg-success';
                                break;
                            case 'director':
                                $icon_class = 'fas fa-user-tie text-info';
                                $badge_class = 'bg-info';
                                break;
                            case 'tax_clearance':
                                $icon_class = 'fas fa-certificate text-warning';
                                $badge_class = 'bg-warning';
                                break;
                        }
                        
                        echo '<div class="timeline-item mb-3">';
                        echo '<div class="d-flex">';
                        echo '<div class="timeline-marker me-3">';
                        echo '<i class="' . $icon_class . '"></i>';
                        echo '</div>';
                        echo '<div class="timeline-content flex-grow-1">';
                        echo '<div class="d-flex justify-content-between align-items-start">';
                        echo '<div>';
                        echo '<h6 class="mb-1">' . htmlspecialchars($activity['action']) . '</h6>';
                        echo '<p class="mb-1 text-muted">';
                        echo '<strong>Firm:</strong> ' . htmlspecialchars($activity['firm_name']);
                        if (isset($activity['person_name'])) {
                            echo ' | <strong>Person:</strong> ' . htmlspecialchars($activity['person_name']);
                        }
                        if (isset($activity['document_type'])) {
                            echo ' | <strong>Document:</strong> ' . htmlspecialchars($activity['document_type']);
                        }
                        if (isset($activity['certificate_number'])) {
                            echo ' | <strong>Certificate:</strong> ' . htmlspecialchars($activity['certificate_number']);
                        }
                        echo '</p>';
                        echo '<small class="text-muted">' . date('M j, Y g:i A', strtotime($activity['activity_date'])) . '</small>';
                        echo '</div>';
                        echo '<div>';
                        if (isset($activity['status'])) {
                            echo '<span class="badge ' . $badge_class . '">' . htmlspecialchars($activity['status']) . '</span>';
                        }
                        echo '<a href="view_firm.php?id=' . $activity['firm_id'] . '" class="btn btn-sm btn-outline-primary ms-2">';
                        echo '<i class="fas fa-eye"></i>';
                        echo '</a>';
                        echo '</div>';
                        echo '</div>';
                        echo '</div>';
                        echo '</div>';
                        echo '</div>';
                    }
                    
                    if (count($activities) > 50) {
                        echo '<div class="text-center mt-3">';
                        echo '<p class="text-muted">Showing latest 50 activities. Use filters to narrow down results.</p>';
                        echo '</div>';
                    }
                }
                ?>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-item {
    border-left: 2px solid #e9ecef;
    padding-left: 1rem;
    position: relative;
}

.timeline-marker {
    position: absolute;
    left: -0.6rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
}
</style>

<?php include '../includes/footer.php'; ?>
