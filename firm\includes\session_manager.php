<?php
/**
 * Session Manager for Firm Portal
 * Handles session security, timeout, and logout functionality
 */

class FirmSessionManager {
    
    private $timeout_duration = 86400; // 24 hours in seconds (effectively disabled)
    private $conn;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->initializeSession();
    }
    
    /**
     * Initialize session with security settings
     */
    private function initializeSession() {
        // Set secure session parameters
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
        
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        // Regenerate session ID periodically for security
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
    
    /**
     * Check if firm is logged in and session is valid
     */
    public function isLoggedIn() {
        return isset($_SESSION['firm_id']) && 
               isset($_SESSION['firm_email']) && 
               $this->isSessionValid();
    }
    
    /**
     * Validate session timeout and activity
     */
    private function isSessionValid() {
        // Check session timeout
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > $this->timeout_duration) {
                $this->logout('Session expired due to inactivity');
                return false;
            }
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        // Validate session against database
        if (isset($_SESSION['firm_id'])) {
            return $this->validateFirmSession($_SESSION['firm_id']);
        }
        
        return false;
    }
    
    /**
     * Validate firm session against database
     */
    private function validateFirmSession($firm_id) {
        $sql = "SELECT id, registration_status FROM tax_firms WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        
        if ($stmt) {
            $stmt->bind_param("i", $firm_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 1) {
                $firm = $result->fetch_assoc();
                
                // Check if firm is still active
                if ($firm['registration_status'] !== 'Active') {
                    $this->logout('Account status changed to: ' . $firm['registration_status']);
                    return false;
                }
                
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Login firm and create session
     */
    public function login($firm_data) {
        // Regenerate session ID for security
        session_regenerate_id(true);
        
        // Set session variables
        $_SESSION['firm_id'] = $firm_data['id'];
        $_SESSION['firm_name'] = $firm_data['name'];
        $_SESSION['firm_email'] = $firm_data['email'];
        $_SESSION['firm_registration_number'] = $firm_data['registration_number'];
        $_SESSION['firm_registration_status'] = $firm_data['registration_status'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
        
        // Log login activity
        $this->logActivity($firm_data['id'], 'login', 'Firm logged in successfully');
        
        return true;
    }
    
    /**
     * Logout firm and destroy session
     */
    public function logout($reason = 'User logout') {
        $firm_id = $_SESSION['firm_id'] ?? null;
        $firm_name = $_SESSION['firm_name'] ?? 'Unknown';
        
        // Log logout activity
        if ($firm_id) {
            $this->logActivity($firm_id, 'logout', $reason);
        }
        
        // Clear all session variables
        $_SESSION = array();
        
        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        session_destroy();
        
        // Start new session for logout message
        session_start();
        $_SESSION['logout_message'] = "You have been successfully logged out.";
        
        return true;
    }
    
    /**
     * Check for session timeout and redirect if necessary
     */
    public function checkTimeout() {
        if (!$this->isLoggedIn()) {
            header("Location: login.php?timeout=1");
            exit();
        }
    }
    
    /**
     * Get remaining session time
     */
    public function getRemainingTime() {
        if (isset($_SESSION['last_activity'])) {
            $elapsed = time() - $_SESSION['last_activity'];
            return max(0, $this->timeout_duration - $elapsed);
        }
        return 0;
    }
    
    /**
     * Extend session (refresh activity)
     */
    public function extendSession() {
        if ($this->isLoggedIn()) {
            $_SESSION['last_activity'] = time();
            return true;
        }
        return false;
    }
    
    /**
     * Log session activity
     */
    private function logActivity($firm_id, $action, $description) {
        // Create activity log table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS firm_activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            firm_id INT NOT NULL,
            action VARCHAR(50) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            session_id VARCHAR(128),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_firm_id (firm_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        )";
        
        $this->conn->query($create_table_sql);
        
        // Insert activity log
        $log_sql = "INSERT INTO firm_activity_log (firm_id, action, description, ip_address, user_agent, session_id) 
                    VALUES (?, ?, ?, ?, ?, ?)";
        $log_stmt = $this->conn->prepare($log_sql);
        
        if ($log_stmt) {
            $ip = $_SERVER['REMOTE_ADDR'] ?? '';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $session_id = session_id();
            
            $log_stmt->bind_param("isssss", $firm_id, $action, $description, $ip, $user_agent, $session_id);
            $log_stmt->execute();
        }
    }
    
    /**
     * Get session information for display
     */
    public function getSessionInfo() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'firm_id' => $_SESSION['firm_id'],
            'firm_name' => $_SESSION['firm_name'],
            'firm_email' => $_SESSION['firm_email'],
            'login_time' => $_SESSION['login_time'] ?? null,
            'last_activity' => $_SESSION['last_activity'] ?? null,
            'remaining_time' => $this->getRemainingTime(),
            'session_duration' => isset($_SESSION['login_time']) ? time() - $_SESSION['login_time'] : 0
        ];
    }
    
    /**
     * Force logout all sessions for a firm (admin function)
     */
    public function forceLogoutFirm($firm_id, $reason = 'Administrative action') {
        // This would require a more complex session storage system
        // For now, we'll just log the action
        $this->logActivity($firm_id, 'force_logout', $reason);
        
        // In a production system, you might store active sessions in database
        // and invalidate them here
        return true;
    }
    
    /**
     * Get active sessions count for a firm
     */
    public function getActiveSessionsCount($firm_id) {
        // This is a simplified version - in production you'd track active sessions
        $sql = "SELECT COUNT(*) as count FROM firm_activity_log 
                WHERE firm_id = ? AND action = 'login' 
                AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                AND NOT EXISTS (
                    SELECT 1 FROM firm_activity_log fl2 
                    WHERE fl2.firm_id = firm_activity_log.firm_id 
                    AND fl2.action = 'logout' 
                    AND fl2.created_at > firm_activity_log.created_at
                )";
        
        $stmt = $this->conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("i", $firm_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            return $row['count'] ?? 0;
        }
        
        return 0;
    }
}

/**
 * Initialize session manager (to be included in firm pages)
 */
function initFirmSession() {
    global $conn;
    
    if (!isset($GLOBALS['firm_session_manager'])) {
        $GLOBALS['firm_session_manager'] = new FirmSessionManager($conn);
    }
    
    return $GLOBALS['firm_session_manager'];
}

/**
 * Quick function to check if firm is logged in
 */
function isFirmLoggedIn() {
    $session_manager = initFirmSession();
    return $session_manager->isLoggedIn();
}

/**
 * Quick function to require firm login
 */
function requireFirmLogin() {
    if (!isFirmLoggedIn()) {
        header("Location: login.php");
        exit();
    }
}

/**
 * Quick function to logout firm
 */
function logoutFirm($reason = 'User logout') {
    $session_manager = initFirmSession();
    $session_manager->logout($reason);
    header("Location: login.php?logout=success");
    exit();
}
?>
