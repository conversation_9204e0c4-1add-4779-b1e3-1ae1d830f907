<?php
session_start();
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

$firm_id = $_SESSION['firm_id'];

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['document_type'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

$document_type = $input['document_type'];

// Validate document type
$allowed_types = ['memart', 'cac_status', 'tax_clearance', 'utility_bill', 'incorporation_cert'];
if (!in_array($document_type, $allowed_types)) {
    echo json_encode(['success' => false, 'message' => 'Invalid document type']);
    exit;
}

try {
    // Get document information
    $select_sql = "SELECT id, file_name, file_path FROM firm_documents WHERE firm_id = ? AND document_type = ?";
    $select_stmt = $conn->prepare($select_sql);
    $select_stmt->bind_param("is", $firm_id, $document_type);
    $select_stmt->execute();
    $result = $select_stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Document not found']);
        exit;
    }
    
    $document = $result->fetch_assoc();
    $file_path = $document['file_path'];
    $file_name = $document['file_name'];
    $document_id = $document['id'];
    
    // Delete file from filesystem if it exists
    $file_deleted = true;
    if (file_exists($file_path)) {
        $file_deleted = unlink($file_path);
        if (!$file_deleted) {
            error_log("Failed to delete file: $file_path");
        }
    }
    
    // Delete database record
    $delete_sql = "DELETE FROM firm_documents WHERE id = ? AND firm_id = ?";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param("ii", $document_id, $firm_id);
    
    if ($delete_stmt->execute()) {
        // Log successful deletion
        error_log("Document deleted successfully: firm_id=$firm_id, type=$document_type, file=$file_name");
        
        // Get document type display name
        $document_names = [
            'memart' => 'CTC of Memorandum & Articles of Association',
            'cac_status' => 'CAC Company Status Report',
            'tax_clearance' => 'Current Tax Clearance Certificate',
            'utility_bill' => 'Current Utility Bill',
            'incorporation_cert' => 'CAC Certificate of Incorporation'
        ];
        
        $display_name = $document_names[$document_type] ?? 'Document';
        
        echo json_encode([
            'success' => true,
            'message' => "$display_name deleted successfully",
            'document_type' => $document_type,
            'file_deleted' => $file_deleted
        ]);
    } else {
        throw new Exception('Failed to delete document record: ' . $delete_stmt->error);
    }
    
} catch (Exception $e) {
    error_log("Delete document error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while deleting the document: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
