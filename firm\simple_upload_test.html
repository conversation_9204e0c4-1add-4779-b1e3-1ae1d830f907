<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Upload Test</h1>
        <p>This is a direct test of the upload functionality without JavaScript complications.</p>
        
        <form id="uploadForm" enctype="multipart/form-data" method="POST" action="upload_document.php">
            <div class="form-group">
                <label for="document_type">Document Type:</label>
                <select name="document_type" id="document_type" required>
                    <option value="">Select document type</option>
                    <option value="memart">CTC of Memorandum & Articles of Association</option>
                    <option value="cac_status">CAC Company Status Report</option>
                    <option value="tax_clearance">Current Tax Clearance Certificate</option>
                    <option value="utility_bill">Current Utility Bill</option>
                    <option value="incorporation_cert">CAC Certificate of Incorporation</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="document">Select File:</label>
                <input type="file" name="document" id="document" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
                <small style="color: #666;">Supported formats: PDF, JPG, PNG, DOC, DOCX (Max size: 10MB)</small>
            </div>
            
            <button type="submit">Upload Document</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h3>📋 Test Instructions:</h3>
            <ol>
                <li><strong>Select Document Type</strong> - Choose from the dropdown</li>
                <li><strong>Choose File</strong> - Select any file from your computer</li>
                <li><strong>Click Upload</strong> - Submit the form</li>
                <li><strong>Check Result</strong> - See if upload was successful</li>
                <li><strong>Go to Dashboard</strong> - Check if document appears</li>
                <li><strong>Refresh Dashboard</strong> - Verify persistence</li>
            </ol>
            
            <p>
                <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
                <a href="diagnose_upload_issue.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Diagnostic Tool</a>
            </p>
        </div>
    </div>

    <script>
        // Handle form submission with AJAX to show results
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            // Show loading
            resultDiv.className = 'result info';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '⏳ Uploading document...';
            
            fetch('upload_document.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ Upload Successful!</h4>
                        <p><strong>Message:</strong> ${data.message}</p>
                        <p><strong>Document Type:</strong> ${data.document_type}</p>
                        <p><strong>File Name:</strong> ${data.file_name}</p>
                        <p><strong>Upload Date:</strong> ${data.upload_date}</p>
                        <p><a href="dashboard.php" style="background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;">View in Dashboard</a></p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ Upload Failed</h4>
                        <p><strong>Error:</strong> ${data.message}</p>
                    `;
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ Upload Error</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Check the browser console for more details.</p>
                `;
            });
        });
    </script>
</body>
</html>
