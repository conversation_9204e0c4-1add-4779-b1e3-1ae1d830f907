<?php
session_start();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Logout Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { padding: 10px 15px; margin: 5px; text-decoration: none; border-radius: 4px; display: inline-block; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>Logout Functionality Test</h1>
    
    <h2>Current Session Status:</h2>
    <?php if (isset($_SESSION['firm_id'])): ?>
        <div class="status success">
            <strong>✅ LOGGED IN</strong><br>
            Firm ID: <?php echo $_SESSION['firm_id']; ?><br>
            Firm Name: <?php echo $_SESSION['firm_name'] ?? 'Not set'; ?><br>
            Firm Email: <?php echo $_SESSION['firm_email'] ?? 'Not set'; ?>
        </div>
        
        <h3>Test Actions:</h3>
        <a href="logout.php" class="btn btn-danger">Test Direct Logout</a>
        <a href="dashboard.php" class="btn btn-primary">Go to Dashboard</a>
        
    <?php else: ?>
        <div class="status error">
            <strong>❌ NOT LOGGED IN</strong><br>
            No active session found.
        </div>
        
        <h3>Login Options:</h3>
        <a href="login.php" class="btn btn-primary">Go to Login Page</a>
        <a href="quick_login_test.php?test_login=1" class="btn btn-success">Quick Test Login</a>
    <?php endif; ?>
    
    <h2>Session Debug Info:</h2>
    <div class="status info">
        <strong>Session ID:</strong> <?php echo session_id(); ?><br>
        <strong>Session Status:</strong> <?php echo session_status() == PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?><br>
        <strong>All Session Data:</strong><br>
        <pre><?php print_r($_SESSION); ?></pre>
    </div>
    
    <h2>Test Instructions:</h2>
    <ol>
        <li>If not logged in, click "Quick Test Login" to create a test session</li>
        <li>Once logged in, click "Test Direct Logout" to test the logout functionality</li>
        <li>Check if you're properly logged out and redirected</li>
        <li>Try accessing the dashboard after logout to confirm session is destroyed</li>
    </ol>
    
    <h2>Files Status:</h2>
    <div class="status info">
        <strong>logout.php exists:</strong> <?php echo file_exists('logout.php') ? '✅ Yes' : '❌ No'; ?><br>
        <strong>login.php exists:</strong> <?php echo file_exists('login.php') ? '✅ Yes' : '❌ No'; ?><br>
        <strong>dashboard.php exists:</strong> <?php echo file_exists('dashboard.php') ? '✅ Yes' : '❌ No'; ?>
    </div>
    
    <p><a href="<?php echo $_SERVER['PHP_SELF']; ?>">🔄 Refresh This Page</a></p>
</body>
</html>
