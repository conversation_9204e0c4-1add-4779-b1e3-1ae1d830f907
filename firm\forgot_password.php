<?php
require_once '../includes/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Check if password_resets table exists, if not create it
$check_table_sql = "SHOW TABLES LIKE 'password_resets'";
$table_result = $conn->query($check_table_sql);
if ($table_result->num_rows == 0) {
    // Table doesn't exist, create it
    $create_table_sql = "CREATE TABLE password_resets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        token VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        used TINYINT(1) DEFAULT 0,
        user_type ENUM('firm', 'practitioner', 'admin') NOT NULL
    )";
    
    if (!$conn->query($create_table_sql)) {
        die("Error creating password_resets table: " . $conn->error);
    }
}

$message = '';
$error = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = trim($_POST['email']);
    
    // Validate email
    if (empty($email)) {
        $error = "Please enter your email address.";
    } else {
        // Check if email exists
        $sql = "SELECT id, name FROM tax_firms WHERE email = ?";
        $stmt = $conn->prepare($sql);
        
        // Check if prepare was successful
        if ($stmt === false) {
            $error = "Database error: " . $conn->error;
        } else {
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 1) {
                $firm = $result->fetch_assoc();
                
                // Generate reset token
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                
                // Store token in database
                $sql = "INSERT INTO password_resets (email, token, expires_at, user_type) 
                        VALUES (?, ?, ?, 'firm')";
                $stmt = $conn->prepare($sql);
                
                // Check if prepare was successful
                if ($stmt === false) {
                    $error = "Database error: " . $conn->error;
                } else {
                    $stmt->bind_param("sss", $email, $token, $expires);
                    
                    if ($stmt->execute()) {
                        // Send reset email
                        $reset_link = "http://" . $_SERVER['HTTP_HOST'] . 
                                    dirname($_SERVER['PHP_SELF']) . 
                                    "/reset_password.php?token=" . $token;
                        
                        $to = $email;
                        $subject = "Password Reset Request";
                        $message_body = "
                        <html>
                        <head>
                            <title>Password Reset</title>
                        </head>
                        <body>
                            <h2>Password Reset Request</h2>
                            <p>Dear {$firm['name']},</p>
                            <p>We received a request to reset your password. Click the link below to reset your password:</p>
                            <p><a href='$reset_link'>Reset Password</a></p>
                            <p>This link will expire in 1 hour.</p>
                            <p>If you did not request a password reset, please ignore this email.</p>
                            <p>Regards,<br>Tax Registration System</p>
                        </body>
                        </html>
                        ";
                        
                        $headers = "MIME-Version: 1.0" . "\r\n";
                        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
                        $headers .= "From: <EMAIL>" . "\r\n";
                        
                        if (mail($to, $subject, $message_body, $headers)) {
                            $message = "Password reset instructions have been sent to your email.";
                        } else {
                            $error = "Failed to send reset email. Please try again.";
                        }
                    } else {
                        $error = "Error processing your request. Please try again: " . $stmt->error;
                    }
                }
            } else {
                $error = "No account found with that email address.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Tax Registration System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            --success-gradient: linear-gradient(135deg, #10b981 0%, #**********%);
            --accent-gradient: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            --dark-gradient: linear-gradient(135deg, #14532d 0%, #**********%);
            --light-green-gradient: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-soft: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-glow: 0 25px 50px -12px rgba(34, 197, 94, 0.25);
            --green-shadow: 0 10px 20px rgba(34, 197, 94, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .bg-decoration {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .floating-shape {
            position: absolute;
            width: 300px;
            height: 300px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            animation: float 15s infinite ease-in-out;
        }

        .floating-shape:nth-child(1) {
            top: -150px;
            left: -100px;
            width: 500px;
            height: 500px;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            bottom: -200px;
            right: -100px;
            width: 400px;
            height: 400px;
            animation-delay: 5s;
        }

        .floating-shape:nth-child(3) {
            top: 50%;
            left: 50%;
            width: 200px;
            height: 200px;
            animation-delay: 10s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Glass morphism container */
        .glass-container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-soft);
            margin: 2rem 0;
            padding: 3rem;
        }

        .auth-container {
            max-width: 450px;
            margin: 3rem auto;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: var(--shadow-soft);
            background: white;
        }

        .card-header {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .card-header h3 {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .card-body {
            padding: 2rem;
        }

        .form-label {
            font-weight: 500;
            color: #4b5563;
            margin-bottom: 0.5rem;
        }

        .input-group {
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .input-group-text {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-right: none;
            color: #6b7280;
        }

        .form-control {
            border: 1px solid #e5e7eb;
            border-left: none;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
        }

        .form-control:focus {
            box-shadow: none;
            border-color: #e5e7eb;
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 0.5rem;
            box-shadow: var(--green-shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 25px rgba(34, 197, 94, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0);
            box-shadow: 0 5px 15px rgba(34, 197, 94, 0.4);
        }

        .auth-links {
            margin-top: 2rem;
            text-align: center;
        }

        .auth-links a {
            color: #22c55e;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .auth-links a:hover {
            color: #16a34a;
            text-decoration: underline;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 0.8s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .invalid-feedback {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .alert {
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: none;
        }

        .alert-danger {
            background-color: #fee2e2;
            color: #b91c1c;
        }

        .alert-success {
            background-color: #dcfce7;
            color: #166534;
        }
    </style>
</head>
<body>
    <div class="bg-decoration">
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="auth-container">
                    <div class="card-header">
                        <h3>Forgot Password</h3>
                        <p class="mb-0">Enter your email to reset your password</p>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($message)): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i> <?php echo $message; ?>
                        </div>
                        <?php endif; ?>
                        
                        <form id="forgotPasswordForm" method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                            <div class="mb-4">
                                <label for="email" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input 
                                        type="email" 
                                        class="form-control" 
                                        id="email" 
                                        name="email" 
                                        placeholder="Enter your registered email"
                                        value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                                        required
                                    >
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="resetBtn">
                                    <span class="btn-text">
                                        <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                                    </span>
                                    <span class="loading-spinner"></span>
                                </button>
                            </div>
                        </form>
                        
                        <div class="auth-links">
                            <p class="mb-0">
                                <a href="login.php"><i class="fas fa-arrow-left me-2"></i>Back to Login</a>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="../index.php" class="text-white text-decoration-none">
                        <i class="fas fa-arrow-left me-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('forgotPasswordForm');
            const resetBtn = document.getElementById('resetBtn');
            const loadingSpinner = document.querySelector('.loading-spinner');
            const btnText = document.querySelector('.btn-text');
            
            if (form) {
                form.addEventListener('submit', function() {
                    resetBtn.disabled = true;
                    btnText.style.display = 'none';
                    loadingSpinner.style.display = 'block';
                });
            }
        });
    </script>
</body>
</html>


