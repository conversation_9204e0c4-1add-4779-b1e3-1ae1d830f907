<?php
// Nuclear session fix - completely destroy and recreate session
session_start();

echo "<h2>Nuclear Session Fix</h2>";
echo "<p>This will completely destroy the current session and create a new one.</p>";

// Show current session
echo "<h3>Current Session (Before Nuclear Fix):</h3>";
echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
print_r($_SESSION);
echo "</pre>";

// Show session info
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Save Path:</strong> " . session_save_path() . "</p>";

// NUCLEAR OPTION: Completely destroy session
echo "<h3>Destroying Session...</h3>";

// Unset all session variables
$_SESSION = array();

// Delete session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

echo "<p style='color: orange;'>✅ Session destroyed!</p>";

// Start a completely new session
session_start();

echo "<p><strong>New Session ID:</strong> " . session_id() . "</p>";

// Get firm data from database
require_once '../includes/config.php';

$sql = "SELECT * FROM tax_firms ORDER BY id LIMIT 1";
$result = $conn->query($sql);
$firm = $result->fetch_assoc();

if ($firm) {
    echo "<h3>Setting New Session Data:</h3>";
    
    // Set session variables
    $_SESSION['firm_id'] = (int)$firm['id'];  // Ensure it's an integer
    $_SESSION['firm_name'] = $firm['name'];
    $_SESSION['firm_email'] = $firm['email'];
    $_SESSION['firm_registration_number'] = $firm['registration_number'];
    $_SESSION['firm_registration_status'] = $firm['registration_status'];
    $_SESSION['login_time'] = time();
    $_SESSION['last_activity'] = time();
    $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // Force session write
    session_write_close();
    
    // Start session again to verify
    session_start();
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<p style='color: #155724; margin: 0;'>✅ New session created successfully!</p>";
    echo "<p style='color: #155724;'><strong>Firm ID:</strong> " . $_SESSION['firm_id'] . "</p>";
    echo "<p style='color: #155724;'><strong>Firm Name:</strong> " . htmlspecialchars($_SESSION['firm_name']) . "</p>";
    echo "</div>";
    
    // Show new session data
    echo "<h3>New Session Data:</h3>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    print_r($_SESSION);
    echo "</pre>";
    
    // Test database connection with new session
    echo "<h3>Testing Database Connection:</h3>";
    $test_sql = "SELECT id, name, email FROM tax_firms WHERE id = ?";
    $test_stmt = $conn->prepare($test_sql);
    $test_stmt->bind_param("i", $_SESSION['firm_id']);
    $test_stmt->execute();
    $test_result = $test_stmt->get_result();
    $test_firm = $test_result->fetch_assoc();
    
    if ($test_firm) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
        echo "<p style='color: #155724; margin: 0;'>✅ Database test successful!</p>";
        echo "<p style='color: #155724;'>Found firm: " . htmlspecialchars($test_firm['name']) . " (ID: " . $test_firm['id'] . ")</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo "<p style='color: #721c24; margin: 0;'>❌ Database test failed!</p>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>❌ No firms found in database!</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h3>⚠️ Important Instructions:</h3>
    <p><strong>The session has been completely reset. Follow these steps:</strong></p>
    <ol>
        <li><strong>Close ALL browser tabs</strong> for this website</li>
        <li><strong>Clear browser cache</strong> (Ctrl+Shift+Delete)</li>
        <li><strong>Open a new browser tab</strong></li>
        <li><strong>Go directly to the dashboard</strong></li>
    </ol>
    
    <p><strong>Or click the button below after closing other tabs:</strong></p>
    <p>
        <a href="dashboard.php" style="background: #dc3545; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">🚀 Go to Dashboard (New Session)</a>
    </p>
</div>

<script>
// Clear any cached data
if ('caches' in window) {
    caches.keys().then(function(names) {
        names.forEach(function(name) {
            caches.delete(name);
        });
    });
}

// Clear localStorage and sessionStorage
localStorage.clear();
sessionStorage.clear();

console.log('Browser cache cleared');
</script>
