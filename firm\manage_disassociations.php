<?php
require_once '../includes/config.php';
require_once '../includes/audit_functions.php';
session_start();

// Check if firm is logged in
if (!isset($_SESSION['firm_id'])) {
    header("Location: login.php");
    exit();
}

$firm_id = $_SESSION['firm_id'];
$message = '';
$alert_class = '';

// Process disassociation approval/rejection
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['request_id']) && isset($_POST['action'])) {
    $request_id = $_POST['request_id'];
    $action = $_POST['action'];
    $status = ($action == 'approve') ? 'approved' : 'rejected';
    
    // Get association ID from request
    $get_sql = "SELECT association_id, practitioner_id, reason FROM practitioner_disassociation_requests WHERE id = ? AND firm_id = ?";
    $get_stmt = $conn->prepare($get_sql);
    $get_stmt->bind_param("ii", $request_id, $firm_id);
    $get_stmt->execute();
    $result = $get_stmt->get_result();
    
    if ($result->num_rows > 0) {
        $request_data = $result->fetch_assoc();
        $association_id = $request_data['association_id'];
        $practitioner_id = $request_data['practitioner_id'];
        $reason = $request_data['reason'];
        
        // Update request status
        $update_req_sql = "UPDATE practitioner_disassociation_requests 
                          SET status = ?, response_date = NOW() 
                          WHERE id = ? AND firm_id = ?";
        $update_req_stmt = $conn->prepare($update_req_sql);
        $update_req_stmt->bind_param("sii", $status, $request_id, $firm_id);
        $update_req_stmt->execute();
        
        if ($status == 'approved') {
            // Update association status to inactive
            $update_assoc_sql = "UPDATE practitioner_firm_associations 
                               SET status = 'inactive', end_date = NOW() 
                               WHERE id = ? AND firm_id = ?";
            $update_assoc_stmt = $conn->prepare($update_assoc_sql);
            $update_assoc_stmt->bind_param("ii", $association_id, $firm_id);
            $update_assoc_stmt->execute();
            
            // Log the event to audit trail
            log_association_event(
                $practitioner_id,
                $firm_id,
                $association_id,
                'approve_disassociation',
                'pending_disassociation',
                'inactive',
                'Disassociation request approved by firm. Reason: ' . $reason,
                'firm',
                $firm_id,
                $conn
            );
            
            $message = "Disassociation request has been approved. The practitioner is no longer associated with your firm.";
        } else {
            // Revert association status to active
            $update_assoc_sql = "UPDATE practitioner_firm_associations 
                               SET status = 'active' 
                               WHERE id = ? AND firm_id = ?";
            $update_assoc_stmt = $conn->prepare($update_assoc_sql);
            $update_assoc_stmt->bind_param("ii", $association_id, $firm_id);
            $update_assoc_stmt->execute();
            
            // Log the event to audit trail
            log_association_event(
                $practitioner_id,
                $firm_id,
                $association_id,
                'reject_disassociation',
                'pending_disassociation',
                'active',
                'Disassociation request rejected by firm. Reason for request: ' . $reason,
                'firm',
                $firm_id,
                $conn
            );
            
            $message = "Disassociation request has been rejected. The practitioner remains associated with your firm.";
        }
        
        // Send notification email to practitioner
        $practitioner_sql = "SELECT full_name, email FROM tax_practitioners WHERE id = ?";
        $practitioner_stmt = $conn->prepare($practitioner_sql);
        $practitioner_stmt->bind_param("i", $practitioner_id);
        $practitioner_stmt->execute();
        $practitioner_result = $practitioner_stmt->get_result();
        $practitioner_data = $practitioner_result->fetch_assoc();
        
        $firm_sql = "SELECT name, email FROM tax_firms WHERE id = ?";
        $firm_stmt = $conn->prepare($firm_sql);
        $firm_stmt->bind_param("i", $firm_id);
        $firm_stmt->execute();
        $firm_result = $firm_stmt->get_result();
        $firm_data = $firm_result->fetch_assoc();
        
        $to = $practitioner_data['email'];
        $subject = "Disassociation Request " . ($status == 'approved' ? 'Approved' : 'Rejected');
        $message_body = "Dear " . $practitioner_data['full_name'] . ",\n\n";
        $message_body .= "Your request to disassociate from " . $firm_data['name'] . " has been " . ($status == 'approved' ? 'approved' : 'rejected') . ".\n\n";
        
        if ($status == 'approved') {
            $message_body .= "You are no longer associated with this firm.\n\n";
        } else {
            $message_body .= "You remain associated with this firm.\n\n";
        }
        
        $message_body .= "Regards,\nTax Professional Management System";
        $headers = "From: " . $firm_data['email'] . "\r\n";
        
        mail($to, $subject, $message_body, $headers);
        
        $alert_class = "alert-success";
    } else {
        $message = "Invalid request or you don't have permission to process this request.";
        $alert_class = "alert-danger";
    }
}

// Get all disassociation requests
$requests_sql = "SELECT r.*, a.start_date as association_start_date,
                p.id as practitioner_id, p.full_name as practitioner_name, 
                p.email, p.phone, p.registration_number, p.qualification, p.specialization
                FROM practitioner_disassociation_requests r
                JOIN practitioner_firm_associations a ON r.association_id = a.id
                JOIN tax_practitioners p ON r.practitioner_id = p.id
                WHERE r.firm_id = ?
                ORDER BY r.status = 'pending' DESC, r.request_date DESC";
$requests_stmt = $conn->prepare($requests_sql);
$requests_stmt->bind_param("i", $firm_id);
$requests_stmt->execute();
$requests_result = $requests_stmt->get_result();

// Count pending requests
$pending_count = 0;
$requests = [];
while ($row = $requests_result->fetch_assoc()) {
    if ($row['status'] == 'pending') {
        $pending_count++;
    }
    $requests[] = $row;
}

include '../includes/header.php';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-unlink me-2"></i>Practitioner Disassociation Requests</h2>
        <div>
            <a href="associated_practitioners.php" class="btn btn-primary me-2">
                <i class="fas fa-users me-2"></i>View All Practitioners
            </a>
            <a href="dashboard.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
    
    <?php if (!empty($message)): ?>
    <div class="alert <?php echo $alert_class; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $alert_class == 'alert-success' ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <?php if ($pending_count > 0): ?>
    <div class="alert alert-warning" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>You have <?php echo $pending_count; ?> pending disassociation request(s)</strong> from practitioners who wish to end their association with your firm.
    </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="requestTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" 
                            type="button" role="tab" aria-controls="pending" aria-selected="true">
                        Pending 
                        <?php if ($pending_count > 0): ?>
                        <span class="badge bg-danger ms-1"><?php echo $pending_count; ?></span>
                        <?php endif; ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="processed-tab" data-bs-toggle="tab" data-bs-target="#processed" 
                            type="button" role="tab" aria-controls="processed" aria-selected="false">
                        Processed Requests
                    </button>
                </li>
            </ul>
        </div>
        
