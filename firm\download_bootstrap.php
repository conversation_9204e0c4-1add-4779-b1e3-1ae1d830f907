<?php
// Download Bootstrap CSS and JS files locally
$bootstrap_css_url = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
$bootstrap_js_url = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
$fontawesome_css_url = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';

// Create assets directory
$assets_dir = '../assets/';
if (!file_exists($assets_dir)) {
    mkdir($assets_dir, 0755, true);
}

// Download Bootstrap CSS
echo "Downloading Bootstrap CSS...<br>";
$bootstrap_css = file_get_contents($bootstrap_css_url);
if ($bootstrap_css !== false) {
    file_put_contents($assets_dir . 'bootstrap.min.css', $bootstrap_css);
    echo "✅ Bootstrap CSS downloaded successfully!<br>";
} else {
    echo "❌ Failed to download Bootstrap CSS<br>";
}

// Download Bootstrap JS
echo "Downloading Bootstrap JS...<br>";
$bootstrap_js = file_get_contents($bootstrap_js_url);
if ($bootstrap_js !== false) {
    file_put_contents($assets_dir . 'bootstrap.bundle.min.js', $bootstrap_js);
    echo "✅ Bootstrap JS downloaded successfully!<br>";
} else {
    echo "❌ Failed to download Bootstrap JS<br>";
}

// Download Font Awesome CSS
echo "Downloading Font Awesome CSS...<br>";
$fontawesome_css = file_get_contents($fontawesome_css_url);
if ($fontawesome_css !== false) {
    file_put_contents($assets_dir . 'fontawesome.min.css', $fontawesome_css);
    echo "✅ Font Awesome CSS downloaded successfully!<br>";
} else {
    echo "❌ Failed to download Font Awesome CSS<br>";
}

echo "<br><strong>Files downloaded to: " . realpath($assets_dir) . "</strong><br>";
echo "<br>Now update your HTML to use local files:<br>";
echo "<code>&lt;link href=\"../assets/bootstrap.min.css\" rel=\"stylesheet\"&gt;</code><br>";
echo "<code>&lt;link href=\"../assets/fontawesome.min.css\" rel=\"stylesheet\"&gt;</code><br>";
echo "<code>&lt;script src=\"../assets/bootstrap.bundle.min.js\"&gt;&lt;/script&gt;</code><br>";
?>
