<?php
session_start();
require_once '../includes/config.php';

// Check if already logged in
if (isset($_SESSION['firm_id'])) {
    header("Location: dashboard.php");
    exit();
}

$error = '';
$success_message = '';

// Check for logout message
if (isset($_SESSION['logout_message'])) {
    $success_message = $_SESSION['logout_message'];
    unset($_SESSION['logout_message']);
}

// Check for logout parameter in URL
if (isset($_GET['logout']) && $_GET['logout'] === 'success') {
    $success_message = "You have been successfully logged out.";
}

// Process login form
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = trim($_POST['email']);
    $password = $_POST['password'];

    // Validate input
    if (empty($email) || empty($password)) {
        $error = "Please enter both email and password.";
    } else {
        // Simple hardcoded check for test account
        if ($email === "<EMAIL>" && $password === "password123") {
            // Set session variables
            $_SESSION['firm_id'] = 1;
            $_SESSION['firm_name'] = "Test Tax Consulting Firm";
            $_SESSION['firm_email'] = $email;

            // Redirect to dashboard
            header("Location: dashboard.php");
            exit();
        }

        // Check database for other accounts
        $sql = "SELECT id, name, email, password, registration_status FROM tax_firms WHERE email = ?";
        $stmt = $conn->prepare($sql);

        if ($stmt === false) {
            $error = "Database error: " . $conn->error;
        } else {
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows === 1) {
                $firm = $result->fetch_assoc();

                // Verify password
                $password_correct = password_verify($password, $firm['password']);

                if ($password_correct) {
                    // Check if account is active
                    if ($firm['registration_status'] == 'Active') {
                        // Set session variables
                        $_SESSION['firm_id'] = $firm['id'];
                        $_SESSION['firm_name'] = $firm['name'];
                        $_SESSION['firm_email'] = $firm['email'];

                        // Log login activity (optional)
                        $log_sql = "INSERT INTO activity_log (user_id, user_type, activity, ip_address) VALUES (?, 'firm', 'login', ?)";
                        $log_stmt = $conn->prepare($log_sql);

                        if ($log_stmt) {
                            $ip = $_SERVER['REMOTE_ADDR'];
                            $log_stmt->bind_param("is", $firm['id'], $ip);
                            $log_stmt->execute();
                        }

                        // Redirect to dashboard
                        header("Location: dashboard.php");
                        exit();
                    } else {
                        $error = "Your account is not active. Status: " . $firm['registration_status'];
                    }
                } else {
                    $error = "Invalid email or password.";
                }
            } else {
                $error = "Invalid email or password.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firm Login - Tax Registration System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            --success-gradient: linear-gradient(135deg, #10b981 0%, #**********%);
            --accent-gradient: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            --dark-gradient: linear-gradient(135deg, #14532d 0%, #**********%);
            --light-green-gradient: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-soft: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-glow: 0 25px 50px -12px rgba(34, 197, 94, 0.25);
            --green-shadow: 0 10px 20px rgba(34, 197, 94, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animated background elements */
        .bg-decoration {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-shape:nth-child(1) {
            top: 20%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            top: 60%;
            right: 10%;
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation-delay: 2s;
        }

        .floating-shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            width: 60px;
            height: 60px;
            background: white;
            transform: rotate(45deg);
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Glass morphism container */
        .glass-container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-soft);
            margin: 2rem 0;
            padding: 3rem;
        }

        .auth-container {
            max-width: 450px;
            margin: 3rem auto;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: var(--shadow-soft);
            background: white;
        }

        .card-header {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .card-header h3 {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .card-body {
            padding: 2rem;
        }

        .form-label {
            font-weight: 500;
            color: #4b5563;
            margin-bottom: 0.5rem;
        }

        .form-control {
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #22c55e;
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
        }

        .input-group-text {
            background-color: #f9fafb;
            border-color: #e5e7eb;
            color: #6b7280;
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--green-shadow);
        }

        .btn-outline-secondary {
            border-color: #e5e7eb;
            color: #6b7280;
        }

        .btn-outline-secondary:hover {
            background-color: #f9fafb;
            color: #4b5563;
        }

        .auth-links {
            margin-top: 2rem;
            text-align: center;
        }

        .auth-links a {
            color: #22c55e;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .auth-links a:hover {
            color: #16a34a;
            text-decoration: underline;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 0.8s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .invalid-feedback {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="bg-decoration">
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="auth-container">
                    <div class="card border-0 shadow-none">
                        <div class="card-header">
                            <h3>Firm Login</h3>
                            <p class="mb-0">Access your tax firm dashboard</p>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($error)): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fa fa-exclamation-circle me-2"></i> <?php echo htmlspecialchars($error); ?>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($success_message)): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i> <?php echo htmlspecialchars($success_message); ?>
                            </div>
                            <?php endif; ?>
                            
                            <form id="loginForm" method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                <div class="mb-4">
                                    <label for="email" class="form-label">Email Address</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                        <input 
                                            type="email" 
                                            class="form-control" 
                                            id="email" 
                                            name="email" 
                                            placeholder="Enter your email"
                                            value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                                            required
                                        >
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <label for="password" class="form-label mb-0">Password</label>
                                        <a href="forgot_password.php" class="text-sm text-decoration-none">Forgot password?</a>
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input 
                                            type="password" 
                                            class="form-control" 
                                            id="password" 
                                            name="password" 
                                            placeholder="Enter your password"
                                            required
                                        >
                                        <button 
                                            type="button" 
                                            class="btn btn-outline-secondary" 
                                            id="togglePassword"
                                        >
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input 
                                        type="checkbox" 
                                        class="form-check-input" 
                                        id="remember" 
                                        name="remember"
                                    >
                                    <label class="form-check-label" for="remember">
                                        Keep me signed in
                                    </label>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary" id="loginBtn">
                                        <span class="btn-text">
                                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                        </span>
                                        <span class="loading-spinner"></span>
                                    </button>
                                </div>
                            </form>
                            
                            <div class="auth-links">
                                <p class="mb-0">
                                    Don't have an account? <a href="../firm_registration.php">Register Now</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="../index.php" class="text-white text-decoration-none">
                        <i class="fas fa-arrow-left me-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        class LoginForm {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.emailInput = document.getElementById('email');
                this.passwordInput = document.getElementById('password');
                this.toggleBtn = document.getElementById('togglePassword');
                this.submitBtn = document.getElementById('loginBtn');
                this.loadingSpinner = document.querySelector('.loading-spinner');
                this.btnText = document.querySelector('.btn-text');
                
                this.init();
            }

            init() {
                this.bindEvents();
            }

            bindEvents() {
                if (this.toggleBtn) {
                    this.toggleBtn.addEventListener('click', () => this.togglePasswordVisibility());
                }
                
                if (this.form) {
                    this.form.addEventListener('submit', (e) => this.handleSubmit(e));
                }
            }

            togglePasswordVisibility() {
                const type = this.passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                this.passwordInput.setAttribute('type', type);
                
                const icon = this.toggleBtn.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            }

            handleSubmit(e) {
                if (this.validateForm()) {
                    this.showLoading();
                } else {
                    e.preventDefault();
                }
            }

            validateForm() {
                let isValid = true;
                
                // Simple validation example - can be expanded
                if (!this.emailInput.value.trim()) {
                    isValid = false;
                    this.showError(this.emailInput, 'Email is required');
                } else if (!this.isValidEmail(this.emailInput.value)) {
                    isValid = false;
                    this.showError(this.emailInput, 'Please enter a valid email');
                }
                
                if (!this.passwordInput.value) {
                    isValid = false;
                    this.showError(this.passwordInput, 'Password is required');
                }
                
                return isValid;
            }

            isValidEmail(email) {
                const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(String(email).toLowerCase());
            }

            showError(input, message) {
                const formGroup = input.closest('.mb-4');
                const existingFeedback = formGroup.querySelector('.invalid-feedback');
                
                if (existingFeedback) {
                    existingFeedback.textContent = message;
                } else {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'invalid-feedback d-block';
                    errorDiv.textContent = message;
                    formGroup.appendChild(errorDiv);
                }
                
                input.classList.add('is-invalid');
            }

            showLoading() {
                this.submitBtn.disabled = true;
                this.btnText.style.display = 'none';
                this.loadingSpinner.style.display = 'block';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            new LoginForm();
        });
    </script>
</body>
</html>








