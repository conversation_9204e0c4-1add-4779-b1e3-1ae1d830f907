<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../config/db_connect.php';

// Check if user is logged in as a firm
if (!isset($_SESSION['firm_id'])) {
    header('Location: login.php');
    exit;
}

$firm_id = $_SESSION['firm_id'];
$firm_name = $_SESSION['firm_name'] ?? 'Unknown Firm';
$firm_email = $_SESSION['firm_email'] ?? '';

// Get firm details
$firm_sql = "SELECT * FROM tax_firms WHERE id = ?";
$firm_stmt = $conn->prepare($firm_sql);
$firm_stmt->bind_param("i", $firm_id);
$firm_stmt->execute();
$firm_result = $firm_stmt->get_result();
$firm_data = $firm_result->fetch_assoc();
$firm_stmt->close();

// Define required documents
$required_documents = [
    [
        'id' => 'memart',
        'title' => 'CTC of Memorandum & Articles of Association (MEMART)',
        'type' => 'MANDATORY',
        'description' => 'Certified True Copy of Memorandum and Articles of Association'
    ],
    [
        'id' => 'cac_status',
        'title' => 'CAC Company Status Report',
        'type' => 'MANDATORY',
        'description' => 'Current Company Status Report from Corporate Affairs Commission'
    ],
    [
        'id' => 'utility_bill',
        'title' => 'Current Utility Bill',
        'type' => 'MANDATORY',
        'description' => 'Recent utility bill showing company address'
    ],
    [
        'id' => 'director_change',
        'title' => 'Signed Application Letter to the Commission Requesting for Update/Change to Director Information',
        'type' => 'OPTIONAL',
        'description' => 'Letter requesting director information updates'
    ],
    [
        'id' => 'secretary_change',
        'title' => 'Signed Application Letter to the Commission Requesting for Update/Change to Company Secretary Information',
        'type' => 'OPTIONAL',
        'description' => 'Letter requesting company secretary information updates'
    ],
    [
        'id' => 'shareholder_change',
        'title' => 'Signed Application Letter to the Commission Requesting for Update/Change to Shareholder Information',
        'type' => 'OPTIONAL',
        'description' => 'Letter requesting shareholder information updates'
    ],
    [
        'id' => 'address_change',
        'title' => 'Signed Application Letter to the Commission Requesting for Update/Change of Address',
        'type' => 'OPTIONAL',
        'description' => 'Letter requesting address change'
    ],
    [
        'id' => 'board_resolution',
        'title' => 'Board Resolution',
        'type' => 'OPTIONAL',
        'description' => 'Board resolution document'
    ],
    [
        'id' => 'notification_letter',
        'title' => 'Signed Letter of Notification',
        'type' => 'OPTIONAL',
        'description' => 'Signed notification letter'
    ]
];

// Get uploaded documents for this firm
$uploaded_docs = [];
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ?";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();
while ($doc = $docs_result->fetch_assoc()) {
    $uploaded_docs[$doc['document_type']] = $doc;
}
$docs_stmt->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supporting Documents - <?php echo htmlspecialchars($firm_name); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            background-color: var(--secondary-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .document-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .document-item {
            display: flex;
            align-items: center;
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.2s ease;
            background: white;
        }

        .document-item:hover {
            background-color: #f8f9fa;
        }

        .document-item:last-child {
            border-bottom: none;
        }

        .document-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #6c757d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.1rem;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .document-number.mandatory {
            background: #007bff;
        }

        .document-content {
            flex: 1;
            margin-right: 20px;
        }

        .document-title {
            font-size: 1rem;
            color: #495057;
            font-weight: 500;
            line-height: 1.4;
            margin-bottom: 5px;
        }

        .document-status {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .status-uploaded {
            color: #28a745;
        }

        .status-pending {
            color: #ffc107;
        }

        .document-actions {
            position: relative;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 8px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #495057;
            transition: all 0.2s ease;
        }

        .dropdown-toggle:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 180px;
            z-index: 1000;
            display: none;
            margin-top: 5px;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 10px 15px;
            border: none;
            background: none;
            text-align: left;
            color: #495057;
            text-decoration: none;
            font-size: 0.9rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
        }

        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }

        .dropdown-item.disabled {
            color: #adb5bd;
            cursor: not-allowed;
        }

        .dropdown-item.disabled:hover {
            background: none;
        }

        .mandatory {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .optional {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .document-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .document-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            background: #1e3d72;
            transform: translateY(-1px);
        }

        .uploaded-status {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--success-color);
            font-weight: 600;
        }

        .file-info {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .progress-summary {
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .progress-bar-custom {
            background: rgba(255,255,255,0.3);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            background: white;
            height: 100%;
            transition: width 0.3s ease;
        }

        .back-btn {
            background: #6c757d;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-building me-2"></i>
                Tax Registration System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    <?php echo htmlspecialchars($firm_name); ?>
                </span>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-file-upload me-3"></i>
                        Supporting Documents
                    </h1>
                    <p class="text-muted mb-0">Upload required documents for tax registration verification</p>
                </div>
                <a href="dashboard.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Progress Summary -->
        <?php
        $mandatory_count = 0;
        $mandatory_uploaded = 0;
        $total_uploaded = 0;
        
        foreach ($required_documents as $doc) {
            if ($doc['type'] === 'MANDATORY') {
                $mandatory_count++;
                if (isset($uploaded_docs[$doc['id']])) {
                    $mandatory_uploaded++;
                }
            }
            if (isset($uploaded_docs[$doc['id']])) {
                $total_uploaded++;
            }
        }
        
        $progress_percentage = $mandatory_count > 0 ? ($mandatory_uploaded / $mandatory_count) * 100 : 0;
        ?>
        
        <div class="progress-summary">
            <div class="row">
                <div class="col-md-8">
                    <h4 class="mb-2">Document Upload Progress</h4>
                    <p class="mb-2">
                        <strong><?php echo $mandatory_uploaded; ?></strong> of <strong><?php echo $mandatory_count; ?></strong> mandatory documents uploaded
                        (<?php echo $total_uploaded; ?> total documents)
                    </p>
                    <div class="progress-bar-custom">
                        <div class="progress-fill" style="width: <?php echo $progress_percentage; ?>%"></div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="h2 mb-0"><?php echo round($progress_percentage); ?>%</div>
                    <small>Completion Rate</small>
                </div>
            </div>
        </div>

        <!-- Documents List -->
        <div class="document-section">
            <?php foreach ($required_documents as $index => $doc): ?>
                <?php $is_uploaded = isset($uploaded_docs[$doc['id']]); ?>
                <div class="document-item">
                    <div class="document-number <?php echo $doc['type'] === 'MANDATORY' ? 'mandatory' : ''; ?>">
                        <?php echo $index + 1; ?>
                    </div>

                    <div class="document-content">
                        <div class="document-title">
                            <?php echo htmlspecialchars($doc['title']); ?>
                        </div>
                        <div class="document-status">
                            <?php if ($is_uploaded): ?>
                                <span class="status-uploaded">
                                    <i class="fas fa-check-circle"></i>
                                    Uploaded on <?php echo date('M j, Y', strtotime($uploaded_docs[$doc['id']]['uploaded_at'])); ?>
                                </span>
                            <?php else: ?>
                                <span class="status-pending">
                                    <i class="fas fa-clock"></i>
                                    Not uploaded
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="document-actions">
                        <div class="dropdown">
                            <div class="dropdown-toggle" onclick="toggleDropdown(this)">
                                <span><?php echo $doc['type']; ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="dropdown-menu">
                                <button class="dropdown-item" onclick="uploadDocument('<?php echo $doc['id']; ?>')">
                                    <i class="fas fa-upload"></i>
                                    <?php echo $is_uploaded ? 'Replace' : 'Upload'; ?>
                                </button>
                                <?php if ($is_uploaded): ?>
                                <button class="dropdown-item" onclick="viewDocument(<?php echo $uploaded_docs[$doc['id']]['id']; ?>)">
                                    <i class="fas fa-eye"></i>
                                    View
                                </button>
                                <a href="download_document.php?id=<?php echo $uploaded_docs[$doc['id']]['id']; ?>" class="dropdown-item">
                                    <i class="fas fa-download"></i>
                                    Download
                                </a>
                                <button class="dropdown-item" onclick="deleteDocument('<?php echo $doc['id']; ?>')">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                                <?php else: ?>
                                <span class="dropdown-item disabled">
                                    <i class="fas fa-eye"></i>
                                    View
                                </span>
                                <span class="dropdown-item disabled">
                                    <i class="fas fa-download"></i>
                                    Download
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Hidden file input -->
    <input type="file" id="fileInput" style="display: none;" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" onchange="handleFileUpload()">

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentDocumentType = '';

        // Dropdown functionality
        function toggleDropdown(element) {
            const dropdown = element.parentElement;
            const menu = dropdown.querySelector('.dropdown-menu');

            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });

            // Toggle current dropdown
            menu.classList.toggle('show');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });

        function uploadDocument(documentType) {
            currentDocumentType = documentType;
            document.getElementById('fileInput').click();
        }

        function replaceDocument(documentType) {
            if (confirm('Are you sure you want to replace this document?')) {
                uploadDocument(documentType);
            }
        }

        function deleteDocument(documentType) {
            if (confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
                // Add delete functionality here
                fetch('delete_document.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        document_type: documentType
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Document deleted successfully!');
                        location.reload();
                    } else {
                        alert('Delete failed: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    alert('An error occurred during deletion');
                });
            }
        }

        function handleFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) return;

            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('File size must be less than 10MB');
                return;
            }

            // Validate file type
            const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please upload only PDF, DOC, DOCX, JPG, JPEG, or PNG files');
                return;
            }

            // Create FormData and upload
            const formData = new FormData();
            formData.append('document', file);
            formData.append('document_type', currentDocumentType);

            // Show loading state
            const uploadBtn = event.target.previousElementSibling || event.target;
            const originalText = uploadBtn.innerHTML;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
            uploadBtn.disabled = true;

            fetch('upload_document.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Document uploaded successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while uploading the document');
            })
            .finally(() => {
                uploadBtn.innerHTML = originalText;
                uploadBtn.disabled = false;
            });
        }

        function viewDocument(documentId) {
            window.open('view_document.php?id=' + documentId, '_blank');
        }
    </script>
</body>
</html>

<?php $conn->close(); ?>
