<!DOCTYPE html>
<html>
<head>
    <title>Test Document Upload</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input[type="file"] { padding: 8px; width: 300px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>Test Document Upload</h1>
    
    <div class="info result">
        <p><strong>Instructions:</strong></p>
        <ul>
            <li>Select a document type from the dropdown</li>
            <li>Choose a file (PDF, JPG, PNG, DOC, DOCX - max 10MB)</li>
            <li>Click upload to test the functionality</li>
            <li>Check the result message below</li>
        </ul>
    </div>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div class="form-group">
            <label for="document_type">Document Type:</label>
            <select id="document_type" name="document_type" required>
                <option value="">Select document type</option>
                <option value="memart">CTC of Memorandum & Articles of Association (MEMART)</option>
                <option value="cac_status">CAC Company Status Report</option>
                <option value="tax_clearance">Current Tax Clearance Certificate</option>
                <option value="utility_bill">Current Utility Bill</option>
                <option value="incorporation_cert">CAC Certificate of Incorporation</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="document">Select File:</label>
            <input type="file" id="document" name="document" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
        </div>
        
        <div class="form-group">
            <button type="submit" id="uploadBtn">Upload Document</button>
        </div>
    </form>
    
    <div id="result"></div>
    
    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
        <h3>Quick Actions:</h3>
        <p>
            <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
            <a href="test_document_system.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">System Test</a>
        </p>
    </div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const uploadBtn = document.getElementById('uploadBtn');
            const resultDiv = document.getElementById('result');
            
            // Validate file selection
            const fileInput = document.getElementById('document');
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error result">Please select a file to upload.</div>';
                return;
            }
            
            // Check file size (10MB max)
            if (fileInput.files[0].size > 10 * 1024 * 1024) {
                resultDiv.innerHTML = '<div class="error result">File size too large. Maximum size is 10MB.</div>';
                return;
            }
            
            // Show loading state
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Uploading...';
            resultDiv.innerHTML = '<div class="info result">Uploading file, please wait...</div>';
            
            // Upload file
            fetch('upload_document.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success result">
                            <h4>✅ Upload Successful!</h4>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <p><strong>File:</strong> ${data.file_name}</p>
                            <p><strong>Upload Date:</strong> ${data.upload_date}</p>
                            <p><a href="dashboard.php">View in Dashboard</a></p>
                        </div>
                    `;
                    // Reset form
                    this.reset();
                } else {
                    resultDiv.innerHTML = `
                        <div class="error result">
                            <h4>❌ Upload Failed</h4>
                            <p><strong>Error:</strong> ${data.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                resultDiv.innerHTML = `
                    <div class="error result">
                        <h4>❌ Upload Error</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            })
            .finally(() => {
                // Reset button state
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Upload Document';
            });
        });
        
        // File input change handler
        document.getElementById('document').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const resultDiv = document.getElementById('result');
            
            if (file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                resultDiv.innerHTML = `
                    <div class="info result">
                        <p><strong>Selected File:</strong> ${file.name}</p>
                        <p><strong>Size:</strong> ${fileSize} MB</p>
                        <p><strong>Type:</strong> ${file.type}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
