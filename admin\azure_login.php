<?php
/**
 * Azure AD Login Handler
 * 
 * Handles the Azure AD authentication flow for administrators.
 */
session_start();
require_once '../includes/config.php';
require_once '../includes/azure_ad_integration.php';

// Check if Azure AD libraries are installed
if (!function_exists('check_azure_libraries') || !check_azure_libraries()) {
    $_SESSION['alert'] = [
        'type' => 'danger',
        'message' => 'Azure AD integration is not properly configured. Required libraries are missing. Please run "composer require microsoft/microsoft-graph" in the project root.'
    ];
    header('Location: login.php');
    exit;
}

// Check if this is a callback from Azure AD
if (isset($_GET['code']) && isset($_GET['state'])) {
    // Verify state to prevent CSRF
    if (!isset($_SESSION['azure_auth_state']) || $_SESSION['azure_auth_state'] !== $_GET['state']) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Invalid authentication state. Please try again.'
        ];
        header('Location: login.php');
        exit;
    }
    
    // Exchange code for token
    $token_data = get_azure_token($_GET['code']);
    if (!$token_data) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Failed to authenticate with Azure AD. Please try again.'
        ];
        header('Location: login.php');
        exit;
    }
    
    // Get user information
    $user_info = get_azure_user_info($token_data['access_token']);
    if (!$user_info) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Failed to retrieve user information. Please try again.'
        ];
        header('Location: login.php');
        exit;
    }
    
    // Check if user exists in our system
    $sql = "SELECT id, full_name, role, last_login, is_active FROM administrators WHERE azure_id = ? OR email = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $user_info['id'], $user_info['email']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // User not found in our system
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Your account is not authorized to access the admin panel. Please contact a system administrator.'
        ];
        header('Location: login.php');
        exit;
    }
    
    $admin = $result->fetch_assoc();
    
    // Check if user is active
    if (!$admin['is_active']) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Your account has been deactivated. Please contact a system administrator.'
        ];
        header('Location: login.php');
        exit;
    }
    
    // Update user information if needed
    if ($admin['full_name'] !== $user_info['display_name'] || empty($admin['azure_id'])) {
        $update_sql = "UPDATE administrators SET 
                      full_name = ?, 
                      azure_id = ?, 
                      last_login = NOW() 
                      WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ssi", $user_info['display_name'], $user_info['id'], $admin['id']);
        $update_stmt->execute();
    } else {
        // Just update last login
        $update_sql = "UPDATE administrators SET last_login = NOW() WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("i", $admin['id']);
        $update_stmt->execute();
    }
    
    // Set session variables
    $_SESSION['admin_id'] = $admin['id'];
    $_SESSION['admin_name'] = $user_info['display_name'];
    $_SESSION['admin_email'] = $user_info['email'];
    $_SESSION['admin_role'] = $admin['role'];
    
    // Log the login
    log_admin_action($admin['id'], 'login', 'Logged in via Azure AD');
    
    // Redirect to dashboard
    header('Location: dashboard.php');
    exit;
} else {
    // Initiate Azure AD login
    $auth_url = get_azure_auth_url();
    if (!$auth_url) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Azure AD integration is not properly configured. Please contact a system administrator.'
        ];
        header('Location: login.php');
        exit;
    }
    
    // Redirect to Azure AD login page
    header('Location: ' . $auth_url);
    exit;
}
