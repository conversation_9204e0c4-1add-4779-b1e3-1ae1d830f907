<?php
// Add debugging at the top but suppress notices
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

session_start();
require_once '../includes/config.php';

// Simple login check - bypass complex session manager
if (!isset($_SESSION['firm_id']) || !isset($_SESSION['firm_email'])) {
    header("Location: login.php");
    exit();
}

// Get session info for display
$firm_id = $_SESSION['firm_id'];
$firm_name = $_SESSION['firm_name'];
$firm_email = $_SESSION['firm_email'];

// Get firm details from database
$firm_sql = "SELECT * FROM tax_firms WHERE id = ?";
$firm_stmt = $conn->prepare($firm_sql);
$firm_stmt->bind_param("i", $firm_id);
$firm_stmt->execute();
$firm_result = $firm_stmt->get_result();
$firm_data = $firm_result->fetch_assoc();

// Ensure firm_data is not null and has default values
if (!$firm_data) {
    $firm_data = [];
}

// Set default values for any missing fields
$firm_data = array_merge([
    'name' => $firm_name ?? 'Unknown Firm',
    'email' => $firm_email ?? '',
    'phone' => '',
    'address' => '',
    'city' => '',
    'state' => '',
    'postal_code' => '',
    'country' => 'Nigeria',
    'alternative_email' => '',
    'alternative_phone' => '',
    'website' => '',
    'registration_number' => '',
    'cac_number' => '',
    'company_type' => 'Limited Liability Company',
    'ownership_type' => 'Nigerian Owned',
    'incorporation_date' => '',
    'registration_date' => '',
    'registration_status' => 'Pending',
    'tin_number' => '',
    'logo_path' => '',
    'profile_completion_percentage' => 0
], $firm_data);

// Helper function to safely get firm data values
function getFirmData($key, $default = '') {
    global $firm_data;
    return isset($firm_data[$key]) && $firm_data[$key] !== null ? $firm_data[$key] : $default;
}

// Simple function to check if all required fields are filled
function checkAllFieldsFilled($data) {
    $required_fields = [
        'name', 'email', 'phone', 'address', 'city', 'state',
        'registration_number', 'cac_number', 'tax_id', 'tin_number',
        'business_type', 'company_type', 'ownership_type', 'incorporation_date'
    ];

    foreach ($required_fields as $field) {
        $value = $data[$field] ?? '';
        if (empty($value) || trim($value) === '' || $value === 'Not provided') {
            return false;
        }
    }
    return true;
}

// Check if all fields are filled and update status
$all_fields_filled = checkAllFieldsFilled($firm_data);
$current_status = $all_fields_filled ? 'Active' : 'Pending';

// Update registration status if it has changed
if ($firm_data['registration_status'] !== $current_status) {
    $update_status_sql = "UPDATE tax_firms SET registration_status = ? WHERE id = ?";
    $update_status_stmt = $conn->prepare($update_status_sql);
    $update_status_stmt->bind_param("si", $current_status, $firm_id);
    $update_status_stmt->execute();
    $firm_data['registration_status'] = $current_status;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Profile - Tax Registration System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --border-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f1f5f9;
            color: var(--dark-color);
            line-height: 1.6;
        }

        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid var(--border-color);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-header h4 {
            color: var(--primary-color);
            font-weight: 600;
            margin: 0;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #64748b;
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: #f8fafc;
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }

        .nav-link.active {
            background-color: #eef2ff;
            color: var(--primary-color);
            border-left-color: var(--primary-color);
            font-weight: 500;
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 0.9rem;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-header {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            color: #64748b;
            font-size: 0.9rem;
        }

        .breadcrumb-nav a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb-nav i {
            margin: 0 0.5rem;
            font-size: 0.8rem;
        }

        .logout-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            background: #dc2626;
            color: white;
        }

        .content-area {
            flex: 1;
            padding: 2rem;
            background: #f8fafc;
        }

        /* Profile Section Styles */
        .profile-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }

        .verified-badge {
            background: var(--success-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        /* Company Name Section Styles - Based on React Component */
        .company-name-section {
            background: white;
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .company-verified-badge {
            background: #dcfce7;
            color: #166534;
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .text-lg {
            font-size: 1.125rem;
            line-height: 1.75rem;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-gray-900 {
            color: #111827;
        }

        .text-gray-400 {
            color: #9ca3af;
        }

        .text-gray-500 {
            color: #6b7280;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .company-name-display {
            margin-top: 0.25rem;
        }

        .company-name-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
            margin-top: 0;
        }

        .company-name-value {
            font-size: 0.875rem;
            font-weight: 500;
            color: #111827;
            margin-bottom: 0;
        }

        .info-icon {
            color: #94a3b8;
            margin-left: 0.5rem;
            cursor: help;
        }

        .edit-icon {
            color: #94a3b8;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .edit-icon:hover {
            background: #f1f5f9;
            color: var(--primary-color);
        }

        .save-icon, .cancel-icon {
            color: #94a3b8;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .save-icon:hover {
            background: #f1f5f9;
            color: var(--success-color);
        }

        .cancel-icon:hover {
            background: #f1f5f9;
            color: var(--danger-color);
        }

        /* Company Logo Section */
        .logo-section {
            display: flex;
            align-items: flex-start;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .logo-container {
            width: 150px;
            height: 150px;
            background: #e2e8f0;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .logo-placeholder {
            width: 80px;
            height: 80px;
            background: #94a3b8;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-placeholder i {
            font-size: 2rem;
            color: white;
        }

        .company-logo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        }

        .change-btn {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .company-name-section {
            flex: 1;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
            font-size: 0.9rem;
        }

        .required {
            color: var(--danger-color);
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            background: #f9fafb;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: white;
        }

        .form-control:read-only {
            background: #f3f4f6;
            color: #6b7280;
        }

        .form-control.editable {
            background: #fff;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
        }

        .form-control.editable:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        select.form-control.editable {
            background: #fff;
            color: #374151;
        }

        select.form-control:disabled {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* Enhanced verification status styling */
        .verification-status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .verification-status.verified {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .verification-status.almost-complete {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .verification-status.in-progress {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        .verification-status.pending {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        /* Progress bar enhancements */
        .progress {
            background-color: #f3f4f6;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .progress-bar {
            transition: width 0.6s ease;
        }

        /* Category completion cards */
        .category-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .category-card.complete {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-color: #0ea5e9;
        }

        .category-card.incomplete {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-color: #f87171;
        }

        /* Missing fields badges */
        .missing-field-badge {
            background: #fef3f2;
            color: #dc2626;
            border: 1px solid #fecaca;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Animated icons */
        .status-icon {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Tooltip enhancements */
        .info-icon {
            cursor: help;
            transition: color 0.3s ease;
        }

        .info-icon:hover {
            transform: scale(1.1);
        }

        /* Verification status badge in header */
        .verification-status-badge {
            font-size: 0.875rem;
            font-weight: 600;
        }

        .verification-status-badge .badge {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
        }

        /* Profile Section Styling */
        .profile-section {
            background: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #374151;
            margin: 0;
        }

        .verified-badge {
            background: #10b981;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        /* Logo Section */
        .logo-section {
            display: flex;
            justify-content: flex-start;
        }

        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #e5e7eb;
        }

        .logo-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #e5e7eb;
            color: #9ca3af;
            font-size: 2rem;
        }

        .change-btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .change-btn:hover {
            background: #4f46e5;
        }

        /* Form Styling */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
            display: block;
            font-weight: 500;
        }

        .required {
            color: #ef4444;
        }

        .input-with-controls {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .input-with-controls .form-control {
            flex: 1;
        }

        .edit-controls {
            display: flex;
            gap: 0.5rem;
        }

        .edit-controls i {
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.25rem;
            transition: all 0.2s;
        }

        .edit-controls i:hover {
            color: #374151;
            background: #f3f4f6;
        }

        .edit-controls .save-icon {
            color: #10b981;
        }

        .edit-controls .cancel-icon {
            color: #ef4444;
        }

        /* Info icon styling */
        .info-icon {
            color: #9ca3af;
            cursor: help;
        }

        .info-icon:hover {
            color: #6b7280;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: fixed;
                top: 0;
                left: -100%;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                width: 100%;
            }

            .top-header {
                padding: 1rem;
            }

            .content-area {
                padding: 1rem;
            }

            .logo-section {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h4><i class="fas fa-building me-2"></i>Company Details</h4>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="company_profile.php" class="nav-link active">
                        <i class="fas fa-user"></i>
                        Company Profile
                    </a>
                </div>
                <div class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-file-alt"></i>
                        Official Documents
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-folder"></i>
                        Supporting Documents
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-users"></i>
                        Shareholders
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-user-tie"></i>
                        Directors
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-user-cog"></i>
                        Company Secretaries
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        Tax Clearance Records
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-tools"></i>
                        Equipment
                    </a>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Header -->
            <div class="top-header">
                <div class="breadcrumb-nav">
                    <a href="#">Home</a>
                    <i class="fas fa-chevron-right"></i>
                    <span>Company Details</span>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <!-- Verification Status -->
                    <div id="verificationStatus" class="verification-status-badge">
                        <?php if ($firm_data['registration_status'] === 'Active'): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>VERIFIED
                            </span>
                        <?php else: ?>
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-clock me-1"></i>PENDING
                            </span>
                        <?php endif; ?>
                    </div>
                    <a href="#" onclick="confirmLogout()" class="logout-btn">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Company Logo and Name Section -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3 class="section-title">Company Profile</h3>
                    </div>



                    <!-- Company Logo Section -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3 class="section-title">Company Logo</h3>
                        </div>
                        <div class="logo-section">
                            <div class="logo-container">
                                <?php if (!empty($firm_data['logo_path']) && file_exists('../' . $firm_data['logo_path'])): ?>
                                    <img src="../<?php echo htmlspecialchars($firm_data['logo_path']); ?>" alt="Company Logo" class="company-logo">
                                <?php else: ?>
                                    <div class="logo-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                                <button class="change-btn" onclick="triggerLogoUpload()">Change</button>
                                <input type="file" id="logoInput" accept="image/*" style="display: none;" onchange="handleLogoUpload(this)">
                            </div>
                        </div>
                    </div>

                    <!-- Company Name Section -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3 class="section-title">Company Name</h3>
                            <div class="d-flex align-items-center">
                                <?php if ($firm_data['registration_status'] === 'Active'): ?>
                                    <span class="verified-badge">VERIFIED</span>
                                <?php endif; ?>
                                <i class="fas fa-info-circle info-icon ms-2" title="Company name information"></i>
                            </div>
                        </div>

                        <div class="company-name-section bg-white p-4 border-bottom">
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <h2 class="text-lg font-semibold text-gray-900 mb-0">Company Name</h2>
                                <div class="d-flex align-items-center gap-2">
                                    <?php if (!empty($firm_data['name']) || !empty($firm_name)): ?>
                                        <span class="company-verified-badge">VERIFIED</span>
                                        <i class="fas fa-info-circle text-gray-400"></i>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="company-name-display">
                                <p class="company-name-label">Company Name</p>
                                <p class="company-name-value"><?php echo htmlspecialchars($firm_data['name'] ?? $firm_name); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Company Registration Details -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3 class="section-title">Company Registration Details</h3>
                        <div class="d-flex align-items-center">
                            <?php if ($firm_data['registration_status'] === 'Active'): ?>
                                <span class="verified-badge">VERIFIED</span>
                            <?php endif; ?>
                            <i class="fas fa-info-circle info-icon ms-2" title="Company registration information"></i>
                            <i class="fas fa-edit edit-icon ms-2" id="editRegistrationBtn" onclick="editRegistrationDetails()" title="Edit registration details"></i>
                            <i class="fas fa-save save-icon ms-2" id="saveRegistrationBtn" onclick="saveRegistrationDetails()" style="display: none;" title="Save changes"></i>
                            <i class="fas fa-times cancel-icon ms-2" id="cancelRegistrationBtn" onclick="cancelEditRegistrationDetails()" style="display: none;" title="Cancel editing"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">RC Number <span class="required">*</span></label>
                        <input type="text" id="registrationNumberInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('registration_number')); ?>" readonly>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Company Type <span class="required">*</span></label>
                        <input type="text" id="companyTypeInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('company_type', 'Limited Liability')); ?>" readonly>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Ownership Type <span class="required">*</span></label>
                        <input type="text" id="ownershipTypeInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('ownership_type', 'Nigeria Owned')); ?>" readonly>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Registration Date <span class="required">*</span></label>
                        <input type="date" id="registrationDateInput" class="form-control" value="<?php echo getFirmData('registration_date') ? date('Y-m-d', strtotime(getFirmData('registration_date'))) : ''; ?>" readonly>
                    </div>
                </div>

                <!-- Company TIN -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3 class="section-title">Company TIN</h3>
                        <div class="d-flex align-items-center">
                            <?php if ($firm_data['registration_status'] === 'Active'): ?>
                                <span class="verified-badge">VERIFIED</span>
                            <?php endif; ?>
                            <i class="fas fa-info-circle info-icon ms-2" title="Tax identification information"></i>
                            <i class="fas fa-edit edit-icon ms-2" id="editTinBtn" onclick="editTinInfo()" title="Edit TIN information"></i>
                            <i class="fas fa-save save-icon ms-2" id="saveTinBtn" onclick="saveTinInfo()" style="display: none;" title="Save changes"></i>
                            <i class="fas fa-times cancel-icon ms-2" id="cancelTinBtn" onclick="cancelEditTinInfo()" style="display: none;" title="Cancel editing"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">FIRS Tax Identification Number <span class="required">*</span></label>
                        <input type="text" id="tinNumberInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('tin_number')); ?>" readonly>
                    </div>
                </div>

                <!-- Company Address -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3 class="section-title">Company Address</h3>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-edit edit-icon" id="editAddressBtn" onclick="editAddressInfo()" title="Edit address"></i>
                            <i class="fas fa-save save-icon ms-2" id="saveAddressBtn" onclick="saveAddressInfo()" style="display: none;" title="Save changes"></i>
                            <i class="fas fa-times cancel-icon ms-2" id="cancelAddressBtn" onclick="cancelEditAddressInfo()" style="display: none;" title="Cancel editing"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Street Address <span class="required">*</span></label>
                        <input type="text" id="addressInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('address')); ?>" readonly>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">City <span class="required">*</span></label>
                            <input type="text" id="cityInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('city')); ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">State <span class="required">*</span></label>
                            <select id="stateInput" class="form-control" disabled>
                                <option value="">Select State</option>
                                <option value="Abia" <?php echo (getFirmData('state') === 'Abia') ? 'selected' : ''; ?>>Abia</option>
                                <option value="Adamawa" <?php echo (getFirmData('state') === 'Adamawa') ? 'selected' : ''; ?>>Adamawa</option>
                                <option value="Akwa Ibom" <?php echo (getFirmData('state') === 'Akwa Ibom') ? 'selected' : ''; ?>>Akwa Ibom</option>
                                <option value="Anambra" <?php echo (getFirmData('state') === 'Anambra') ? 'selected' : ''; ?>>Anambra</option>
                                <option value="Bauchi" <?php echo (getFirmData('state') === 'Bauchi') ? 'selected' : ''; ?>>Bauchi</option>
                                <option value="Bayelsa" <?php echo (getFirmData('state') === 'Bayelsa') ? 'selected' : ''; ?>>Bayelsa</option>
                                <option value="Benue" <?php echo (getFirmData('state') === 'Benue') ? 'selected' : ''; ?>>Benue</option>
                                <option value="Borno" <?php echo (getFirmData('state') === 'Borno') ? 'selected' : ''; ?>>Borno</option>
                                <option value="Cross River" <?php echo (getFirmData('state') === 'Cross River') ? 'selected' : ''; ?>>Cross River</option>
                                <option value="Delta" <?php echo (getFirmData('state') === 'Delta') ? 'selected' : ''; ?>>Delta</option>
                                <option value="Ebonyi" <?php echo (getFirmData('state') === 'Ebonyi') ? 'selected' : ''; ?>>Ebonyi</option>
                                <option value="Edo" <?php echo (getFirmData('state') === 'Edo') ? 'selected' : ''; ?>>Edo</option>
                                <option value="Ekiti" <?php echo (getFirmData('state') === 'Ekiti') ? 'selected' : ''; ?>>Ekiti</option>
                                <option value="Enugu" <?php echo (getFirmData('state') === 'Enugu') ? 'selected' : ''; ?>>Enugu</option>
                                <option value="FCT" <?php echo (getFirmData('state') === 'FCT') ? 'selected' : ''; ?>>Federal Capital Territory</option>
                                <option value="Gombe" <?php echo (getFirmData('state') === 'Gombe') ? 'selected' : ''; ?>>Gombe</option>
                                <option value="Imo" <?php echo (getFirmData('state') === 'Imo') ? 'selected' : ''; ?>>Imo</option>
                                <option value="Jigawa" <?php echo (getFirmData('state') === 'Jigawa') ? 'selected' : ''; ?>>Jigawa</option>
                                <option value="Kaduna" <?php echo (getFirmData('state') === 'Kaduna') ? 'selected' : ''; ?>>Kaduna</option>
                                <option value="Kano" <?php echo (getFirmData('state') === 'Kano') ? 'selected' : ''; ?>>Kano</option>
                                <option value="Katsina" <?php echo (getFirmData('state') === 'Katsina') ? 'selected' : ''; ?>>Katsina</option>
                                <option value="Kebbi" <?php echo (getFirmData('state') === 'Kebbi') ? 'selected' : ''; ?>>Kebbi</option>
                                <option value="Kogi" <?php echo (getFirmData('state') === 'Kogi') ? 'selected' : ''; ?>>Kogi</option>
                                <option value="Kwara" <?php echo (getFirmData('state') === 'Kwara') ? 'selected' : ''; ?>>Kwara</option>
                                <option value="Lagos" <?php echo (getFirmData('state') === 'Lagos') ? 'selected' : ''; ?>>Lagos</option>
                                <option value="Nasarawa" <?php echo (getFirmData('state') === 'Nasarawa') ? 'selected' : ''; ?>>Nasarawa</option>
                                <option value="Niger" <?php echo (getFirmData('state') === 'Niger') ? 'selected' : ''; ?>>Niger</option>
                                <option value="Ogun" <?php echo (getFirmData('state') === 'Ogun') ? 'selected' : ''; ?>>Ogun</option>
                                <option value="Ondo" <?php echo (getFirmData('state') === 'Ondo') ? 'selected' : ''; ?>>Ondo</option>
                                <option value="Osun" <?php echo (getFirmData('state') === 'Osun') ? 'selected' : ''; ?>>Osun</option>
                                <option value="Oyo" <?php echo (getFirmData('state') === 'Oyo') ? 'selected' : ''; ?>>Oyo</option>
                                <option value="Plateau" <?php echo (getFirmData('state') === 'Plateau') ? 'selected' : ''; ?>>Plateau</option>
                                <option value="Rivers" <?php echo (getFirmData('state') === 'Rivers') ? 'selected' : ''; ?>>Rivers</option>
                                <option value="Sokoto" <?php echo (getFirmData('state') === 'Sokoto') ? 'selected' : ''; ?>>Sokoto</option>
                                <option value="Taraba" <?php echo (getFirmData('state') === 'Taraba') ? 'selected' : ''; ?>>Taraba</option>
                                <option value="Yobe" <?php echo (getFirmData('state') === 'Yobe') ? 'selected' : ''; ?>>Yobe</option>
                                <option value="Zamfara" <?php echo (getFirmData('state') === 'Zamfara') ? 'selected' : ''; ?>>Zamfara</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Postal Code</label>
                            <input type="text" id="postalCodeInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('postal_code')); ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Country</label>
                            <input type="text" id="countryInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('country', 'Nigeria')); ?>" readonly>
                        </div>
                    </div>
                </div>

                <!-- Company Contact Information -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3 class="section-title">Company Contact Information</h3>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-edit edit-icon" id="editContactBtn" onclick="editContactInfo()" title="Edit contact information"></i>
                            <i class="fas fa-save save-icon ms-2" id="saveContactBtn" onclick="saveContactInfo()" style="display: none;" title="Save changes"></i>
                            <i class="fas fa-times cancel-icon ms-2" id="cancelContactBtn" onclick="cancelEditContactInfo()" style="display: none;" title="Cancel editing"></i>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Email Address <span class="required">*</span></label>
                            <input type="email" id="emailInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('email')); ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Alternative Email Address</label>
                            <input type="email" id="alternativeEmailInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('alternative_email')); ?>" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Phone Number <span class="required">*</span></label>
                            <input type="tel" id="phoneInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('phone')); ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Alternative Phone Number</label>
                            <input type="tel" id="alternativePhoneInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('alternative_phone')); ?>" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Website</label>
                            <input type="url" id="websiteInput" class="form-control" value="<?php echo htmlspecialchars(getFirmData('website')); ?>" readonly>
                        </div>
                    </div>
                </div>

                <!-- Company CEO Details -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3 class="section-title">Company CEO Details</h3>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-edit edit-icon" title="Edit CEO details"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Name of CEO <span class="required">*</span></label>
                        <input type="text" class="form-control" value="--" readonly>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Email Address <span class="required">*</span></label>
                            <input type="email" class="form-control" value="--" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Phone Number <span class="required">*</span></label>
                            <input type="tel" class="form-control" value="--" readonly>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Logout confirmation
        function confirmLogout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = 'logout.php';
            }
        }

        // Sidebar navigation
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));
                    // Add active class to clicked link
                    this.classList.add('active');
                });
            });
        });

        // Mobile sidebar toggle (if needed)
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // Logo upload functionality
        function triggerLogoUpload() {
            document.getElementById('logoInput').click();
        }

        function handleLogoUpload(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];

                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPEG, PNG, or GIF)');
                    return;
                }

                // Validate file size (max 5MB)
                const maxSize = 5 * 1024 * 1024; // 5MB in bytes
                if (file.size > maxSize) {
                    alert('File size must be less than 5MB');
                    return;
                }

                // Show loading state
                const changeBtn = document.querySelector('.change-btn');
                const originalText = changeBtn.textContent;
                changeBtn.textContent = 'Uploading...';
                changeBtn.disabled = true;

                // Create form data
                const formData = new FormData();
                formData.append('logo', file);

                // Upload the file
                fetch('upload_logo.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the logo display
                        const logoContainer = document.querySelector('.logo-container');
                        const logoPlaceholder = logoContainer.querySelector('.logo-placeholder');
                        const existingLogo = logoContainer.querySelector('.company-logo');

                        if (existingLogo) {
                            existingLogo.src = '../' + data.logo_path + '?t=' + new Date().getTime(); // Add timestamp to prevent caching
                        } else if (logoPlaceholder) {
                            // Replace placeholder with actual logo
                            logoPlaceholder.outerHTML = `<img src="../${data.logo_path}?t=${new Date().getTime()}" alt="Company Logo" class="company-logo">`;
                        }

                        alert('Logo uploaded successfully!');
                    } else {
                        alert('Error uploading logo: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while uploading the logo. Please try again.');
                })
                .finally(() => {
                    // Reset button state
                    changeBtn.textContent = originalText;
                    changeBtn.disabled = false;
                    // Clear the input
                    input.value = '';
                });
            }
        }

        // Company name is read-only and cannot be edited
        // Name is set during registration and cannot be modified

        // Registration details editing functionality
        function editRegistrationDetails() {
            const fields = [
                'registrationNumberInput',
                'cacNumberInput',
                'companyTypeInput',
                'ownershipTypeInput',
                'incorporationDateInput',
                'registrationDateInput'
            ];

            fields.forEach(fieldId => {
                const input = document.getElementById(fieldId);
                if (input) {
                    input.readOnly = false;
                    input.classList.add('editable');
                }
            });

            // Toggle buttons
            document.getElementById('editRegistrationBtn').style.display = 'none';
            document.getElementById('saveRegistrationBtn').style.display = 'inline';
            document.getElementById('cancelRegistrationBtn').style.display = 'inline';
        }

        function saveRegistrationDetails() {
            // Show loading state
            const saveBtn = document.getElementById('saveRegistrationBtn');
            saveBtn.className = 'fas fa-spinner fa-spin save-icon ms-2';

            const formData = new FormData();
            formData.append('action', 'update_registration_details');
            formData.append('registration_number', document.getElementById('registrationNumberInput').value);
            formData.append('cac_number', document.getElementById('cacNumberInput').value);
            formData.append('company_type', document.getElementById('companyTypeInput').value);
            formData.append('ownership_type', document.getElementById('ownershipTypeInput').value);
            formData.append('incorporation_date', document.getElementById('incorporationDateInput').value);
            formData.append('registration_date', document.getElementById('registrationDateInput').value);
            formData.append('tin_number', ''); // TIN is handled separately

            fetch('update_firm_info.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the display values immediately
                    const fields = [
                        'registrationNumberInput',
                        'cacNumberInput',
                        'companyTypeInput',
                        'ownershipTypeInput',
                        'incorporationDateInput',
                        'registrationDateInput'
                    ];

                    // Keep the current values in the fields and make them readonly
                    fields.forEach(fieldId => {
                        const input = document.getElementById(fieldId);
                        if (input) {
                            input.readOnly = true;
                            input.classList.remove('editable');
                        }
                    });

                    // Reset button states
                    document.getElementById('editRegistrationBtn').style.display = 'inline';
                    document.getElementById('saveRegistrationBtn').style.display = 'none';
                    document.getElementById('cancelRegistrationBtn').style.display = 'none';

                    showVerificationFeedback('Registration details updated successfully!', data);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating registration details');
            })
            .finally(() => {
                saveBtn.className = 'fas fa-save save-icon ms-2';
            });
        }

        function cancelEditRegistrationDetails() {
            const fields = [
                'registrationNumberInput',
                'cacNumberInput',
                'companyTypeInput',
                'ownershipTypeInput',
                'incorporationDateInput',
                'registrationDateInput'
            ];

            fields.forEach(fieldId => {
                const input = document.getElementById(fieldId);
                if (input) {
                    input.readOnly = true;
                    input.classList.remove('editable');
                }
            });

            // Toggle buttons
            document.getElementById('editRegistrationBtn').style.display = 'inline';
            document.getElementById('saveRegistrationBtn').style.display = 'none';
            document.getElementById('cancelRegistrationBtn').style.display = 'none';

            // Only reload if user actually wants to cancel changes
            if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                location.reload(); // Reload to restore original values
            }
        }

        // Contact information editing functionality
        function editContactInfo() {
            const fields = ['emailInput', 'alternativeEmailInput', 'phoneInput', 'alternativePhoneInput', 'websiteInput'];

            fields.forEach(fieldId => {
                const input = document.getElementById(fieldId);
                if (input) {
                    input.readOnly = false;
                    input.classList.add('editable');
                }
            });

            document.getElementById('editContactBtn').style.display = 'none';
            document.getElementById('saveContactBtn').style.display = 'inline';
            document.getElementById('cancelContactBtn').style.display = 'inline';
        }

        function saveContactInfo() {
            const saveBtn = document.getElementById('saveContactBtn');
            saveBtn.className = 'fas fa-spinner fa-spin save-icon ms-2';

            const formData = new FormData();
            formData.append('action', 'update_contact_info');
            formData.append('email', document.getElementById('emailInput').value);
            formData.append('alternative_email', document.getElementById('alternativeEmailInput').value);
            formData.append('phone', document.getElementById('phoneInput').value);
            formData.append('alternative_phone', document.getElementById('alternativePhoneInput').value);
            formData.append('website', document.getElementById('websiteInput').value);

            fetch('update_firm_info.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const fields = ['emailInput', 'alternativeEmailInput', 'phoneInput', 'alternativePhoneInput', 'websiteInput'];
                    fields.forEach(fieldId => {
                        const input = document.getElementById(fieldId);
                        if (input) {
                            input.readOnly = true;
                            input.classList.remove('editable');
                        }
                    });

                    document.getElementById('editContactBtn').style.display = 'inline';
                    document.getElementById('saveContactBtn').style.display = 'none';
                    document.getElementById('cancelContactBtn').style.display = 'none';

                    showVerificationFeedback('Contact information updated successfully!', data);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating contact information');
            })
            .finally(() => {
                saveBtn.className = 'fas fa-save save-icon ms-2';
            });
        }

        function cancelEditContactInfo() {
            if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                location.reload();
            }
        }

        // Address information editing functionality
        function editAddressInfo() {
            const fields = ['addressInput', 'cityInput', 'postalCodeInput', 'countryInput'];
            const selectFields = ['stateInput'];

            fields.forEach(fieldId => {
                const input = document.getElementById(fieldId);
                if (input) {
                    input.readOnly = false;
                    input.classList.add('editable');
                }
            });

            selectFields.forEach(fieldId => {
                const select = document.getElementById(fieldId);
                if (select) {
                    select.disabled = false;
                    select.classList.add('editable');
                }
            });

            document.getElementById('editAddressBtn').style.display = 'none';
            document.getElementById('saveAddressBtn').style.display = 'inline';
            document.getElementById('cancelAddressBtn').style.display = 'inline';
        }

        function saveAddressInfo() {
            const saveBtn = document.getElementById('saveAddressBtn');
            saveBtn.className = 'fas fa-spinner fa-spin save-icon ms-2';

            const formData = new FormData();
            formData.append('action', 'update_address_info');
            formData.append('address', document.getElementById('addressInput').value);
            formData.append('city', document.getElementById('cityInput').value);
            formData.append('state', document.getElementById('stateInput').value);
            formData.append('postal_code', document.getElementById('postalCodeInput').value);
            formData.append('country', document.getElementById('countryInput').value);

            fetch('update_firm_info.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const fields = ['addressInput', 'cityInput', 'postalCodeInput', 'countryInput'];
                    const selectFields = ['stateInput'];

                    fields.forEach(fieldId => {
                        const input = document.getElementById(fieldId);
                        if (input) {
                            input.readOnly = true;
                            input.classList.remove('editable');
                        }
                    });

                    selectFields.forEach(fieldId => {
                        const select = document.getElementById(fieldId);
                        if (select) {
                            select.disabled = true;
                            select.classList.remove('editable');
                        }
                    });

                    document.getElementById('editAddressBtn').style.display = 'inline';
                    document.getElementById('saveAddressBtn').style.display = 'none';
                    document.getElementById('cancelAddressBtn').style.display = 'none';

                    showVerificationFeedback('Address information updated successfully!', data);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating address information');
            })
            .finally(() => {
                saveBtn.className = 'fas fa-save save-icon ms-2';
            });
        }

        function cancelEditAddressInfo() {
            if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                location.reload();
            }
        }

        // TIN editing functionality
        function editTinInfo() {
            const input = document.getElementById('tinNumberInput');
            if (input) {
                input.readOnly = false;
                input.classList.add('editable');
            }

            document.getElementById('editTinBtn').style.display = 'none';
            document.getElementById('saveTinBtn').style.display = 'inline';
            document.getElementById('cancelTinBtn').style.display = 'inline';
        }

        function saveTinInfo() {
            const saveBtn = document.getElementById('saveTinBtn');
            saveBtn.className = 'fas fa-spinner fa-spin save-icon ms-2';

            const formData = new FormData();
            formData.append('action', 'update_tin_info');
            formData.append('tin_number', document.getElementById('tinNumberInput').value);

            fetch('update_firm_info.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const input = document.getElementById('tinNumberInput');
                    if (input) {
                        input.readOnly = true;
                        input.classList.remove('editable');
                    }

                    document.getElementById('editTinBtn').style.display = 'inline';
                    document.getElementById('saveTinBtn').style.display = 'none';
                    document.getElementById('cancelTinBtn').style.display = 'none';

                    showVerificationFeedback('TIN information updated successfully!', data);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating TIN information');
            })
            .finally(() => {
                saveBtn.className = 'fas fa-save save-icon ms-2';
            });
        }

        function cancelEditTinInfo() {
            if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                location.reload();
            }
        }

        // Simple verification feedback function
        function showVerificationFeedback(baseMessage, data) {
            let message = baseMessage;

            if (data.verification_status === 'verified') {
                message += '\n\n🎉 CONGRATULATIONS! All required information is complete. Status changed to VERIFIED!';

                // Update the header status badge immediately
                updateHeaderStatusBadge('verified');

                alert(message);
                // Reload page to show updated status
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                message += '\n\nℹ️ ' + data.message;
                alert(message);
            }
        }

        // Function to update header status badge
        function updateHeaderStatusBadge(status) {
            const statusElement = document.getElementById('verificationStatus');
            if (statusElement) {
                if (status === 'verified') {
                    statusElement.innerHTML = '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>VERIFIED</span>';
                } else {
                    statusElement.innerHTML = '<span class="badge bg-warning text-dark"><i class="fas fa-clock me-1"></i>PENDING</span>';
                }
            }
        }
    </script>
</body>
</html>
