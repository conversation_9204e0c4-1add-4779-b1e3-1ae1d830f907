<?php
require_once '../includes/config.php';
require_once '../includes/audit_functions.php';
session_start();

// Check if firm is logged in
if (!isset($_SESSION['firm_id'])) {
    header("Location: login.php");
    exit();
}

$firm_id = $_SESSION['firm_id'];
$message = '';
$alert_class = '';

// Handle removing a practitioner
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'remove' && isset($_POST['association_id'])) {
    $association_id = $_POST['association_id'];
    
    // Get practitioner ID from association
    $get_sql = "SELECT practitioner_id FROM practitioner_firm_associations WHERE id = ? AND firm_id = ?";
    $get_stmt = $conn->prepare($get_sql);
    $get_stmt->bind_param("ii", $association_id, $firm_id);
    $get_stmt->execute();
    $result = $get_stmt->get_result();
    
    if ($result->num_rows > 0) {
        $association_data = $result->fetch_assoc();
        $practitioner_id = $association_data['practitioner_id'];
        
        // Update the association status to 'inactive'
        $sql = "UPDATE practitioner_firm_associations 
                SET status = 'inactive', end_date = NOW() 
                WHERE id = ? AND firm_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $association_id, $firm_id);
        
        if ($stmt->execute()) {
            // Log the event to audit trail
            log_association_event(
                $practitioner_id,
                $firm_id,
                $association_id,
                'manual_removal',
                'active',
                'inactive',
                'Practitioner manually removed by firm',
                'firm',
                $firm_id,
                $conn
            );
            
            $message = "Practitioner has been removed from your firm.";
            $alert_class = "alert-success";
        } else {
            $message = "Error removing practitioner: " . $conn->error;
            $alert_class = "alert-danger";
        }
    } else {
        $message = "Invalid association or you don't have permission to remove this practitioner.";
        $alert_class = "alert-danger";
    }
}

// Get all associated practitioners
$practitioners_sql = "SELECT a.id as association_id, a.start_date, a.status as association_status,
                     p.id as practitioner_id, p.full_name, p.email, p.phone, p.registration_number,
                     p.qualification, p.specialization, p.registration_status
                     FROM practitioner_firm_associations a
                     JOIN tax_practitioners p ON a.practitioner_id = p.id
                     WHERE a.firm_id = ? AND a.status = 'active'
                     ORDER BY p.full_name";
$practitioners_stmt = $conn->prepare($practitioners_sql);
$practitioners_stmt->bind_param("i", $firm_id);
$practitioners_stmt->execute();
$practitioners_result = $practitioners_stmt->get_result();

include '../includes/header.php';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users me-2"></i>Associated Tax Professionals</h2>
        <div>
            <a href="manage_association_requests.php" class="btn btn-primary me-2">
                <i class="fas fa-user-plus me-2"></i>Review Requests
            </a>
            <a href="dashboard.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
    
    <?php if (!empty($message)): ?>
    <div class="alert <?php echo $alert_class; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $alert_class == 'alert-success' ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-id-card me-2"></i>Your Associated Practitioners</h4>
                <div class="input-group" style="max-width: 300px;">
                    <span class="input-group-text bg-white">
                        <i class="fas fa-search text-primary"></i>
                    </span>
                    <input type="text" id="practitionerSearch" class="form-control" placeholder="Search practitioners...">
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if ($practitioners_result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="practitionersTable">
                        <thead class="table-light">
                            <tr>
                                <th>Name</th>
                                <th>Registration #</th>
                                <th>Status</th>
                                <th>Specialization</th>
                                <th>Contact</th>
                                <th>Association Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($practitioner = $practitioners_result->fetch_assoc()): ?>
                                <tr>
                                    <td class="fw-bold"><?php echo htmlspecialchars($practitioner['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($practitioner['registration_number']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $practitioner['registration_status'] == 'Active' ? 'success' : 
                                                ($practitioner['registration_status'] == 'Suspended' ? 'warning' : 
                                                    ($practitioner['registration_status'] == 'Revoked' ? 'danger' : 'secondary')); 
                                        ?>">
                                            <?php echo htmlspecialchars($practitioner['registration_status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($practitioner['specialization']); ?></td>
                                    <td>
                                        <div><i class="fas fa-envelope me-1 text-muted"></i> <?php echo htmlspecialchars($practitioner['email']); ?></div>
                                        <div><i class="fas fa-phone me-1 text-muted"></i> <?php echo htmlspecialchars($practitioner['phone']); ?></div>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($practitioner['start_date'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-primary btn-sm view-practitioner" 
                                                    data-bs-toggle="modal" data-bs-target="#practitionerModal"
                                                    data-id="<?php echo $practitioner['practitioner_id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($practitioner['full_name']); ?>"
                                                    data-reg="<?php echo htmlspecialchars($practitioner['registration_number']); ?>"
                                                    data-email="<?php echo htmlspecialchars($practitioner['email']); ?>"
                                                    data-phone="<?php echo htmlspecialchars($practitioner['phone']); ?>"
                                                    data-qualification="<?php echo htmlspecialchars($practitioner['qualification']); ?>"
                                                    data-specialization="<?php echo htmlspecialchars($practitioner['specialization']); ?>"
                                                    data-status="<?php echo htmlspecialchars($practitioner['registration_status']); ?>"
                                                    data-association-id="<?php echo $practitioner['association_id']; ?>">
                                                <i class="fas fa-eye me-1"></i>View
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users-slash fa-3x text-muted mb-3"></i>
                    <h5>No Associated Practitioners</h5>
                    <p class="text-muted">You don't have any tax professionals associated with your firm yet.</p>
                    <a href="manage_association_requests.php" class="btn btn-primary mt-2">
                        <i class="fas fa-user-plus me-2"></i>Review Association Requests
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i>About Practitioner Associations</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Benefits of Associated Practitioners</h5>
                    <ul>
                        <li>Expand your firm's service capabilities</li>
                        <li>Access specialized expertise in various tax domains</li>
                        <li>Increase your firm's capacity to handle more clients</li>
                        <li>Build a network of qualified professionals</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>Managing Your Practitioners</h5>
                    <ul>
                        <li>Regularly verify practitioners' registration status</li>
                        <li>Remove practitioners who no longer work with your firm</li>
                        <li>Monitor practitioners' professional development</li>
                        <li>Ensure compliance with regulatory requirements</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Practitioner Details Modal -->
<div class="modal fade" id="practitionerModal" tabindex="-1" aria-labelledby="practitionerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="practitionerModalLabel">Practitioner Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <div class="avatar-placeholder bg-light rounded-circle mx-auto mb-3" style="width: 120px; height: 120px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user fa-4x text-secondary"></i>
                        </div>
                        <h5 id="modal-practitioner-name">Practitioner Name</h5>
                        <span class="badge bg-success mb-2" id="modal-practitioner-status">Active</span>
                        <p class="badge bg-info" id="modal-practitioner-specialization">Specialization</p>
                    </div>
                    <div class="col-md-8">
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Registration Number:</div>
                            <div class="col-md-8 fw-bold" id="modal-practitioner-reg"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Email:</div>
                            <div class="col-md-8" id="modal-practitioner-email"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Phone:</div>
                            <div class="col-md-8" id="modal-practitioner-phone"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Qualification:</div>
                            <div class="col-md-8" id="modal-practitioner-qualification"></div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <form method="post" action="" id="removeForm">
                        <input type="hidden" name="action" value="remove">
                        <input type="hidden" name="association_id" id="modal-association-id" value="">
                        <button type="button" class="btn btn-danger" id="confirmRemoveBtn">
                            <i class="fas fa-user-minus me-1"></i>Remove from Firm
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">Confirm Removal</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove <span id="confirm-practitioner-name" class="fw-bold"></span> from your firm?</p>
                <p>This action will end the professional association between your firm and this practitioner.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="finalRemoveBtn">
                    <i class="fas fa-user-minus me-1"></i>Remove Practitioner
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('practitionerSearch');
    const table = document.getElementById('practitionersTable');
    
    if (searchInput && table) {
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        
        searchInput.addEventListener('keyup', function() {
            const query = this.value.toLowerCase();
            
            for (let i = 0; i < rows.length; i++) {
                const name = rows[i].getElementsByTagName('td')[0].textContent.toLowerCase();
                const regNum = rows[i].getElementsByTagName('td')[1].textContent.toLowerCase();
                const specialization = rows[i].getElementsByTagName('td')[3].textContent.toLowerCase();
                const contact = rows[i].getElementsByTagName('td')[4].textContent.toLowerCase();
                
                if (name.indexOf(query) > -1 || regNum.indexOf(query) > -1 || 
                    specialization.indexOf(query) > -1 || contact.indexOf(query) > -1) {
                    rows[i].style.display = "";
                } else {
                    rows[i].style.display = "none";
                }
            }
        });
    }
    
    // View practitioner modal
    const viewButtons = document.querySelectorAll('.view-practitioner');
    const confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const reg = this.getAttribute('data-reg');
            const email = this.getAttribute('data-email');
            const phone = this.getAttribute('data-phone');
            const qualification = this.getAttribute('data-qualification');
            const specialization = this.getAttribute('data-specialization');
            const status = this.getAttribute('data-status');
            const associationId = this.getAttribute('data-association-id');
            
            document.getElementById('modal-practitioner-name').textContent = name;
            document.getElementById('modal-practitioner-reg').textContent = reg;
            document.getElementById('modal-practitioner-email').textContent = email;
            document.getElementById('modal-practitioner-phone').textContent = phone;
            document.getElementById('modal-practitioner-qualification').textContent = qualification;
            document.getElementById('modal-practitioner-specialization').textContent = specialization;
            document.getElementById('modal-association-id').value = associationId;
            
            // Set status badge color
            const statusBadge = document.getElementById('modal-practitioner-status');
            statusBadge.textContent = status;
            statusBadge.className = 'badge mb-2 bg-' + 
                (status === 'Active' ? 'success' : 
                 (status === 'Suspended' ? 'warning' : 
                  (status === 'Revoked' ? 'danger' : 'secondary')));
            
            // Set confirmation modal data
            document.getElementById('confirm-practitioner-name').textContent = name;
        });
    });
    
    // Handle remove confirmation
    const confirmRemoveBtn = document.getElementById('confirmRemoveBtn');
    const finalRemoveBtn = document.getElementById('finalRemoveBtn');
    const removeForm = document.getElementById('removeForm');
    
    if (confirmRemoveBtn) {
        confirmRemoveBtn.addEventListener('click', function() {
            const practitionerModal = bootstrap.Modal.getInstance(document.getElementById('practitionerModal'));
            practitionerModal.hide();
            confirmationModal.show();
        });
    }
    
    if (finalRemoveBtn) {
        finalRemoveBtn.addEventListener('click', function() {
            removeForm.submit();
        });
    }
});
</script>

<?php include '../includes/footer.php'; ?>
