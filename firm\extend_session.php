<?php
/**
 * Session Extension Endpoint for Firm Portal
 * Handles AJAX requests to extend user sessions
 */

header('Content-Type: application/json');

// Include required files
require_once '../includes/config.php';
require_once 'includes/session_manager.php';

// Initialize session manager
$session_manager = initFirmSession();

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit();
}

// Check if firm is logged in
if (!$session_manager->isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Not authenticated'
    ]);
    exit();
}

try {
    // Extend the session
    $extended = $session_manager->extendSession();
    
    if ($extended) {
        $session_info = $session_manager->getSessionInfo();
        
        echo json_encode([
            'success' => true,
            'message' => 'Session extended successfully',
            'remaining_time' => $session_info['remaining_time'],
            'extended_at' => date('Y-m-d H:i:s')
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to extend session'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}
?>
