<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>🗑️ Delete Functionality Test</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Handle AJAX delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax_delete'])) {
    header('Content-Type: application/json');
    
    $input = json_decode(file_get_contents('php://input'), true);
    $document_type = $input['document_type'] ?? '';
    
    if (empty($document_type)) {
        echo json_encode(['success' => false, 'message' => 'Document type not provided']);
        exit();
    }
    
    // Get document info
    $select_sql = "SELECT id, file_name, file_path FROM firm_documents WHERE firm_id = ? AND document_type = ?";
    $select_stmt = $conn->prepare($select_sql);
    $select_stmt->bind_param("is", $firm_id, $document_type);
    $select_stmt->execute();
    $result = $select_stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Document not found']);
        exit();
    }
    
    $document = $result->fetch_assoc();
    
    // Delete file if exists
    if (file_exists($document['file_path'])) {
        unlink($document['file_path']);
    }
    
    // Delete from database
    $delete_sql = "DELETE FROM firm_documents WHERE id = ? AND firm_id = ?";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param("ii", $document['id'], $firm_id);
    
    if ($delete_stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Document deleted successfully',
            'document_type' => $document_type
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to delete from database']);
    }
    
    exit();
}

// Show current documents
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY document_type";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<h3>Current Documents:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Type</th><th>File Name</th><th>Status</th><th>Upload Date</th><th>Actions</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td>";
        echo "<button onclick='testDelete(\"" . $doc['document_type'] . "\")' style='background: #dc3545; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>Test Delete</button>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>No documents found</p>";
    echo "<p><a href='fix_persistence_final.php'>Create test documents</a></p>";
}

$conn->close();
?>

<div id="result" style="margin: 20px 0; padding: 15px; border-radius: 5px; display: none;"></div>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
    <h3>🧪 Delete Test Instructions:</h3>
    <ol>
        <li><strong>Click "Test Delete"</strong> on any document above</li>
        <li><strong>Check the result</strong> - should show success message</li>
        <li><strong>Refresh page</strong> - document should be gone</li>
        <li><strong>Go to dashboard</strong> - test delete there too</li>
    </ol>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="fix_persistence_final.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Create Test Documents</a>
    </p>
</div>

<script>
function testDelete(documentType) {
    const resultDiv = document.getElementById('result');
    
    // Show loading
    resultDiv.style.display = 'block';
    resultDiv.style.backgroundColor = '#d1ecf1';
    resultDiv.style.color = '#0c5460';
    resultDiv.innerHTML = '⏳ Deleting ' + documentType + '...';
    
    fetch('test_delete.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            document_type: documentType,
            ajax_delete: true
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.style.backgroundColor = '#d4edda';
            resultDiv.style.color = '#155724';
            resultDiv.innerHTML = '✅ ' + data.message;
            
            // Refresh page after 2 seconds
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            resultDiv.style.backgroundColor = '#f8d7da';
            resultDiv.style.color = '#721c24';
            resultDiv.innerHTML = '❌ ' + data.message;
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        resultDiv.style.backgroundColor = '#f8d7da';
        resultDiv.style.color = '#721c24';
        resultDiv.innerHTML = '❌ Error: ' + error.message;
    });
}
</script>
