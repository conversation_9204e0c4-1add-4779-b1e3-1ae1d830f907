<?php
session_start();
require_once '../config/db_connect.php';

echo "<h2>Debug Information</h2>";

// Check session
echo "<h3>Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check if user is logged in as a firm
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>ERROR: No firm_id in session. User not logged in as firm.</p>";
    echo "<p><a href='login.php'>Go to Login</a></p>";
    exit;
} else {
    echo "<p style='color: green;'>SUCCESS: Firm logged in with ID: " . $_SESSION['firm_id'] . "</p>";
}

$firm_id = $_SESSION['firm_id'];

// Test database connection
try {
    $firm_sql = "SELECT * FROM tax_firms WHERE id = ?";
    $firm_stmt = $conn->prepare($firm_sql);
    $firm_stmt->bind_param("i", $firm_id);
    $firm_stmt->execute();
    $firm_result = $firm_stmt->get_result();
    $firm_data = $firm_result->fetch_assoc();
    $firm_stmt->close();
    
    if ($firm_data) {
        echo "<p style='color: green;'>SUCCESS: Firm data found in database</p>";
        echo "<pre>";
        print_r($firm_data);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>ERROR: No firm data found for ID: $firm_id</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>DATABASE ERROR: " . $e->getMessage() . "</p>";
}

// Test firm_documents table
try {
    $docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ?";
    $docs_stmt = $conn->prepare($docs_sql);
    $docs_stmt->bind_param("i", $firm_id);
    $docs_stmt->execute();
    $docs_result = $docs_stmt->get_result();
    
    echo "<h3>Uploaded Documents:</h3>";
    if ($docs_result->num_rows > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Document Type</th><th>Filename</th><th>Upload Date</th></tr>";
        while ($doc = $docs_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $doc['id'] . "</td>";
            echo "<td>" . $doc['document_type'] . "</td>";
            echo "<td>" . $doc['original_filename'] . "</td>";
            echo "<td>" . $doc['uploaded_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No documents uploaded yet.</p>";
    }
    $docs_stmt->close();
} catch (Exception $e) {
    echo "<p style='color: red;'>DOCUMENTS TABLE ERROR: " . $e->getMessage() . "</p>";
}

echo "<h3>Test Links:</h3>";
echo "<p><a href='supporting_documents.php'>Go to Supporting Documents</a></p>";
echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>";

$conn->close();
?>
