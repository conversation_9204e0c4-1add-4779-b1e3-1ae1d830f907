<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_ids = $_POST['selected_firms'] ?? [];
    
    if (!empty($selected_ids) && in_array($action, ['approve', 'reject', 'suspend'])) {
        $success_count = 0;
        $error_count = 0;
        
        foreach ($selected_ids as $id) {
            $new_status = '';
            switch ($action) {
                case 'approve':
                    $new_status = 'Active';
                    break;
                case 'reject':
                    $new_status = 'Rejected';
                    break;
                case 'suspend':
                    $new_status = 'Suspended';
                    break;
            }
            
            $update_sql = "UPDATE tax_firms SET registration_status = ? WHERE id = ?";
            $stmt = $conn->prepare($update_sql);
            
            if ($stmt && $stmt->bind_param("si", $new_status, $id) && $stmt->execute()) {
                $success_count++;
                
                // Log the action
                if (function_exists('log_admin_action')) {
                    log_admin_action($_SESSION['admin_id'], 'bulk_' . $action . '_firm', "Bulk {$action}ed firm ID: {$id}");
                }
            } else {
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            $_SESSION['alert'] = [
                'type' => 'success',
                'message' => "Successfully {$action}ed {$success_count} firm(s)."
            ];
        }
        
        if ($error_count > 0) {
            $_SESSION['alert'] = [
                'type' => 'warning',
                'message' => "Failed to process {$error_count} firm(s)."
            ];
        }
        
        header("Location: manage_firms.php");
        exit();
    }
}

// Build query filters
$status_filter = $_GET['status'] ?? 'all';
$search_term = $_GET['search'] ?? '';
$business_type_filter = $_GET['business_type'] ?? '';

$where_conditions = [];
$params = [];
$param_types = "";

// Filter by status
if ($status_filter !== 'all') {
    $where_conditions[] = "registration_status = ?";
    $params[] = $status_filter;
    $param_types .= "s";
}

// Search functionality
if (!empty($search_term)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR registration_number LIKE ?)";
    $search_param = "%{$search_term}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= "sss";
}

// Filter by business type
if (!empty($business_type_filter)) {
    $where_conditions[] = "business_type = ?";
    $params[] = $business_type_filter;
    $param_types .= "s";
}

$where_clause = !empty($where_conditions) ? implode(" AND ", $where_conditions) : "1=1";

// Pagination
$per_page = 20;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $per_page;

// Count total records
$count_sql = "SELECT COUNT(*) as total FROM tax_firms WHERE {$where_clause}";
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// Get firms
$sql = "SELECT id, name, email, phone, registration_number, tax_id, business_type, 
               registration_status, registration_date
        FROM tax_firms 
        WHERE {$where_clause}
        ORDER BY registration_date DESC 
        LIMIT ? OFFSET ?";

$stmt = $conn->prepare($sql);
$params[] = $per_page;
$params[] = $offset;
$param_types .= "ii";
$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

$page_title = "Manage Tax Firms";
include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Manage Tax Firms</h1>
                    <p class="text-muted">Review and manage registered tax firms</p>
                </div>
                <div>
                    <span class="badge bg-info fs-6"><?php echo $total_records; ?> 
                        <?php echo $status_filter === 'all' ? 'Total' : ucfirst($status_filter); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (isset($_SESSION['alert'])): ?>
    <div class="alert alert-<?php echo $_SESSION['alert']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['alert']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['alert']); endif; ?>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Registration Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Statuses</option>
                            <option value="Pending" <?php echo $status_filter === 'Pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="Active" <?php echo $status_filter === 'Active' ? 'selected' : ''; ?>>Active</option>
                            <option value="Suspended" <?php echo $status_filter === 'Suspended' ? 'selected' : ''; ?>>Suspended</option>
                            <option value="Rejected" <?php echo $status_filter === 'Rejected' ? 'selected' : ''; ?>>Rejected</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="business_type" class="form-label">Business Type</label>
                        <select class="form-select" id="business_type" name="business_type">
                            <option value="">All Types</option>
                            <option value="Corporation" <?php echo $business_type_filter === 'Corporation' ? 'selected' : ''; ?>>Corporation</option>
                            <option value="Partnership" <?php echo $business_type_filter === 'Partnership' ? 'selected' : ''; ?>>Partnership</option>
                            <option value="LLC" <?php echo $business_type_filter === 'LLC' ? 'selected' : ''; ?>>LLC</option>
                            <option value="Sole Proprietorship" <?php echo $business_type_filter === 'Sole Proprietorship' ? 'selected' : ''; ?>>Sole Proprietorship</option>
                            <option value="Government" <?php echo $business_type_filter === 'Government' ? 'selected' : ''; ?>>Government</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($search_term); ?>" 
                               placeholder="Search by name, email, or registration number">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Firms Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>Registered Firms
                </h5>
                <div>
                    <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('approve')" id="bulkApproveBtn" disabled>
                        <i class="fas fa-check me-1"></i>Approve Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('suspend')" id="bulkSuspendBtn" disabled>
                        <i class="fas fa-pause me-1"></i>Suspend Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('reject')" id="bulkRejectBtn" disabled>
                        <i class="fas fa-times me-1"></i>Reject Selected
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if ($result->num_rows > 0): ?>
            <form id="bulkActionForm" method="POST">
                <input type="hidden" name="bulk_action" id="bulkActionInput">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Firm Details</th>
                                <th>Contact Information</th>
                                <th>Business Info</th>
                                <th>Status</th>
                                <th>Registration Date</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" name="selected_firms[]" value="<?php echo $row['id']; ?>" 
                                           class="form-check-input firm-checkbox">
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($row['name']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-id-card me-1"></i><?php echo htmlspecialchars($row['registration_number']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($row['email']); ?>
                                        <br>
                                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($row['phone'] ?? 'N/A'); ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <small class="text-muted">Type:</small> <?php echo htmlspecialchars($row['business_type'] ?? 'N/A'); ?>
                                        <br>
                                        <small class="text-muted">Tax ID:</small> <?php echo htmlspecialchars($row['tax_id'] ?? 'N/A'); ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $status_class = 'secondary';
                                    switch ($row['registration_status']) {
                                        case 'Active':
                                            $status_class = 'success';
                                            break;
                                        case 'Pending':
                                            $status_class = 'warning';
                                            break;
                                        case 'Suspended':
                                            $status_class = 'info';
                                            break;
                                        case 'Rejected':
                                            $status_class = 'danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?php echo $status_class; ?>">
                                        <?php echo htmlspecialchars($row['registration_status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo date('M d, Y', strtotime($row['registration_date'])); ?>
                                    <br>
                                    <small class="text-muted"><?php echo date('H:i', strtotime($row['registration_date'])); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="view_firm.php?id=<?php echo $row['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit_firm.php?id=<?php echo $row['id']; ?>" 
                                           class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </form>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Firms Found</h5>
                <p class="text-muted">No firms match your current filter criteria.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <nav aria-label="Firms pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Previous</a>
            </li>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
            </li>
            <?php endfor; ?>
            
            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Next</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<script>
// Bulk action functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.firm-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActionButtons();
});

document.querySelectorAll('.firm-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActionButtons);
});

function updateBulkActionButtons() {
    const checkedBoxes = document.querySelectorAll('.firm-checkbox:checked');
    const bulkButtons = ['bulkApproveBtn', 'bulkSuspendBtn', 'bulkRejectBtn'];
    
    bulkButtons.forEach(buttonId => {
        document.getElementById(buttonId).disabled = checkedBoxes.length === 0;
    });
}

function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.firm-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        alert('Please select at least one firm.');
        return;
    }
    
    const actionText = action.charAt(0).toUpperCase() + action.slice(1);
    if (confirm(`Are you sure you want to ${action} ${checkedBoxes.length} selected firm(s)?`)) {
        document.getElementById('bulkActionInput').value = action;
        document.getElementById('bulkActionForm').submit();
    }
}

// Auto-hide alerts
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('show')) {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }
    });
}, 5000);
</script>

<?php include '../includes/admin_footer.php'; ?>
