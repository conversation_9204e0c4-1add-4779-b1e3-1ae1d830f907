<?php
require_once '../includes/config.php';

echo "<h2>Create Test Firm</h2>";

// Check if we already have firms
$check_sql = "SELECT COUNT(*) as count FROM tax_firms";
$check_result = $conn->query($check_sql);
$count = $check_result->fetch_assoc()['count'];

echo "<p>Current number of firms in database: <strong>$count</strong></p>";

if ($count == 0) {
    echo "<p style='color: orange;'>No firms found. Creating a test firm...</p>";
    
    // Create a test firm
    $name = "Test Tax Consulting Firm";
    $email = "<EMAIL>";
    $phone = "***********";
    $address = "123 Business District, Lagos";
    $password = password_hash("password123", PASSWORD_DEFAULT);
    
    // Generate registration number
    $prefix = "TF";
    $year = date("Y");
    $random = mt_rand(1000, 9999);
    $registration_number = $prefix . $year . $random;
    
    $insert_sql = "INSERT INTO tax_firms (name, email, phone, address, password, registration_number, registration_status) VALUES (?, ?, ?, ?, ?, ?, 'Active')";
    $stmt = $conn->prepare($insert_sql);
    $stmt->bind_param("ssssss", $name, $email, $phone, $address, $password, $registration_number);
    
    if ($stmt->execute()) {
        $new_firm_id = $conn->insert_id;
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p style='color: #155724; margin: 0;'>✅ Test firm created successfully!</p>";
        echo "<p style='margin: 5px 0 0 0;'>";
        echo "<strong>Firm ID:</strong> $new_firm_id<br>";
        echo "<strong>Name:</strong> $name<br>";
        echo "<strong>Email:</strong> $email<br>";
        echo "<strong>Registration Number:</strong> $registration_number<br>";
        echo "<strong>Password:</strong> password123";
        echo "</p>";
        echo "</div>";
        
        // Start session and login as this firm
        session_start();
        $_SESSION['firm_id'] = $new_firm_id;
        $_SESSION['firm_name'] = $name;
        $_SESSION['firm_email'] = $email;
        
        echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p style='color: #004085; margin: 0;'>🔐 Automatically logged in as this firm!</p>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to create test firm: " . $stmt->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✅ Firms already exist in database.</p>";
    
    // Show existing firms
    $firms_sql = "SELECT id, name, email, registration_number, registration_status FROM tax_firms ORDER BY id";
    $firms_result = $conn->query($firms_sql);
    
    echo "<h3>Existing Firms:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Registration #</th><th>Status</th><th>Action</th></tr>";
    
    while ($firm = $firms_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $firm['id'] . "</td>";
        echo "<td>" . htmlspecialchars($firm['name']) . "</td>";
        echo "<td>" . htmlspecialchars($firm['email']) . "</td>";
        echo "<td>" . htmlspecialchars($firm['registration_number']) . "</td>";
        echo "<td>" . htmlspecialchars($firm['registration_status']) . "</td>";
        echo "<td><a href='?login_as=" . $firm['id'] . "'>Login as this firm</a></td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Handle login as specific firm
if (isset($_GET['login_as'])) {
    $login_firm_id = (int)$_GET['login_as'];
    
    $login_sql = "SELECT * FROM tax_firms WHERE id = ?";
    $login_stmt = $conn->prepare($login_sql);
    $login_stmt->bind_param("i", $login_firm_id);
    $login_stmt->execute();
    $login_result = $login_stmt->get_result();
    $login_firm = $login_result->fetch_assoc();
    
    if ($login_firm) {
        session_start();
        $_SESSION['firm_id'] = $login_firm['id'];
        $_SESSION['firm_name'] = $login_firm['name'];
        $_SESSION['firm_email'] = $login_firm['email'];
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p style='color: #155724; margin: 0;'>✅ Successfully logged in as: " . htmlspecialchars($login_firm['name']) . "</p>";
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ Firm not found!</p>";
    }
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
    <h3>Next Steps:</h3>
    <p><a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Go to Dashboard</a></p>
    <p><a href="debug_session.php" style="background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Check Session</a></p>
    <p><a href="test_profile_data.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Test Profile Data</a></p>
</div>
