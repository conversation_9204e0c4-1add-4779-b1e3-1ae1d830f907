<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Fixing Empty Document Status</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Get all documents for this firm, including those with NULL or empty status
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY document_type";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

echo "<h3>Current Document Status Issues:</h3>";

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Document Type</th><th>File Name</th><th>Current Status</th><th>File Exists</th><th>Correct Status</th><th>Action</th></tr>";
    
    $issues_found = 0;
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        $current_status = $doc['status'] ?? 'NULL';
        $current_status_display = empty($current_status) ? '(EMPTY)' : $current_status;
        
        // Determine correct status
        $correct_status = $file_exists ? 'uploaded' : 'pending';
        $needs_fix = (empty($doc['status']) || $doc['status'] !== $correct_status);
        
        if ($needs_fix) {
            $issues_found++;
        }
        
        $row_color = $needs_fix ? 'background-color: #fff3cd;' : '';
        
        echo "<tr style='$row_color'>";
        echo "<td>" . $doc['id'] . "</td>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($current_status_display) . "</td>";
        echo "<td style='color: " . ($file_exists ? 'green' : 'red') . ";'>" . ($file_exists ? '✅ Yes' : '❌ No') . "</td>";
        echo "<td>" . $correct_status . "</td>";
        echo "<td>";
        
        if ($needs_fix) {
            echo "<a href='?fix_id=" . $doc['id'] . "&new_status=" . $correct_status . "' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Fix</a>";
        } else {
            echo "<span style='color: green;'>✅ OK</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($issues_found > 0) {
        echo "<div style='background: #fff3cd; padding: 15px; margin: 15px 0; border-radius: 5px; border: 1px solid #ffeaa7;'>";
        echo "<h4 style='color: #856404;'>⚠️ Issues Found: $issues_found</h4>";
        echo "<p style='color: #856404;'>Some documents have empty or incorrect status values.</p>";
        echo "<a href='?fix_all=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Fix All Issues</a>";
        echo "<a href='?delete_empty=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Delete Empty Records</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; margin: 15px 0; border-radius: 5px; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724;'>✅ All Status Values Are Correct!</h4>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: orange;'>⚠️ No documents found for this firm</p>";
}

// Handle individual fix
if (isset($_GET['fix_id']) && isset($_GET['new_status'])) {
    $fix_id = (int)$_GET['fix_id'];
    $new_status = $_GET['new_status'];
    
    // Validate new status
    if (in_array($new_status, ['uploaded', 'pending', 'rejected'])) {
        $update_sql = "UPDATE firm_documents SET status = ? WHERE id = ? AND firm_id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("sii", $new_status, $fix_id, $firm_id);
        
        if ($update_stmt->execute()) {
            echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px; color: #155724;'>";
            echo "✅ Successfully updated document status to '$new_status'";
            echo "</div>";
            echo "<script>setTimeout(() => window.location.href = 'fix_empty_status.php', 1500);</script>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; color: #721c24;'>";
            echo "❌ Failed to update status: " . $update_stmt->error;
            echo "</div>";
        }
    }
}

// Handle fix all
if (isset($_GET['fix_all'])) {
    echo "<h3>Fixing All Status Issues:</h3>";
    
    $fix_all_sql = "SELECT id, document_type, file_path, status FROM firm_documents WHERE firm_id = ?";
    $fix_all_stmt = $conn->prepare($fix_all_sql);
    $fix_all_stmt->bind_param("i", $firm_id);
    $fix_all_stmt->execute();
    $fix_all_result = $fix_all_stmt->get_result();
    
    $fixed_count = 0;
    
    while ($doc = $fix_all_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        $current_status = $doc['status'];
        $correct_status = $file_exists ? 'uploaded' : 'pending';
        
        if (empty($current_status) || $current_status !== $correct_status) {
            $update_sql = "UPDATE firm_documents SET status = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("si", $correct_status, $doc['id']);
            
            if ($update_stmt->execute()) {
                echo "<p style='color: green;'>✅ Fixed: " . $doc['document_type'] . " → $correct_status</p>";
                $fixed_count++;
            } else {
                echo "<p style='color: red;'>❌ Failed to fix: " . $doc['document_type'] . "</p>";
            }
        }
    }
    
    echo "<div style='background: #d4edda; padding: 15px; margin: 15px 0; border-radius: 5px;'>";
    echo "<h4 style='color: #155724;'>✅ Fix Complete!</h4>";
    echo "<p style='color: #155724;'>Fixed $fixed_count document status issues.</p>";
    echo "<p><a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Go to Dashboard</a></p>";
    echo "</div>";
}

// Handle delete empty records
if (isset($_GET['delete_empty'])) {
    echo "<h3>Deleting Empty Records:</h3>";
    
    $delete_sql = "DELETE FROM firm_documents WHERE firm_id = ? AND (status IS NULL OR status = '')";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param("i", $firm_id);
    
    if ($delete_stmt->execute()) {
        $deleted_count = $delete_stmt->affected_rows;
        echo "<div style='background: #fff3cd; padding: 15px; margin: 15px 0; border-radius: 5px;'>";
        echo "<h4 style='color: #856404;'>🗑️ Deletion Complete!</h4>";
        echo "<p style='color: #856404;'>Deleted $deleted_count empty document records.</p>";
        echo "<p><a href='fix_empty_status.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Refresh Status</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; color: #721c24;'>";
        echo "❌ Failed to delete empty records: " . $delete_stmt->error;
        echo "</div>";
    }
}

// Show database schema for status column
echo "<h3>Database Schema Check:</h3>";
$schema_sql = "SHOW COLUMNS FROM firm_documents WHERE Field = 'status'";
$schema_result = $conn->query($schema_sql);

if ($schema_result && $schema_result->num_rows > 0) {
    $schema = $schema_result->fetch_assoc();
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    echo "<tr>";
    echo "<td>" . $schema['Field'] . "</td>";
    echo "<td>" . $schema['Type'] . "</td>";
    echo "<td>" . $schema['Null'] . "</td>";
    echo "<td>" . $schema['Key'] . "</td>";
    echo "<td>" . ($schema['Default'] ?? 'NULL') . "</td>";
    echo "<td>" . $schema['Extra'] . "</td>";
    echo "</tr>";
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ Status column not found in firm_documents table</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
    <h3>Quick Actions:</h3>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="check_documents.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Check Documents</a>
        <a href="create_test_documents.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Create Test Docs</a>
    </p>
</div>
