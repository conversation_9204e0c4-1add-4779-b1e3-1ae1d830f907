<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../../includes/config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized - No firm_id in session']);
    exit();
}

$firm_id = $_SESSION['firm_id'];
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getShareholders($conn, $firm_id);
        break;
    case 'POST':
        addShareholder($conn, $firm_id);
        break;
    case 'PUT':
        updateShareholder($conn, $firm_id);
        break;
    case 'DELETE':
        deleteShareholder($conn, $firm_id);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

function getShareholders($conn, $firm_id) {
    $status = $_GET['status'] ?? 'all';
    
    $sql = "SELECT * FROM firm_shareholders WHERE firm_id = ?";
    $params = [$firm_id];
    $types = "i";
    
    if ($status !== 'all') {
        $sql .= " AND status = ?";
        $params[] = $status;
        $types .= "s";
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $shareholders = [];
    while ($row = $result->fetch_assoc()) {
        $shareholders[] = $row;
    }
    
    // Get counts for each status
    $counts = [];
    $status_types = ['verified', 'pending', 'rejected'];
    
    foreach ($status_types as $status_type) {
        $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM firm_shareholders WHERE firm_id = ? AND status = ?");
        $count_stmt->bind_param("is", $firm_id, $status_type);
        $count_stmt->execute();
        $count_result = $count_stmt->get_result();
        $count_row = $count_result->fetch_assoc();
        $counts[$status_type] = $count_row['count'];
    }
    
    echo json_encode([
        'shareholders' => $shareholders,
        'counts' => $counts
    ]);
}

function addShareholder($conn, $firm_id) {
    $raw_input = file_get_contents('php://input');
    $input = json_decode($raw_input, true);

    // Debug logging
    error_log("Raw input: " . $raw_input);
    error_log("Decoded input: " . print_r($input, true));
    error_log("Firm ID: " . $firm_id);

    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON: ' . json_last_error_msg()]);
        return;
    }

    $required_fields = ['name', 'number_of_shares', 'share_percentage'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $sql = "INSERT INTO firm_shareholders (firm_id, name, email, phone, address, number_of_shares, share_percentage, share_class, appointment_date, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isssidsss",
        $firm_id,
        $input['name'],
        $input['email'] ?? null,
        $input['phone'] ?? null,
        $input['address'] ?? null,
        $input['number_of_shares'],
        $input['share_percentage'],
        $input['share_class'] ?? 'Ordinary',
        $input['appointment_date'] ?? date('Y-m-d'),
        $input['status'] ?? 'pending'
    );
    
    if ($stmt->execute()) {
        $new_id = $conn->insert_id;
        echo json_encode(['success' => true, 'id' => $new_id, 'message' => 'Shareholder added successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to add shareholder: ' . $stmt->error]);
    }
}

function updateShareholder($conn, $firm_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Shareholder ID is required']);
        return;
    }
    
    $sql = "UPDATE firm_shareholders SET name = ?, email = ?, phone = ?, address = ?, number_of_shares = ?, share_percentage = ?, share_class = ?, appointment_date = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND firm_id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssidsssii",
        $input['name'],
        $input['email'],
        $input['phone'],
        $input['address'],
        $input['number_of_shares'],
        $input['share_percentage'],
        $input['share_class'],
        $input['appointment_date'],
        $input['status'],
        $input['id'],
        $firm_id
    );
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Shareholder updated successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Shareholder not found']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update shareholder']);
    }
}

function deleteShareholder($conn, $firm_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Shareholder ID is required']);
        return;
    }
    
    $sql = "DELETE FROM firm_shareholders WHERE id = ? AND firm_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $input['id'], $firm_id);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Shareholder deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Shareholder not found']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete shareholder']);
    }
}

$conn->close();
?>
