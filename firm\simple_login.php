<?php
session_start();
require_once '../config.php';

// Check if already logged in
if (isset($_SESSION['firm_id'])) {
    header("Location: dashboard.php");
    exit();
}

$error = '';
$success_message = '';

// Process login form
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    echo "<h3>Login Debug Information:</h3>";
    echo "<p>Email entered: " . htmlspecialchars($email) . "</p>";
    echo "<p>Password entered: " . htmlspecialchars($password) . "</p>";
    
    // Validate input
    if (empty($email) || empty($password)) {
        $error = "Please enter both email and password.";
        echo "<p style='color: red;'>Error: Empty fields</p>";
    } else {
        // Simple hardcoded check first
        if ($email === "<EMAIL>" && $password === "password123") {
            echo "<p style='color: green;'>Hardcoded credentials match!</p>";
            
            // Set session variables
            $_SESSION['firm_id'] = 1;
            $_SESSION['firm_name'] = "Test Tax Consulting Firm";
            $_SESSION['firm_email'] = $email;
            
            echo "<p style='color: green;'>Session set successfully!</p>";
            echo "<p>Redirecting to dashboard...</p>";
            
            // Redirect to dashboard
            header("Location: dashboard.php");
            exit();
        } else {
            // Check database
            echo "<p>Checking database...</p>";
            
            $sql = "SELECT id, name, email, password, registration_status FROM tax_firms WHERE email = ?";
            $stmt = $conn->prepare($sql);
            
            if ($stmt) {
                $stmt->bind_param("s", $email);
                $stmt->execute();
                $result = $stmt->get_result();
                
                echo "<p>Database query executed. Rows found: " . $result->num_rows . "</p>";
                
                if ($result->num_rows === 1) {
                    $firm = $result->fetch_assoc();
                    echo "<p>Firm found: " . htmlspecialchars($firm['name']) . "</p>";
                    echo "<p>Status: " . $firm['registration_status'] . "</p>";
                    
                    // Try password verification
                    $password_correct = password_verify($password, $firm['password']);
                    echo "<p>Password verification: " . ($password_correct ? "SUCCESS" : "FAILED") . "</p>";
                    
                    if ($password_correct) {
                        if ($firm['registration_status'] == 'Active') {
                            // Set session variables
                            $_SESSION['firm_id'] = $firm['id'];
                            $_SESSION['firm_name'] = $firm['name'];
                            $_SESSION['firm_email'] = $firm['email'];
                            
                            echo "<p style='color: green;'>Login successful! Redirecting...</p>";
                            
                            // Redirect to dashboard
                            header("Location: dashboard.php");
                            exit();
                        } else {
                            $error = "Your account is not active. Status: " . $firm['registration_status'];
                            echo "<p style='color: red;'>Account not active</p>";
                        }
                    } else {
                        $error = "Invalid email or password.";
                        echo "<p style='color: red;'>Password verification failed</p>";
                        
                        // Let's try to fix the password
                        echo "<p>Attempting to fix password...</p>";
                        $new_hash = password_hash($password, PASSWORD_DEFAULT);
                        $update_sql = "UPDATE tax_firms SET password = ? WHERE email = ?";
                        $update_stmt = $conn->prepare($update_sql);
                        
                        if ($update_stmt) {
                            $update_stmt->bind_param("ss", $new_hash, $email);
                            if ($update_stmt->execute()) {
                                echo "<p style='color: green;'>Password updated! Please try logging in again.</p>";
                            }
                        }
                    }
                } else {
                    $error = "Invalid email or password.";
                    echo "<p style='color: red;'>No firm found with this email</p>";
                    
                    // Create the firm if it doesn't exist
                    if ($email === "<EMAIL>") {
                        echo "<p>Creating test firm...</p>";
                        
                        $insert_sql = "INSERT INTO tax_firms (name, email, password, registration_status) VALUES (?, ?, ?, 'Active')";
                        $insert_stmt = $conn->prepare($insert_sql);
                        
                        if ($insert_stmt) {
                            $name = "Test Tax Consulting Firm";
                            $hash = password_hash($password, PASSWORD_DEFAULT);
                            $insert_stmt->bind_param("sss", $name, $email, $hash);
                            
                            if ($insert_stmt->execute()) {
                                echo "<p style='color: green;'>Test firm created! Please try logging in again.</p>";
                            }
                        }
                    }
                }
            } else {
                $error = "Database error: " . $conn->error;
                echo "<p style='color: red;'>Database error: " . $conn->error . "</p>";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Firm Login - Tax Registration System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            width: 100%;
            max-width: 400px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            border: none;
        }
        .alert {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 class="text-center mb-4">Firm Login (Debug Version)</h2>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        
        <div class="alert alert-info">
            <strong>Test Credentials:</strong><br>
            Email: <EMAIL><br>
            Password: password123
        </div>
        
        <form method="POST">
            <div class="mb-3">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" class="form-control" id="email" name="email" required 
                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : '<EMAIL>'; ?>">
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" required 
                       value="password123">
            </div>
            
            <button type="submit" class="btn btn-primary w-100">Login</button>
        </form>
        
        <div class="text-center mt-3">
            <a href="login.php">Back to Regular Login</a>
        </div>
    </div>
</body>
</html>
