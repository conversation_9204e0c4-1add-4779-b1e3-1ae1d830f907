<?php
require_once '../includes/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Check if password_resets table exists, if not create it
$check_table_sql = "SHOW TABLES LIKE 'password_resets'";
$table_result = $conn->query($check_table_sql);
if ($table_result->num_rows == 0) {
    // Table doesn't exist, create it
    $create_table_sql = "CREATE TABLE password_resets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        token VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        used TINYINT(1) DEFAULT 0,
        user_type ENUM('firm', 'practitioner', 'admin') NOT NULL
    )";
    
    if (!$conn->query($create_table_sql)) {
        die("Error creating password_resets table: " . $conn->error);
    }
}

$token = isset($_GET['token']) ? $_GET['token'] : '';
$message = '';
$error = '';
$valid_token = false;
$email = '';

// Validate token
if (!empty($token)) {
    $sql = "SELECT email FROM password_resets 
            WHERE token = ? AND expires_at > NOW() AND used = 0 AND user_type = 'firm'";
    $stmt = $conn->prepare($sql);
    
    if ($stmt === false) {
        $error = "Database error: " . $conn->error;
    } else {
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 1) {
            $row = $result->fetch_assoc();
            $email = $row['email'];
            $valid_token = true;
        } else {
            $error = "Invalid or expired reset link. Please request a new one.";
        }
    }
}

// Process password reset form
if ($_SERVER["REQUEST_METHOD"] == "POST" && $valid_token) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate passwords
    if (empty($password)) {
        $error = "Please enter a password.";
    } elseif (strlen($password) < 8) {
        $error = "Password must be at least 8 characters.";
    } elseif ($password !== $confirm_password) {
        $error = "Passwords do not match.";
    } else {
        // Hash the new password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Update the password
        $update_sql = "UPDATE tax_firms SET password = ? WHERE email = ?";
        $update_stmt = $conn->prepare($update_sql);
        
        if ($update_stmt === false) {
            $error = "Database error: " . $conn->error;
        } else {
            $update_stmt->bind_param("ss", $hashed_password, $email);
            
            if ($update_stmt->execute()) {
                // Mark token as used
                $mark_sql = "UPDATE password_resets SET used = 1 WHERE token = ?";
                $mark_stmt = $conn->prepare($mark_sql);
                
                if ($mark_stmt === false) {
                    // Still consider password reset successful even if marking token fails
                    $message = "Your password has been reset successfully. You can now <a href='login.php'>login</a> with your new password.";
                } else {
                    $mark_stmt->bind_param("s", $token);
                    $mark_stmt->execute();
                    $message = "Your password has been reset successfully. You can now <a href='login.php'>login</a> with your new password.";
                }
            } else {
                $error = "Failed to reset password. Please try again: " . $update_stmt->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Tax Registration System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .auth-body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .auth-container {
            margin: 2rem 0;
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: none;
            padding: 1.5rem 1.5rem 0.5rem;
            text-align: center;
        }
        
        .card-header h3 {
            color: #333;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-label {
            font-weight: 500;
            color: #495057;
        }
        
        .input-group-text {
            background-color: #f8f9fa;
            border-right: none;
        }
        
        .form-control {
            border-left: none;
        }
        
        .form-control:focus {
            box-shadow: none;
            border-color: #ced4da;
        }
        
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            padding: 0.6rem 1rem;
            font-weight: 500;
            position: relative;
        }
        
        .btn-primary:hover {
            background-color: #0b5ed7;
        }
        
        .btn-outline-secondary {
            border-color: #ced4da;
            color: #6c757d;
        }
        
        .loading-spinner {
            display: none;
            width: 1.5rem;
            height: 1.5rem;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }
        
        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .auth-links {
            text-align: center;
        }
        
        .auth-links a {
            color: #0d6efd;
            text-decoration: none;
        }
        
        .auth-links a:hover {
            text-decoration: underline;
        }
        
        .alert {
            border-radius: 5px;
            padding: 0.75rem 1rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body class="auth-body">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="auth-container">
                    <div class="card">
                        <div class="card-header">
                            <h3>Reset Password</h3>
                            <p class="mb-0 text-muted">Enter your new password</p>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($error)): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fa fa-exclamation-circle me-2"></i> <?php echo $error; ?>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($message)): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fa fa-check-circle me-2"></i> <?php echo $message; ?>
                            </div>
                            <?php else: ?>
                                <?php if ($valid_token): ?>
                                <form id="resetPasswordForm" method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?token=' . $token); ?>">
                                    <div class="mb-4">
                                        <label for="password" class="form-label">New Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-lock"></i>
                                            </span>
                                            <input 
                                                type="password" 
                                                class="form-control" 
                                                id="password" 
                                                name="password" 
                                                placeholder="Enter new password"
                                                required
                                            >
                                            <button 
                                                type="button" 
                                                class="btn btn-outline-secondary" 
                                                id="togglePassword"
                                            >
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">Password must be at least 8 characters long.</small>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="confirm_password" class="form-label">Confirm Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-lock"></i>
                                            </span>
                                            <input 
                                                type="password" 
                                                class="form-control" 
                                                id="confirm_password" 
                                                name="confirm_password" 
                                                placeholder="Confirm new password"
                                                required
                                            >
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid mb-3">
                                        <button type="submit" class="btn btn-primary" id="resetBtn">
                                            <span class="btn-text">
                                                <i class="fas fa-key me-2"></i>Reset Password
                                            </span>
                                            <span class="loading-spinner"></span>
                                        </button>
                                    </div>
                                </form>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <div class="auth-links">
                                <p class="mb-0">
                                    <a href="login.php"><i class="fas fa-arrow-left me-1"></i> Back to Login</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('resetPasswordForm');
            const resetBtn = document.getElementById('resetBtn');
            const loadingSpinner = document.querySelector('.loading-spinner');
            const btnText = document.querySelector('.btn-text');
            const toggleBtn = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            
            if (toggleBtn && passwordInput) {
                toggleBtn.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    
                    const icon = toggleBtn.querySelector('i');
                    icon.classList.toggle('fa-eye');
                    icon.classList.toggle('fa-eye-slash');
                });
            }
            
            if (form) {
                form.addEventListener('submit', function() {
                    resetBtn.disabled = true;
                    btnText.style.display = 'none';
                    loadingSpinner.style.display = 'block';
                });
            }
        });
    </script>
</body>
</html>

