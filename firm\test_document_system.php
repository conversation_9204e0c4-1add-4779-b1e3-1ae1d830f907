<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    echo "<p><a href='../login.php'>Login</a></p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Document Management System Test</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Check if firm_documents table exists
echo "<h3>1. Database Table Check</h3>";
$table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<p style='color: green;'>✅ firm_documents table exists</p>";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE firm_documents");
    echo "<h4>Table Structure:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ firm_documents table does not exist</p>";
    echo "<p><a href='setup_documents_table.php'>Create Table</a></p>";
}

// Check upload directory
echo "<h3>2. Upload Directory Check</h3>";
$upload_dir = '../uploads/documents/';
if (file_exists($upload_dir)) {
    echo "<p style='color: green;'>✅ Base upload directory exists: $upload_dir</p>";
    
    $firm_dir = $upload_dir . 'firm_' . $firm_id . '/';
    if (file_exists($firm_dir)) {
        echo "<p style='color: green;'>✅ Firm-specific directory exists: $firm_dir</p>";
        
        // List files in firm directory
        $files = scandir($firm_dir);
        $files = array_diff($files, array('.', '..'));
        if (count($files) > 0) {
            echo "<h4>Files in firm directory:</h4>";
            echo "<ul>";
            foreach ($files as $file) {
                $file_path = $firm_dir . $file;
                $file_size = filesize($file_path);
                echo "<li>$file (" . number_format($file_size) . " bytes)</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No files in firm directory</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Firm-specific directory does not exist (will be created on first upload)</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Upload directory does not exist</p>";
}

// Check current documents in database
echo "<h3>3. Current Documents in Database</h3>";
if ($table_check && $table_check->num_rows > 0) {
    $docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ?";
    $docs_stmt = $conn->prepare($docs_sql);
    $docs_stmt->bind_param("i", $firm_id);
    $docs_stmt->execute();
    $docs_result = $docs_stmt->get_result();
    
    if ($docs_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Document Type</th><th>File Name</th><th>Status</th><th>Upload Date</th><th>File Size</th><th>Actions</th></tr>";
        
        while ($doc = $docs_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
            echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
            echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
            echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
            echo "<td>" . number_format($doc['file_size']) . " bytes</td>";
            echo "<td>";
            echo "<a href='view_document.php?type=" . $doc['document_type'] . "' target='_blank'>View</a> | ";
            echo "<a href='download_document.php?type=" . $doc['document_type'] . "'>Download</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No documents found for this firm</p>";
    }
}

// Test upload functionality
echo "<h3>4. Upload Test</h3>";
echo "<form action='upload_document.php' method='POST' enctype='multipart/form-data' style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";
echo "<h4>Test Document Upload</h4>";
echo "<p>";
echo "<label>Document Type:</label><br>";
echo "<select name='document_type' required>";
echo "<option value=''>Select document type</option>";
echo "<option value='memart'>MEMART</option>";
echo "<option value='cac_status'>CAC Status Report</option>";
echo "<option value='tax_clearance'>Tax Clearance Certificate</option>";
echo "<option value='utility_bill'>Utility Bill</option>";
echo "<option value='incorporation_cert'>Certificate of Incorporation</option>";
echo "</select>";
echo "</p>";
echo "<p>";
echo "<label>File:</label><br>";
echo "<input type='file' name='document' accept='.pdf,.jpg,.jpeg,.png,.doc,.docx' required>";
echo "</p>";
echo "<p>";
echo "<button type='submit'>Upload Test Document</button>";
echo "</p>";
echo "</form>";

// Check file permissions
echo "<h3>5. File Permissions Check</h3>";
if (file_exists($upload_dir)) {
    $perms = fileperms($upload_dir);
    echo "<p>Upload directory permissions: " . substr(sprintf('%o', $perms), -4) . "</p>";
    
    if (is_writable($upload_dir)) {
        echo "<p style='color: green;'>✅ Upload directory is writable</p>";
    } else {
        echo "<p style='color: red;'>❌ Upload directory is not writable</p>";
    }
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px;">
    <h3>Quick Actions:</h3>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="setup_documents_table.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Setup Database</a>
    </p>
</div>
