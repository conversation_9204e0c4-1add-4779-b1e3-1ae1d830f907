<?php
require_once '../includes/config.php';

echo "<h2>Fixing Documents Table Structure</h2>";

// Check if firm_documents table exists
$table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");

if ($table_check && $table_check->num_rows > 0) {
    echo "<p style='color: blue;'>✓ firm_documents table exists</p>";
    
    // Show current structure
    echo "<h3>Current Table Structure:</h3>";
    $structure = $conn->query("DESCRIBE firm_documents");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $existing_columns = [];
    while ($row = $structure->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Define required columns
    $required_columns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'firm_id' => 'INT NOT NULL',
        'document_type' => 'VARCHAR(50) NOT NULL',
        'file_name' => 'VARCHAR(255) NOT NULL',
        'file_path' => 'VARCHAR(500) NOT NULL',
        'file_size' => 'INT NOT NULL',
        'upload_date' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'status' => "ENUM('uploaded', 'pending', 'rejected') DEFAULT 'uploaded'",
        'uploaded_by' => 'VARCHAR(100) DEFAULT "System"',
        'notes' => 'TEXT',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    // Check for missing columns
    $missing_columns = array_diff(array_keys($required_columns), $existing_columns);
    
    if (!empty($missing_columns)) {
        echo "<h3>Adding Missing Columns:</h3>";
        
        foreach ($missing_columns as $column) {
            $sql = "ALTER TABLE firm_documents ADD COLUMN $column " . $required_columns[$column];
            
            if ($conn->query($sql)) {
                echo "<p style='color: green;'>✅ Added column: $column</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to add column $column: " . $conn->error . "</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ All required columns exist</p>";
    }
    
    // Add foreign key constraint if it doesn't exist
    echo "<h3>Checking Foreign Key Constraints:</h3>";
    $fk_check = $conn->query("
        SELECT CONSTRAINT_NAME 
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_NAME = 'firm_documents' 
        AND CONSTRAINT_NAME != 'PRIMARY'
        AND REFERENCED_TABLE_NAME = 'tax_firms'
    ");
    
    if ($fk_check->num_rows == 0) {
        $fk_sql = "ALTER TABLE firm_documents ADD CONSTRAINT fk_firm_documents_firm_id 
                   FOREIGN KEY (firm_id) REFERENCES tax_firms(id) ON DELETE CASCADE";
        
        if ($conn->query($fk_sql)) {
            echo "<p style='color: green;'>✅ Added foreign key constraint</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Could not add foreign key constraint: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Foreign key constraint exists</p>";
    }
    
    // Add unique constraint for firm_id + document_type
    echo "<h3>Checking Unique Constraints:</h3>";
    $unique_check = $conn->query("
        SELECT CONSTRAINT_NAME 
        FROM information_schema.TABLE_CONSTRAINTS 
        WHERE TABLE_NAME = 'firm_documents' 
        AND CONSTRAINT_TYPE = 'UNIQUE'
        AND CONSTRAINT_NAME LIKE '%firm_document%'
    ");
    
    if ($unique_check->num_rows == 0) {
        $unique_sql = "ALTER TABLE firm_documents ADD CONSTRAINT unique_firm_document 
                       UNIQUE KEY (firm_id, document_type)";
        
        if ($conn->query($unique_sql)) {
            echo "<p style='color: green;'>✅ Added unique constraint for firm_id + document_type</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Could not add unique constraint: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Unique constraint exists</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ firm_documents table does not exist. Creating it...</p>";
    
    // Create the table with correct structure
    $create_sql = "
    CREATE TABLE firm_documents (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firm_id INT NOT NULL,
        document_type VARCHAR(50) NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('uploaded', 'pending', 'rejected') DEFAULT 'uploaded',
        uploaded_by VARCHAR(100) DEFAULT 'System',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (firm_id) REFERENCES tax_firms(id) ON DELETE CASCADE,
        UNIQUE KEY unique_firm_document (firm_id, document_type),
        INDEX idx_firm_id (firm_id),
        INDEX idx_document_type (document_type),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    if ($conn->query($create_sql)) {
        echo "<p style='color: green;'>✅ firm_documents table created successfully!</p>";
    } else {
        echo "<p style='color: red;'>❌ Error creating table: " . $conn->error . "</p>";
    }
}

// Show final table structure
echo "<h3>Final Table Structure:</h3>";
$final_structure = $conn->query("DESCRIBE firm_documents");
if ($final_structure) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    while ($row = $final_structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test the table with a sample query
echo "<h3>Testing Table Access:</h3>";
$test_sql = "SELECT COUNT(*) as count FROM firm_documents";
$test_result = $conn->query($test_sql);

if ($test_result) {
    $count = $test_result->fetch_assoc()['count'];
    echo "<p style='color: green;'>✅ Table access test successful. Current document count: $count</p>";
} else {
    echo "<p style='color: red;'>❌ Table access test failed: " . $conn->error . "</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
    <h3 style="color: #155724;">Table Structure Fixed!</h3>
    <p style="color: #155724;">The firm_documents table now has the correct structure for the document management system.</p>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Test Dashboard</a>
        <a href="test_upload.html" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Test Upload</a>
    </p>
</div>
