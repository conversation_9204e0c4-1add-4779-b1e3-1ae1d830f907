<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Not logged in. Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Profile Data Test for Firm ID: $firm_id</h2>";

// Get firm data from database
$sql = "SELECT * FROM tax_firms WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $firm_id);
$stmt->execute();
$result = $stmt->get_result();
$firm_data = $result->fetch_assoc();

if ($firm_data) {
    echo "<h3>Current Data in Database:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    
    foreach ($firm_data as $key => $value) {
        echo "<tr>";
        echo "<td><strong>$key</strong></td>";
        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Test specific fields we're interested in
    echo "<h3>Profile Fields Status:</h3>";
    $profile_fields = [
        'address' => 'Street Address',
        'city' => 'City',
        'state' => 'State', 
        'postal_code' => 'Postal Code',
        'email' => 'Email',
        'alternative_email' => 'Alternative Email',
        'phone' => 'Phone',
        'website' => 'Website',
        'ceo_name' => 'CEO Name',
        'ceo_email' => 'CEO Email',
        'ceo_phone' => 'CEO Phone'
    ];
    
    echo "<ul>";
    foreach ($profile_fields as $field => $label) {
        $value = $firm_data[$field] ?? 'NOT SET';
        $status = empty($value) || $value === 'NOT SET' ? '❌' : '✅';
        echo "<li>$status <strong>$label</strong>: " . htmlspecialchars($value) . "</li>";
    }
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>No firm data found for ID: $firm_id</p>";
}

// Test if we can update data
if ($_POST) {
    echo "<h3>Testing Update...</h3>";
    
    $test_data = [
        'address' => $_POST['test_address'] ?? 'Test Address 123',
        'city' => $_POST['test_city'] ?? 'Test City',
        'ceo_name' => $_POST['test_ceo'] ?? 'Test CEO Name'
    ];
    
    $update_sql = "UPDATE tax_firms SET address = ?, city = ?, ceo_name = ? WHERE id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("sssi", $test_data['address'], $test_data['city'], $test_data['ceo_name'], $firm_id);
    
    if ($update_stmt->execute()) {
        echo "<p style='color: green;'>✅ Test update successful! Affected rows: " . $update_stmt->affected_rows . "</p>";
        echo "<p><a href='test_profile_data.php'>Refresh to see changes</a></p>";
    } else {
        echo "<p style='color: red;'>❌ Test update failed: " . $update_stmt->error . "</p>";
    }
}

$stmt->close();
$conn->close();
?>

<h3>Test Update Form:</h3>
<form method="POST">
    <p>
        <label>Test Address:</label><br>
        <input type="text" name="test_address" value="123 Test Street" style="width: 300px;">
    </p>
    <p>
        <label>Test City:</label><br>
        <input type="text" name="test_city" value="Test City" style="width: 300px;">
    </p>
    <p>
        <label>Test CEO Name:</label><br>
        <input type="text" name="test_ceo" value="John Doe CEO" style="width: 300px;">
    </p>
    <p>
        <button type="submit">Test Update</button>
    </p>
</form>

<p><a href="dashboard.php">← Back to Dashboard</a></p>
