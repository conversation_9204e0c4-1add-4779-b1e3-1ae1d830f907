<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/auth_check.php';
require_once '../includes/citn_integration.php';

// Check admin authentication
require_admin_auth();

// Initialize CITN integration
$citn = new CITNIntegration($conn);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'verify_citn':
            $practitioner_id = (int)$_POST['practitioner_id'];
            $citn_number = trim($_POST['citn_number']);
            
            $result = $citn->verifyCITNNumber($citn_number, $practitioner_id, 'manual');
            echo json_encode($result);
            exit;
            
        case 'queue_verification':
            $practitioner_id = (int)$_POST['practitioner_id'];
            $citn_number = trim($_POST['citn_number']);
            $priority = $_POST['priority'] ?? 'normal';
            
            $success = $citn->queueVerification($practitioner_id, $citn_number, $priority);
            echo json_encode(['success' => $success]);
            exit;
            
        case 'process_queue':
            $batch_size = (int)($_POST['batch_size'] ?? 10);
            $processed = $citn->processVerificationQueue($batch_size);
            echo json_encode(['success' => true, 'processed' => $processed]);
            exit;
            
        case 'update_config':
            $config_key = $_POST['config_key'];
            $config_value = $_POST['config_value'];
            
            $sql = "UPDATE citn_api_config SET config_value = ?, updated_at = NOW(), updated_by = ? WHERE config_key = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sis", $config_value, $_SESSION['admin_id'], $config_key);
            $success = $stmt->execute();
            
            echo json_encode(['success' => $success]);
            exit;
    }
}

// Get statistics
$stats = $citn->getVerificationStats();

// Get recent verification logs
$sql = "SELECT cvl.*, tp.full_name, tp.email 
        FROM citn_verification_log cvl 
        LEFT JOIN tax_practitioners tp ON cvl.practitioner_id = tp.id 
        ORDER BY cvl.verification_date DESC 
        LIMIT 50";
$recent_logs = $conn->query($sql);

// Get practitioners with CITN numbers
$sql = "SELECT id, full_name, email, registration_number, citn_number, citn_verification_status, 
               citn_verified_date, citn_member_name, citn_membership_type, citn_membership_status,
               citn_expiry_date, citn_verification_attempts
        FROM tax_practitioners 
        WHERE citn_number IS NOT NULL AND citn_number != ''
        ORDER BY citn_verification_status, full_name";
$practitioners_result = $conn->query($sql);

// Get queue status
$sql = "SELECT status, COUNT(*) as count FROM citn_verification_queue GROUP BY status";
$queue_stats = $conn->query($sql);

// Get configuration
$sql = "SELECT * FROM citn_api_config ORDER BY config_key";
$config_result = $conn->query($sql);

$page_title = "CITN Management";
include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">CITN Management</h1>
                    <p class="text-muted">Manage Chartered Institute of Taxation of Nigeria verifications</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary me-2" onclick="processQueue()">
                        <i class="fas fa-play me-1"></i>Process Queue
                    </button>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#configModal">
                        <i class="fas fa-cog me-1"></i>Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_practitioners']); ?></h4>
                            <p class="mb-0">Total Practitioners</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['verified']); ?></h4>
                            <p class="mb-0">CITN Verified</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['pending']); ?></h4>
                            <p class="mb-0">Pending Verification</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['invalid'] + $stats['expired']); ?></h4>
                            <p class="mb-0">Invalid/Expired</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Queue Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Verification Queue Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php while ($queue_stat = $queue_stats->fetch_assoc()): ?>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="mb-0 text-<?php echo getQueueStatusColor($queue_stat['status']); ?>">
                                    <?php echo number_format($queue_stat['count']); ?>
                                </h4>
                                <p class="mb-0 text-capitalize"><?php echo str_replace('_', ' ', $queue_stat['status']); ?></p>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Practitioners with CITN -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Practitioners with CITN Numbers</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="practitionersTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Registration #</th>
                                    <th>CITN Number</th>
                                    <th>Status</th>
                                    <th>Member Name</th>
                                    <th>Membership Type</th>
                                    <th>Expiry Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($practitioner = $practitioners_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($practitioner['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($practitioner['email']); ?></td>
                                    <td><?php echo htmlspecialchars($practitioner['registration_number']); ?></td>
                                    <td><?php echo htmlspecialchars($practitioner['citn_number']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo getCITNStatusColor($practitioner['citn_verification_status']); ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $practitioner['citn_verification_status'])); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($practitioner['citn_member_name'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($practitioner['citn_membership_type'] ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if ($practitioner['citn_expiry_date']): ?>
                                            <?php 
                                            $expiry = new DateTime($practitioner['citn_expiry_date']);
                                            $now = new DateTime();
                                            $is_expired = $expiry < $now;
                                            ?>
                                            <span class="<?php echo $is_expired ? 'text-danger' : 'text-success'; ?>">
                                                <?php echo $expiry->format('M d, Y'); ?>
                                                <?php if ($is_expired): ?>
                                                    <i class="fas fa-exclamation-triangle ms-1" title="Expired"></i>
                                                <?php endif; ?>
                                            </span>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="verifyCITN(<?php echo $practitioner['id']; ?>, '<?php echo htmlspecialchars($practitioner['citn_number']); ?>')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" 
                                                    onclick="queueVerification(<?php echo $practitioner['id']; ?>, '<?php echo htmlspecialchars($practitioner['citn_number']); ?>')">
                                                <i class="fas fa-clock"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="viewDetails(<?php echo $practitioner['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Verification Logs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Verification Logs</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Practitioner</th>
                                    <th>CITN Number</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Response Code</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($log = $recent_logs->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo date('M d, Y H:i', strtotime($log['verification_date'])); ?></td>
                                    <td>
                                        <?php if ($log['full_name']): ?>
                                            <?php echo htmlspecialchars($log['full_name']); ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($log['email']); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">Anonymous</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['citn_number']); ?></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo ucfirst($log['verification_type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo getCITNStatusColor($log['verification_status']); ?>">
                                            <?php echo ucfirst($log['verification_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($log['api_response_code']): ?>
                                            <span class="badge bg-<?php echo $log['api_response_code'] == 200 ? 'success' : 'danger'; ?>">
                                                <?php echo $log['api_response_code']; ?>
                                            </span>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Modal -->
<div class="modal fade" id="configModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">CITN API Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <?php $config_result->data_seek(0); ?>
                    <?php while ($config = $config_result->fetch_assoc()): ?>
                    <div class="mb-3">
                        <label for="<?php echo $config['config_key']; ?>" class="form-label">
                            <?php echo ucwords(str_replace('_', ' ', $config['config_key'])); ?>
                        </label>
                        <?php if ($config['config_key'] === 'citn_verification_enabled' || $config['config_key'] === 'citn_mock_mode'): ?>
                            <select class="form-select" name="<?php echo $config['config_key']; ?>" id="<?php echo $config['config_key']; ?>">
                                <option value="true" <?php echo $config['config_value'] === 'true' ? 'selected' : ''; ?>>Enabled</option>
                                <option value="false" <?php echo $config['config_value'] === 'false' ? 'selected' : ''; ?>>Disabled</option>
                            </select>
                        <?php else: ?>
                            <input type="text" class="form-control" 
                                   name="<?php echo $config['config_key']; ?>" 
                                   id="<?php echo $config['config_key']; ?>"
                                   value="<?php echo htmlspecialchars($config['config_value']); ?>">
                        <?php endif; ?>
                        <?php if ($config['description']): ?>
                            <div class="form-text"><?php echo htmlspecialchars($config['description']); ?></div>
                        <?php endif; ?>
                    </div>
                    <?php endwhile; ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveConfiguration()">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<?php
// Helper functions
function getCITNStatusColor($status) {
    switch ($status) {
        case 'verified': return 'success';
        case 'pending': return 'warning';
        case 'invalid': return 'danger';
        case 'expired': return 'warning';
        case 'error': return 'danger';
        default: return 'secondary';
    }
}

function getQueueStatusColor($status) {
    switch ($status) {
        case 'completed': return 'success';
        case 'processing': return 'info';
        case 'pending': return 'warning';
        case 'failed': return 'danger';
        case 'cancelled': return 'secondary';
        default: return 'secondary';
    }
}
?>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#practitionersTable').DataTable({
        "pageLength": 25,
        "order": [[ 4, "asc" ]], // Sort by status
        "columnDefs": [
            { "orderable": false, "targets": [8] } // Disable sorting on Actions column
        ]
    });
});

// Verify CITN number
function verifyCITN(practitionerId, citnNumber) {
    if (!confirm('Are you sure you want to verify this CITN number?')) {
        return;
    }

    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    $.ajax({
        url: 'citn_management.php',
        method: 'POST',
        data: {
            action: 'verify_citn',
            practitioner_id: practitionerId,
            citn_number: citnNumber
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', 'CITN verification completed successfully');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('danger', 'Verification failed: ' + response.message);
            }
        },
        error: function() {
            showAlert('danger', 'An error occurred during verification');
        },
        complete: function() {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        }
    });
}

// Queue verification
function queueVerification(practitionerId, citnNumber) {
    const priority = prompt('Enter priority (low, normal, high, urgent):', 'normal');
    if (!priority) return;

    $.ajax({
        url: 'citn_management.php',
        method: 'POST',
        data: {
            action: 'queue_verification',
            practitioner_id: practitionerId,
            citn_number: citnNumber,
            priority: priority
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Verification queued successfully');
            } else {
                showAlert('danger', 'Failed to queue verification');
            }
        },
        error: function() {
            showAlert('danger', 'An error occurred while queuing verification');
        }
    });
}

// Process verification queue
function processQueue() {
    const batchSize = prompt('Enter batch size (default: 10):', '10');
    if (!batchSize) return;

    const btn = event.target;
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
    btn.disabled = true;

    $.ajax({
        url: 'citn_management.php',
        method: 'POST',
        data: {
            action: 'process_queue',
            batch_size: parseInt(batchSize)
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', `Processed ${response.processed.length} items from queue`);
                setTimeout(() => location.reload(), 2000);
            } else {
                showAlert('danger', 'Failed to process queue');
            }
        },
        error: function() {
            showAlert('danger', 'An error occurred while processing queue');
        },
        complete: function() {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        }
    });
}

// Save configuration
function saveConfiguration() {
    const form = document.getElementById('configForm');
    const formData = new FormData(form);
    const configs = {};

    for (let [key, value] of formData.entries()) {
        configs[key] = value;
    }

    let saved = 0;
    let total = Object.keys(configs).length;

    for (let [key, value] of Object.entries(configs)) {
        $.ajax({
            url: 'citn_management.php',
            method: 'POST',
            data: {
                action: 'update_config',
                config_key: key,
                config_value: value
            },
            dataType: 'json',
            success: function(response) {
                saved++;
                if (saved === total) {
                    showAlert('success', 'Configuration saved successfully');
                    $('#configModal').modal('hide');
                }
            },
            error: function() {
                showAlert('danger', 'Failed to save configuration for ' + key);
            }
        });
    }
}

// View practitioner details
function viewDetails(practitionerId) {
    // This would open a modal with detailed practitioner information
    // For now, redirect to manage users page with filter
    window.location.href = `manage_users.php?filter=${practitionerId}`;
}

// Show alert message
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Insert at the top of the container
    $('.container-fluid').prepend(alertHtml);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>

<?php include '../includes/admin_footer.php'; ?>
