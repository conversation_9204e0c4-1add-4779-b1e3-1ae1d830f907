<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    header('HTTP/1.0 403 Forbidden');
    echo 'Access denied';
    exit();
}

require_once '../includes/config.php';

try {
    $firm_id = $_SESSION['firm_id'];
    $document_type = $_GET['type'] ?? '';
    
    // Validate document type
    $allowed_types = ['memart', 'cac_status', 'tax_clearance', 'utility_bill', 'incorporation_cert'];
    if (!in_array($document_type, $allowed_types)) {
        throw new Exception('Invalid document type');
    }
    
    // Check if firm_documents table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");
    if (!$table_check || $table_check->num_rows === 0) {
        throw new Exception('Document system not initialized');
    }

    // Get document info from database
    $sql = "SELECT file_name, file_path, file_size FROM firm_documents
            WHERE firm_id = ? AND document_type = ? AND status = 'uploaded'";
    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        throw new Exception('Database error: ' . $conn->error);
    }

    $stmt->bind_param("is", $firm_id, $document_type);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        // Check if document exists but with different status
        $check_sql = "SELECT status FROM firm_documents WHERE document_type = ? AND firm_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $document_type, $firm_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $doc_status = $check_result->fetch_assoc()['status'];
            $status_display = empty($doc_status) ? '(empty/null)' : $doc_status;
            throw new Exception("Document found but status is: $status_display (not uploaded)");
        } else {
            throw new Exception("Document type '$document_type' not found for this firm");
        }
    }
    
    $document = $result->fetch_assoc();
    $file_path = $document['file_path'];
    $file_name = $document['file_name'];
    $file_size = $document['file_size'];
    
    // Check if file exists on disk
    if (!file_exists($file_path)) {
        throw new Exception('File not found on server');
    }
    
    // Get file extension for MIME type
    $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
    
    // Set appropriate MIME type
    $mime_types = [
        'pdf' => 'application/pdf',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    $mime_type = $mime_types[$file_extension] ?? 'application/octet-stream';
    
    // Set headers for download
    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: attachment; filename="' . $file_name . '"');
    header('Content-Length: ' . $file_size);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    
    // Output file
    readfile($file_path);
    
} catch (Exception $e) {
    header('HTTP/1.0 404 Not Found');
    echo 'Error: ' . $e->getMessage();
}

$conn->close();
?>
