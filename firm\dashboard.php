<?php
session_start();
require_once '../includes/config.php';

// TEMPORARY FIX: Force correct session if firm_id is wrong
if (isset($_SESSION['firm_id']) && $_SESSION['firm_id'] == 1) {
    // Get the correct firm from database
    $correct_firm_sql = "SELECT * FROM tax_firms ORDER BY id LIMIT 1";
    $correct_firm_result = $conn->query($correct_firm_sql);
    $correct_firm = $correct_firm_result->fetch_assoc();

    if ($correct_firm) {
        $_SESSION['firm_id'] = (int)$correct_firm['id'];
        $_SESSION['firm_name'] = $correct_firm['name'];
        $_SESSION['firm_email'] = $correct_firm['email'];
        $_SESSION['firm_registration_number'] = $correct_firm['registration_number'];
        $_SESSION['firm_registration_status'] = $correct_firm['registration_status'];

        // Force session write
        session_write_close();
        session_start();
    }
}

// Simple login check
if (!isset($_SESSION['firm_id']) || !isset($_SESSION['firm_email'])) {
    header("Location: login.php");
    exit();
}

// Get session info for display
$firm_id = $_SESSION['firm_id'];
$firm_name = $_SESSION['firm_name'];
$firm_email = $_SESSION['firm_email'];

// Get firm details from database
$firm_sql = "SELECT * FROM tax_firms WHERE id = ?";
$firm_stmt = $conn->prepare($firm_sql);
$firm_stmt->bind_param("i", $firm_id);
$firm_stmt->execute();
$firm_result = $firm_stmt->get_result();
$firm_data = $firm_result->fetch_assoc();
$firm_stmt->close();

// Debug: Log what we retrieved from database (remove this in production)
error_log("Firm data retrieved: " . print_r($firm_data, true));

// Get document statuses from database
$document_statuses = [];

// Check if firm_documents table exists
$table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");
if ($table_check && $table_check->num_rows > 0) {
    $documents_sql = "SELECT document_type, file_name, file_path, status, upload_date FROM firm_documents WHERE firm_id = ?";
    $documents_stmt = $conn->prepare($documents_sql);

    if ($documents_stmt) {
        $documents_stmt->bind_param("i", $firm_id);
        $documents_stmt->execute();
        $documents_result = $documents_stmt->get_result();

        while ($doc = $documents_result->fetch_assoc()) {
            $document_statuses[$doc['document_type']] = $doc;
        }
        $documents_stmt->close();

        // Debug: Log loaded documents
        error_log("Dashboard loaded " . count($document_statuses) . " documents for firm_id: $firm_id");
    } else {
        error_log("Failed to prepare documents query: " . $conn->error);
    }
} else {
    error_log("firm_documents table does not exist");
}

// Define supporting documents for the dashboard
$supporting_documents = [
    [
        'id' => 'memart',
        'title' => 'CTC of Memorandum & Articles of Association (MEMART)',
        'type' => 'MANDATORY',
        'description' => 'Certified True Copy of Memorandum and Articles of Association'
    ],
    [
        'id' => 'cac_status',
        'title' => 'CAC Company Status Report',
        'type' => 'MANDATORY',
        'description' => 'Current Company Status Report from Corporate Affairs Commission'
    ],
    [
        'id' => 'utility_bill',
        'title' => 'Current Utility Bill',
        'type' => 'MANDATORY',
        'description' => 'Recent utility bill showing company address'
    ],
    [
        'id' => 'director_change',
        'title' => 'Signed Application Letter to the Commission Requesting for Update/Change to Director Information',
        'type' => 'OPTIONAL',
        'description' => 'Letter requesting director information updates'
    ],
    [
        'id' => 'secretary_change',
        'title' => 'Signed Application Letter to the Commission Requesting for Update/Change to Company Secretary Information',
        'type' => 'OPTIONAL',
        'description' => 'Letter requesting company secretary information updates'
    ],
    [
        'id' => 'shareholder_change',
        'title' => 'Signed Application Letter to the Commission Requesting for Update/Change to Shareholder Information',
        'type' => 'OPTIONAL',
        'description' => 'Letter requesting shareholder information updates'
    ],
    [
        'id' => 'address_change',
        'title' => 'Signed Application Letter to the Commission Requesting for Update/Change of Address',
        'type' => 'OPTIONAL',
        'description' => 'Letter requesting address change'
    ],
    [
        'id' => 'board_resolution',
        'title' => 'Board Resolution',
        'type' => 'OPTIONAL',
        'description' => 'Board resolution document'
    ],
    [
        'id' => 'notification_letter',
        'title' => 'Signed Letter of Notification',
        'type' => 'OPTIONAL',
        'description' => 'Signed notification letter'
    ]
];

// Helper function to get document status
function getDocumentStatus($type, $statuses) {
    if (isset($statuses[$type])) {
        $doc = $statuses[$type];
        // Always show uploaded documents as having files (since we removed view/download)
        $has_file = ($doc['status'] === 'uploaded');
        return [
            'status' => $doc['status'],
            'file_name' => $doc['file_name'],
            'upload_date' => $doc['upload_date'] ? date('M j, Y', strtotime($doc['upload_date'])) : null,
            'has_file' => $has_file,
            'file_exists' => file_exists($doc['file_path'] ?? ''),
            'file_path' => $doc['file_path'] ?? null
        ];
    }
    return [
        'status' => 'pending',
        'file_name' => null,
        'upload_date' => null,
        'has_file' => false,
        'file_exists' => false,
        'file_path' => null
    ];
}

// Set default values for any missing fields
if (!$firm_data) {
    $firm_data = [];
}

// Create defaults array without overriding database values
$defaults = [
    'name' => 'Unknown Firm',
    'email' => $firm_email ?? '',
    'phone' => '',
    'address' => '',
    'city' => '',
    'state' => '',
    'postal_code' => '',
    'registration_number' => '',
    'cac_number' => '',
    'company_type' => 'Limited Liability Company',
    'ownership_type' => 'Nigerian Owned',
    'incorporation_date' => '',
    'registration_date' => '',
    'registration_status' => 'Pending',
    'tin_number' => '',
    'tax_id' => '',
    'logo_path' => '',
    'alternative_email' => '',
    'website' => '',
    'ceo_name' => '',
    'ceo_email' => '',
    'ceo_phone' => ''
];

// Merge defaults with firm data, but prioritize database values
$firm_data = array_merge($defaults, $firm_data);

// Ensure the registered company name from database is always used if available
// Only fall back to session name if database name is truly empty
if (empty($firm_data['name']) || $firm_data['name'] === 'Unknown Firm') {
    $firm_data['name'] = $firm_name ?? 'Unknown Firm';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Details - Tax Registration System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --border-color: #e2e8f0;
            --text-muted: #64748b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f1f5f9;
            color: #334155;
        }

        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid var(--border-color);
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: #f8fafc;
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .breadcrumb-nav a {
            color: var(--text-muted);
            text-decoration: none;
        }

        .breadcrumb-nav a:hover {
            color: var(--primary-color);
        }

        .page-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark-color);
            margin: 0;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #64748b;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .nav-link:hover {
            background-color: #f1f5f9;
            color: var(--primary-color);
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            background: #f1f5f9;
        }

        .top-header {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .logout-btn {
            background: var(--danger-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            background: #dc2626;
            color: white;
        }

        .content-area {
            padding: 2rem;
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* Company Name Section Styles */
        .company-name-section {
            background: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            margin-bottom: 1.5rem;
        }

        .company-verified-badge {
            background: #dcfce7;
            color: #166534;
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .text-lg {
            font-size: 1.125rem;
            line-height: 1.75rem;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-gray-900 {
            color: #111827;
        }

        .text-gray-400 {
            color: #9ca3af;
        }

        .text-gray-500 {
            color: #6b7280;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .company-name-display {
            margin-top: 0.25rem;
        }

        .company-name-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
            margin-top: 0;
        }

        .company-name-value {
            font-size: 0.875rem;
            font-weight: 500;
            color: #111827;
            margin-bottom: 0;
        }

        /* Profile Cards */
        .profile-card {
            background: white;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .company-logo-section {
            display: flex;
            gap: 2rem;
            align-items: flex-start;
        }

        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .logo-placeholder {
            width: 120px;
            height: 120px;
            background: #e2e8f0;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #94a3b8;
        }

        .company-logo {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 0.5rem;
            border: 2px solid #e5e7eb;
        }

        .change-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .change-btn:hover:not(:disabled) {
            background: #5b5bd6;
        }

        .change-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .company-name-info {
            flex: 1;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }

        .verification-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .field-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .field-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
            font-weight: 500;
        }

        .field-value {
            font-size: 0.875rem;
            color: #111827;
            font-weight: 500;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .required {
            color: var(--danger-color);
        }

        .field-note {
            display: block;
            margin-top: 0.5rem;
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.4;
        }

        .field-note i {
            margin-right: 0.25rem;
        }

        .text-success {
            color: var(--success-color) !important;
        }

        .text-warning {
            color: #f59e0b !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .company-logo-section {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Official Documents Styles */
        .documents-header {
            margin-bottom: 2rem;
        }

        .documents-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .documents-subtitle {
            color: #6b7280;
            font-size: 1rem;
            margin: 0;
        }

        .info-message {
            display: flex;
            align-items: flex-start;
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 16px;
            margin: 1.5rem 0;
            color: #1565c0;
        }

        .info-message i {
            font-size: 1.2rem;
            margin-right: 12px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .info-content {
            flex: 1;
        }

        .info-content p {
            margin: 0;
            line-height: 1.5;
            font-size: 0.95rem;
        }

        .contact-link {
            color: #1565c0;
            text-decoration: underline;
            font-weight: 600;
            cursor: pointer;
        }

        .contact-link:hover {
            color: #0d47a1;
            text-decoration: none;
        }

        /* Contact modal specific styles */
        #contactModal .form-group {
            margin-bottom: 1.5rem;
        }

        #contactModal label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        #contactModal select,
        #contactModal textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            font-family: inherit;
        }

        #contactModal select:focus,
        #contactModal textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        #contactModal textarea {
            resize: vertical;
            min-height: 120px;
        }

        .documents-summary {
            display: flex;
            gap: 1rem;
            margin: 1.5rem 0;
            flex-wrap: wrap;
        }

        .summary-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            min-width: 150px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .summary-card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .summary-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f3f4f6;
        }

        .summary-icon i {
            font-size: 1.25rem;
        }

        .summary-info {
            display: flex;
            flex-direction: column;
        }

        .summary-count {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-color);
            line-height: 1;
            transition: transform 0.3s ease;
        }

        .summary-label {
            font-size: 0.75rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-top: 0.25rem;
        }

        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
        }

        .document-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .document-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .document-number {
            position: absolute;
            top: -8px;
            left: -8px;
            background: var(--primary-color);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1;
        }

        .document-number.mandatory {
            background: var(--primary-color);
        }

        .document-number.optional {
            background: #6c757d;
        }

        .document-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), #5b5bd6);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .document-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .document-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .document-description {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .document-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .status-uploaded {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-expired {
            background: #fee2e2;
            color: #991b1b;
        }

        .document-date {
            font-size: 0.75rem;
            color: #9ca3af;
        }

        .document-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-action {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.875rem;
            border: 1px solid transparent;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .btn-view {
            background: #f3f4f6;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-view:hover:not(:disabled) {
            background: #e5e7eb;
            border-color: #9ca3af;
        }

        .btn-download {
            background: var(--success-color);
            color: white;
        }

        .btn-download:hover:not(:disabled) {
            background: #059669;
        }

        .btn-upload {
            background: var(--primary-color);
            color: white;
        }

        .btn-upload:hover:not(:disabled) {
            background: #5b5bd6;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover:not(:disabled) {
            background: #c82333;
        }

        .btn-action:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 0.75rem;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .close {
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            color: #9ca3af;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: var(--dark-color);
        }

        .modal-body {
            padding: 1.5rem;
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
            transition: all 0.2s ease;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background: #f8fafc;
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .upload-area p {
            margin-bottom: 1rem;
            color: #6b7280;
        }

        .upload-area input[type="file"] {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            margin-bottom: 0.5rem;
        }

        .file-info {
            color: #9ca3af;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: #5b5bd6;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        /* Responsive adjustments for documents */
        @media (max-width: 768px) {
            .documents-grid {
                grid-template-columns: 1fr;
            }

            .document-actions {
                justify-content: center;
            }

            .document-status {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }
        }

        /* Success Popup Styles */
        .success-popup {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: white;
            border: 1px solid #d1fae5;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .success-popup.show {
            transform: translateX(0);
        }

        .success-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 1.5rem;
            color: #065f46;
        }

        .success-content i {
            font-size: 1.25rem;
            color: var(--success-color);
        }

        .success-content span {
            font-weight: 500;
        }

        /* Edit Form Styles */
        .edit-btn {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }

        .edit-btn:hover {
            color: var(--primary-color);
            background: #f3f4f6;
        }

        .edit-form {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-input:invalid {
            border-color: var(--danger-color);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .edit-form .form-grid {
            margin-bottom: 0;
        }

        /* Loading state for save buttons */
        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-primary.loading {
            position: relative;
        }

        .btn-primary.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Shareholders, Directors, Company Secretaries Styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 1.875rem;
            font-weight: 700;
            margin: 0;
        }

        .make-updates-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .make-updates-btn:hover {
            background: #5b5bd6;
            transform: translateY(-1px);
        }

        .tabs-container {
            margin-bottom: 1.5rem;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            gap: 0;
        }

        .tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            text-decoration: none;
            color: #6b7280;
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            position: relative;
        }

        .tab:hover {
            color: var(--primary-color);
            background: #f8fafc;
        }

        .tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: #f8fafc;
        }

        .tab-count {
            background: #e5e7eb;
            color: #374151;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            min-width: 1.5rem;
            text-align: center;
        }

        .tab.active .tab-count {
            background: var(--primary-color);
            color: white;
        }

        .info-alert {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-icon {
            width: 1.5rem;
            height: 1.5rem;
            background: #2196f3;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        .info-text {
            color: #1565c0;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            background: white;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .empty-state h2 {
            color: #6b7280;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .empty-state p {
            color: #9ca3af;
            font-size: 0.875rem;
            margin: 0;
        }

        .data-table {
            background: white;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 0.875rem;
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
            font-size: 0.875rem;
            color: #374151;
        }

        .table tbody tr:hover {
            background: #f8fafc;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .status-verified {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .action-btn {
            background: none;
            border: 1px solid #d1d5db;
            color: #374151;
            padding: 0.375rem 0.75rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-right: 0.5rem;
        }

        .action-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .action-btn.edit {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .action-btn.edit:hover {
            background: #f0f4ff;
        }

        .action-btn.delete {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .action-btn.delete:hover {
            background: #fef2f2;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 0.75rem;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
            border-radius: 0.75rem 0.75rem 0 0;
        }

        .modal-header h2 {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.5rem;
            font-weight: 700;
        }

        .close {
            color: #9ca3af;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s ease;
            line-height: 1;
        }

        .close:hover {
            color: var(--danger-color);
        }

        .modal form {
            padding: 2rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 1.5rem 2rem;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
            border-radius: 0 0 0.75rem 0.75rem;
            margin-top: 1rem;
        }

        .btn-primary,
        .btn-secondary {
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #5b5bd6;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        /* Responsive modal */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                margin: 5% auto;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .modal-header,
            .modal form,
            .modal-footer {
                padding: 1rem;
            }
        }

        /* Loading state */
        .btn-loading {
            opacity: 0.7;
            cursor: not-allowed;
            position: relative;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        /* Tax Clearance Records Styles */
        .tax-clearance-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .tax-clearance-header h1 {
            color: var(--primary-color);
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .add-record-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .add-record-btn:hover {
            background: #5b5bd6;
            transform: translateY(-1px);
        }

        .tax-clearance-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .search-container {
            display: flex;
            align-items: center;
            position: relative;
        }

        .search-input {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem 0 0 0.375rem;
            font-size: 0.875rem;
            width: 200px;
            outline: none;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .search-btn {
            padding: 0.5rem 0.75rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 0 0.375rem 0.375rem 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-btn:hover {
            background: #5b5bd6;
        }

        .display-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: #6b7280;
        }

        .display-controls select {
            padding: 0.25rem 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }

        .view-controls {
            display: flex;
            gap: 0.25rem;
        }

        .view-btn {
            padding: 0.5rem;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 0.25rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .view-btn:hover {
            background: #f3f4f6;
            border-color: var(--primary-color);
        }

        .view-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .tax-empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .tax-empty-state h3 {
            color: #374151;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .tax-empty-state p {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .tax-records-container {
            background: white;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .tax-record-card {
            border-bottom: 1px solid #f3f4f6;
            padding: 1.5rem;
            transition: background-color 0.2s ease;
        }

        .tax-record-card:hover {
            background: #f8fafc;
        }

        .tax-record-card:last-child {
            border-bottom: none;
        }

        .record-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .record-number {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }

        .record-company {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .record-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .record-field {
            display: flex;
            flex-direction: column;
        }

        .record-field-label {
            font-size: 0.75rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.25rem;
        }

        .record-field-value {
            font-size: 0.875rem;
            color: #1f2937;
            font-weight: 500;
        }

        .record-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        .record-action-btn {
            padding: 0.25rem 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .record-action-btn:hover {
            background: #f3f4f6;
        }

        .tax-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8fafc;
            border-top: 1px solid var(--border-color);
        }

        .pagination-info {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .pagination-btn {
            padding: 0.5rem 0.75rem;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f3f4f6;
            border-color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-numbers {
            font-size: 0.875rem;
            color: #374151;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="breadcrumb-nav">
                    <a href="#" onclick="goHome()">
                        <i class="fas fa-arrow-left"></i>
                        Home
                    </a>
                    <span>/</span>
                    <span>Company Details</span>
                </div>
                <h1 class="page-title">Company Details</h1>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <button class="nav-link active" onclick="showSection('company-profile')">
                        <i class="fas fa-user"></i>
                        Company Profile
                    </button>
                </div>
                <div class="nav-item">
                    <button class="nav-link" onclick="showSection('official-documents')">
                        <i class="fas fa-file-alt"></i>
                        Official Documents
                    </button>
                </div>
                <div class="nav-item">
                    <button class="nav-link" onclick="showSection('supporting-documents')">
                        <i class="fas fa-folder"></i>
                        Supporting Documents
                    </button>
                </div>
                <div class="nav-item">
                    <button class="nav-link" onclick="showSection('shareholders')">
                        <i class="fas fa-users"></i>
                        Shareholders
                    </button>
                </div>
                <div class="nav-item">
                    <button class="nav-link" onclick="showSection('directors')">
                        <i class="fas fa-user-tie"></i>
                        Directors
                    </button>
                </div>
                <div class="nav-item">
                    <button class="nav-link" onclick="showSection('company-secretaries')">
                        <i class="fas fa-user-shield"></i>
                        Company Secretaries
                    </button>
                </div>
                <div class="nav-item">
                    <button class="nav-link" onclick="showSection('tax-clearance')">
                        <i class="fas fa-receipt"></i>
                        Tax Clearance Records
                    </button>
                </div>
                <div class="nav-item">
                    <button class="nav-link" onclick="showSection('equipment')">
                        <i class="fas fa-tools"></i>
                        Equipment
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-header">
                <div></div>
                <a href="#" onclick="confirmLogout()" class="logout-btn">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>

            <div class="content-area">
                <!-- Company Profile Section -->
                <div id="company-profile" class="content-section active">
                    <!-- Company Logo and Name Section -->
                    <div class="profile-card">
                        <div class="company-logo-section">
                            <div class="logo-container">
                                <?php if (!empty($firm_data['logo_path']) && file_exists($firm_data['logo_path'])): ?>
                                    <img src="<?php echo htmlspecialchars($firm_data['logo_path']); ?>?t=<?php echo time(); ?>" alt="Company Logo" class="company-logo">
                                <?php else: ?>
                                    <div class="logo-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                                <button class="change-btn" onclick="triggerLogoUpload()">Change</button>
                                <input type="file" id="logoInput" accept="image/*" style="display: none;" onchange="handleLogoUpload(this)">
                            </div>
                            <div class="company-name-info">
                                <div class="section-header">
                                    <h3 class="section-title">Company Name</h3>
                                    <div class="verification-badge">
                                        <span class="company-verified-badge">VERIFIED</span>
                                        <i class="fas fa-info-circle text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-group">
                                    <label class="field-label">Company Name</label>
                                    <div class="field-value"><?php echo htmlspecialchars($firm_data['name']); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Company Registration Details -->
                    <div class="profile-card">
                        <div class="section-header">
                            <h3 class="section-title">Company Registration Details</h3>
                            <div class="verification-badge">
                                <span class="company-verified-badge">VERIFIED</span>
                                <i class="fas fa-info-circle text-gray-400"></i>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="field-group">
                                <label class="field-label">RC Number <span class="required">*</span></label>
                                <div class="field-value"><?php echo htmlspecialchars($firm_data['registration_number'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Company Type <span class="required">*</span></label>
                                <div class="field-value"><?php echo htmlspecialchars($firm_data['company_type'] ?: 'Limited Liability'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Ownership Type <span class="required">*</span></label>
                                <div class="field-value"><?php echo htmlspecialchars($firm_data['ownership_type'] ?: 'Nigeria Owned'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Registration Date <span class="required">*</span></label>
                                <div class="field-value"><?php
                                    if (!empty($firm_data['registration_date'])) {
                                        $date = new DateTime($firm_data['registration_date']);
                                        echo htmlspecialchars($date->format('M j, Y'));
                                    } else {
                                        echo 'Not Available';
                                    }
                                ?></div>
                            </div>
                        </div>
                    </div>

                    <!-- Company TIN -->
                    <div class="profile-card">
                        <div class="section-header">
                            <h3 class="section-title">Company TIN</h3>
                            <div class="verification-badge">
                                <?php
                                $tin_value = $firm_data['tax_id'] ?? $firm_data['tin_number'] ?? '';
                                if (!empty($tin_value)): ?>
                                    <span class="company-verified-badge">VERIFIED</span>
                                    <i class="fas fa-info-circle text-gray-400" title="TIN verified from registration"></i>
                                <?php else: ?>
                                    <span class="status-badge status-pending">PENDING</span>
                                    <i class="fas fa-info-circle text-gray-400" title="TIN not provided during registration"></i>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="field-group">
                            <label class="field-label">FIRS Tax Identification Number <span class="required">*</span></label>
                            <div class="field-value">
                                <?php
                                // Check both tin_number and tax_id fields for backward compatibility
                                $tin_value = $firm_data['tax_id'] ?? $firm_data['tin_number'] ?? '';
                                if (!empty($tin_value)) {
                                    echo htmlspecialchars($tin_value);
                                } else {
                                    echo '<span style="color: #6b7280; font-style: italic;">Not provided during registration</span>';
                                }
                                ?>
                            </div>
                            <?php if (!empty($tin_value)): ?>
                                <small class="field-note">
                                    <i class="fas fa-check-circle text-success"></i>
                                    This TIN was provided and verified during firm registration.
                                </small>
                            <?php else: ?>
                                <small class="field-note">
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                    TIN was not provided during registration. Contact support to update your TIN.
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Company Address -->
                    <div class="profile-card" id="address-section">
                        <div class="section-header">
                            <h3 class="section-title">Company Address</h3>
                            <div class="verification-badge">
                                <span class="company-verified-badge">VERIFIED</span>
                                <i class="fas fa-info-circle text-gray-400 me-2"></i>
                                <button class="edit-btn" onclick="toggleEdit('address')" id="address-edit-btn">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-grid" id="address-display">
                            <div class="field-group">
                                <label class="field-label">Street <span class="required">*</span></label>
                                <div class="field-value" id="address-street-display"><?php echo htmlspecialchars($firm_data['address'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Area/City <span class="required">*</span></label>
                                <div class="field-value" id="address-city-display"><?php echo htmlspecialchars($firm_data['city'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">State <span class="required">*</span></label>
                                <div class="field-value" id="address-state-display"><?php echo htmlspecialchars($firm_data['state'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Zip Code <span class="required">*</span></label>
                                <div class="field-value" id="address-postal-display"><?php echo htmlspecialchars($firm_data['postal_code'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Country <span class="required">*</span></label>
                                <div class="field-value">Nigeria</div>
                            </div>
                        </div>

                        <!-- Edit Form (Hidden by default) -->
                        <form class="edit-form" id="address-edit-form" style="display: none;">
                            <div class="form-grid">
                                <div class="field-group">
                                    <label class="field-label">Street <span class="required">*</span></label>
                                    <input type="text" class="form-input" id="address-street" name="address" value="<?php echo htmlspecialchars($firm_data['address'] ?? ''); ?>" required>
                                </div>

                                <div class="field-group">
                                    <label class="field-label">Area/City <span class="required">*</span></label>
                                    <input type="text" class="form-input" id="address-city" name="city" value="<?php echo htmlspecialchars($firm_data['city'] ?? ''); ?>" required>
                                </div>

                                <div class="field-group">
                                    <label class="field-label">State <span class="required">*</span></label>
                                    <input type="text" class="form-input" id="address-state" name="state" value="<?php echo htmlspecialchars($firm_data['state'] ?? ''); ?>" required>
                                </div>

                                <div class="field-group">
                                    <label class="field-label">Zip Code <span class="required">*</span></label>
                                    <input type="text" class="form-input" id="address-postal" name="postal_code" value="<?php echo htmlspecialchars($firm_data['postal_code'] ?? ''); ?>" required>
                                </div>

                                <div class="field-group">
                                    <label class="field-label">Country <span class="required">*</span></label>
                                    <select class="form-input" name="country">
                                        <option value="Nigeria" selected>Nigeria</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-secondary" onclick="cancelEdit('address')">Cancel</button>
                                <button type="submit" class="btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>

                    <!-- Company Contact Information -->
                    <div class="profile-card" id="contact-section">
                        <div class="section-header">
                            <h3 class="section-title">Company Contact Information</h3>
                            <div class="verification-badge">
                                <button class="edit-btn" onclick="toggleEdit('contact')" id="contact-edit-btn">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-grid" id="contact-display">
                            <div class="field-group">
                                <label class="field-label">Email Address <span class="required">*</span></label>
                                <div class="field-value" id="contact-email-display"><?php echo htmlspecialchars($firm_data['email'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Alternative Email Address</label>
                                <div class="field-value" id="contact-alt-email-display"><?php echo htmlspecialchars($firm_data['alternative_email'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Phone Number <span class="required">*</span></label>
                                <div class="field-value" id="contact-phone-display"><?php echo htmlspecialchars($firm_data['phone'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Website</label>
                                <div class="field-value" id="contact-website-display"><?php echo htmlspecialchars($firm_data['website'] ?: 'Not Available'); ?></div>
                            </div>
                        </div>

                        <!-- Edit Form (Hidden by default) -->
                        <form class="edit-form" id="contact-edit-form" style="display: none;">
                            <div class="form-grid">
                                <div class="field-group">
                                    <label class="field-label">Email Address <span class="required">*</span></label>
                                    <input type="email" class="form-input" id="contact-email" name="email" value="<?php echo htmlspecialchars($firm_data['email'] ?? ''); ?>" required>
                                </div>

                                <div class="field-group">
                                    <label class="field-label">Alternative Email Address</label>
                                    <input type="email" class="form-input" id="contact-alt-email" name="alternative_email" value="<?php echo htmlspecialchars($firm_data['alternative_email'] ?? ''); ?>">
                                </div>

                                <div class="field-group">
                                    <label class="field-label">Phone Number <span class="required">*</span></label>
                                    <input type="tel" class="form-input" id="contact-phone" name="phone" value="<?php echo htmlspecialchars($firm_data['phone'] ?? ''); ?>" required>
                                </div>

                                <div class="field-group">
                                    <label class="field-label">Website</label>
                                    <input type="url" class="form-input" id="contact-website" name="website" value="<?php echo htmlspecialchars($firm_data['website'] ?? ''); ?>" placeholder="https://example.com">
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-secondary" onclick="cancelEdit('contact')">Cancel</button>
                                <button type="submit" class="btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>

                    <!-- Company CEO Details -->
                    <div class="profile-card" id="ceo-section">
                        <div class="section-header">
                            <h3 class="section-title">Company CEO Details</h3>
                            <div class="verification-badge">
                                <button class="edit-btn" onclick="toggleEdit('ceo')" id="ceo-edit-btn">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-grid" id="ceo-display">
                            <div class="field-group">
                                <label class="field-label">Name of CEO <span class="required">*</span></label>
                                <div class="field-value" id="ceo-name-display"><?php echo htmlspecialchars($firm_data['ceo_name'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Email Address <span class="required">*</span></label>
                                <div class="field-value" id="ceo-email-display"><?php echo htmlspecialchars($firm_data['ceo_email'] ?: 'Not Available'); ?></div>
                            </div>

                            <div class="field-group">
                                <label class="field-label">Phone Number <span class="required">*</span></label>
                                <div class="field-value" id="ceo-phone-display"><?php echo htmlspecialchars($firm_data['ceo_phone'] ?: 'Not Available'); ?></div>
                            </div>
                        </div>

                        <!-- Edit Form (Hidden by default) -->
                        <form class="edit-form" id="ceo-edit-form" style="display: none;">
                            <div class="form-grid">
                                <div class="field-group">
                                    <label class="field-label">Name of CEO <span class="required">*</span></label>
                                    <input type="text" class="form-input" id="ceo-name" name="ceo_name" value="<?php echo htmlspecialchars($firm_data['ceo_name'] ?? ''); ?>" required>
                                </div>

                                <div class="field-group">
                                    <label class="field-label">Email Address <span class="required">*</span></label>
                                    <input type="email" class="form-input" id="ceo-email" name="ceo_email" value="<?php echo htmlspecialchars($firm_data['ceo_email'] ?? ''); ?>" required>
                                </div>

                                <div class="field-group">
                                    <label class="field-label">Phone Number <span class="required">*</span></label>
                                    <input type="tel" class="form-input" id="ceo-phone" name="ceo_phone" value="<?php echo htmlspecialchars($firm_data['ceo_phone'] ?? ''); ?>" required>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-secondary" onclick="cancelEdit('ceo')">Cancel</button>
                                <button type="submit" class="btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Official Documents Section -->
                <div id="official-documents" class="content-section active">
                    <div class="documents-header">
                        <h1 class="documents-title">Official Documents</h1>
                        <p class="documents-subtitle">Manage and view your company's official documents</p>

                        <!-- Information Message -->
                        <div class="info-message">
                            <i class="fas fa-info-circle"></i>
                            <div class="info-content">
                                <p>Please confirm that your official documents have been uploaded and are accurate and up-to-date. If any information is incorrect, kindly click on the edit icon to make the necessary updates. However, you can send us a chat message <a href="#" onclick="openContactModal()" class="contact-link">here</a> if you have any question or feedback.</p>
                            </div>
                        </div>

                        <!-- Document Summary -->
                        <div class="documents-summary">
                            <div class="summary-card">
                                <div class="summary-icon">
                                    <i class="fas fa-file-check text-success"></i>
                                </div>
                                <div class="summary-info">
                                    <span class="summary-count" id="uploaded-count">
                                        <?php
                                        $uploaded_count = 0;
                                        $official_docs = ['memart', 'cac_status', 'tax_clearance', 'utility_bill', 'cac_certificate'];
                                        foreach ($official_docs as $doc_type) {
                                            $status = getDocumentStatus($doc_type, $document_statuses);
                                            if ($status['has_file']) {
                                                $uploaded_count++;
                                            }
                                        }
                                        echo $uploaded_count;
                                        ?>
                                    </span>
                                    <span class="summary-label">Uploaded</span>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">
                                    <i class="fas fa-file-times text-warning"></i>
                                </div>
                                <div class="summary-info">
                                    <span class="summary-count" id="pending-count">
                                        <?php
                                        $pending_count = 0;
                                        foreach ($official_docs as $doc_type) {
                                            $status = getDocumentStatus($doc_type, $document_statuses);
                                            if (!$status['has_file']) {
                                                $pending_count++;
                                            }
                                        }
                                        echo $pending_count;
                                        ?>
                                    </span>
                                    <span class="summary-label">Pending</span>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">
                                    <i class="fas fa-files text-primary"></i>
                                </div>
                                <div class="summary-info">
                                    <span class="summary-count" id="total-count">
                                        <?php echo count($official_docs); ?>
                                    </span>
                                    <span class="summary-label">Total Documents</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="documents-grid">
                        <!-- CTC of Memorandum & Articles of Association (MEMART) -->
                        <div class="document-card">
                            <div class="document-number">1</div>
                            <div class="document-icon">
                                <i class="fas fa-file-contract"></i>
                            </div>
                            <div class="document-info">
                                <h3 class="document-title">CTC of Memorandum & Articles of Association (MEMART)</h3>
                                <p class="document-description">Certified True Copy of company's constitutional documents</p>
                                <div class="document-status">
                                    <?php
                                    $memart_status = getDocumentStatus('memart', $document_statuses);
                                    if ($memart_status['has_file']): ?>
                                        <span class="status-badge status-uploaded">
                                            <i class="fas fa-check-circle"></i>
                                            Uploaded
                                        </span>
                                        <span class="document-date">Last updated: <?php echo $memart_status['upload_date']; ?></span>
                                    <?php else: ?>
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock"></i>
                                            Pending Upload
                                        </span>
                                        <span class="document-date">Required for compliance</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="document-actions">
                                <button class="btn-action btn-upload" onclick="uploadDocument('memart')">
                                    <i class="fas fa-upload"></i>
                                    <?php echo $memart_status['has_file'] ? 'Replace' : 'Upload'; ?>
                                </button>
                                <?php if ($memart_status['has_file']): ?>
                                <button class="btn-action btn-delete" onclick="deleteDocument('memart')">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- CAC Company Status Report -->
                        <div class="document-card">
                            <div class="document-number">2</div>
                            <div class="document-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="document-info">
                                <h3 class="document-title">CAC Company Status Report</h3>
                                <p class="document-description">Current status report from Corporate Affairs Commission</p>
                                <div class="document-status">
                                    <?php
                                    $cac_status = getDocumentStatus('cac_status', $document_statuses);
                                    if ($cac_status['has_file']): ?>
                                        <span class="status-badge status-uploaded">
                                            <i class="fas fa-check-circle"></i>
                                            Uploaded
                                        </span>
                                        <span class="document-date">Last updated: <?php echo $cac_status['upload_date']; ?></span>
                                    <?php else: ?>
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock"></i>
                                            Pending Upload
                                        </span>
                                        <span class="document-date">Required for compliance</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="document-actions">
                                <button class="btn-action btn-upload" onclick="uploadDocument('cac_status')">
                                    <i class="fas fa-upload"></i>
                                    <?php echo $cac_status['has_file'] ? 'Replace' : 'Upload'; ?>
                                </button>
                                <?php if ($cac_status['has_file']): ?>
                                <button class="btn-action btn-delete" onclick="deleteDocument('cac_status')">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Current Tax Clearance Certificate -->
                        <div class="document-card">
                            <div class="document-number">3</div>
                            <div class="document-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="document-info">
                                <h3 class="document-title">Current Tax Clearance Certificate</h3>
                                <p class="document-description">Valid tax clearance certificate from FIRS</p>
                                <div class="document-status">
                                    <?php
                                    $tax_clearance_status = getDocumentStatus('tax_clearance', $document_statuses);
                                    if ($tax_clearance_status['has_file']): ?>
                                        <span class="status-badge status-uploaded">
                                            <i class="fas fa-check-circle"></i>
                                            Uploaded
                                        </span>
                                        <span class="document-date">Last updated: <?php echo $tax_clearance_status['upload_date']; ?></span>
                                    <?php else: ?>
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock"></i>
                                            Pending Upload
                                        </span>
                                        <span class="document-date">Required for compliance</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="document-actions">
                                <button class="btn-action btn-upload" onclick="uploadDocument('tax_clearance')">
                                    <i class="fas fa-upload"></i>
                                    <?php echo $tax_clearance_status['has_file'] ? 'Replace' : 'Upload'; ?>
                                </button>
                                <?php if ($tax_clearance_status['has_file']): ?>
                                <button class="btn-action btn-delete" onclick="deleteDocument('tax_clearance')">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Current Utility Bill -->
                        <div class="document-card">
                            <div class="document-number">4</div>
                            <div class="document-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div class="document-info">
                                <h3 class="document-title">Current Utility Bill</h3>
                                <p class="document-description">Recent utility bill for address verification</p>
                                <div class="document-status">
                                    <?php
                                    $utility_bill_status = getDocumentStatus('utility_bill', $document_statuses);
                                    if ($utility_bill_status['has_file']): ?>
                                        <span class="status-badge status-uploaded">
                                            <i class="fas fa-check-circle"></i>
                                            Uploaded
                                        </span>
                                        <span class="document-date">Last updated: <?php echo $utility_bill_status['upload_date']; ?></span>
                                    <?php else: ?>
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock"></i>
                                            Pending Upload
                                        </span>
                                        <span class="document-date">Required for compliance</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="document-actions">
                                <button class="btn-action btn-upload" onclick="uploadDocument('utility_bill')">
                                    <i class="fas fa-upload"></i>
                                    <?php echo $utility_bill_status['has_file'] ? 'Replace' : 'Upload'; ?>
                                </button>
                                <?php if ($utility_bill_status['has_file']): ?>
                                <button class="btn-action btn-delete" onclick="deleteDocument('utility_bill')">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- CAC Certificate of Incorporation -->
                        <div class="document-card">
                            <div class="document-number">5</div>
                            <div class="document-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <div class="document-info">
                                <h3 class="document-title">CAC Certificate of Incorporation</h3>
                                <p class="document-description">Official certificate of company incorporation</p>
                                <div class="document-status">
                                    <?php
                                    $incorporation_cert_status = getDocumentStatus('incorporation_cert', $document_statuses);
                                    if ($incorporation_cert_status['has_file']): ?>
                                        <span class="status-badge status-uploaded">
                                            <i class="fas fa-check-circle"></i>
                                            Uploaded
                                        </span>
                                        <span class="document-date">Last updated: <?php echo $incorporation_cert_status['upload_date']; ?></span>
                                    <?php else: ?>
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock"></i>
                                            Pending Upload
                                        </span>
                                        <span class="document-date">Required for compliance</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="document-actions">
                                <button class="btn-action btn-upload" onclick="uploadDocument('incorporation_cert')">
                                    <i class="fas fa-upload"></i>
                                    <?php echo $incorporation_cert_status['has_file'] ? 'Replace' : 'Upload'; ?>
                                </button>
                                <?php if ($incorporation_cert_status['has_file']): ?>
                                <button class="btn-action btn-delete" onclick="deleteDocument('incorporation_cert')">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Document Upload Modal -->
                    <div id="uploadModal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3 id="modalTitle">Upload Document</h3>
                                <span class="close" onclick="closeUploadModal()">&times;</span>
                            </div>
                            <div class="modal-body">
                                <form id="documentUploadForm" enctype="multipart/form-data">
                                    <input type="hidden" id="documentType" name="document_type">
                                    <div class="upload-area">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <p>Drag and drop your file here or click to browse</p>
                                        <input type="file" id="documentFile" name="document" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
                                        <div class="file-info">
                                            <small>Supported formats: PDF, JPG, PNG (Max size: 10MB)</small>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn-secondary" onclick="closeUploadModal()">Cancel</button>
                                        <button type="submit" class="btn-primary">Upload Document</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Admin Modal -->
                <div id="contactModal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Contact Administrator</h3>
                            <span class="close" onclick="closeContactModal()">&times;</span>
                        </div>
                        <div class="modal-body">
                            <form id="contactForm">
                                <div class="form-group">
                                    <label for="contactSubject">Subject:</label>
                                    <select id="contactSubject" name="subject" required>
                                        <option value="">Select a subject</option>
                                        <option value="document_issue">Document Upload Issue</option>
                                        <option value="document_correction">Document Information Correction</option>
                                        <option value="technical_support">Technical Support</option>
                                        <option value="general_inquiry">General Inquiry</option>
                                        <option value="feedback">Feedback</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="contactMessage">Message:</label>
                                    <textarea id="contactMessage" name="message" rows="5" placeholder="Please describe your question or feedback..." required></textarea>
                                </div>

                                <div class="form-actions">
                                    <button type="button" class="btn-secondary" onclick="closeContactModal()">Cancel</button>
                                    <button type="submit" class="btn-primary">Send Message</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Supporting Documents Section -->
                <div id="supporting-documents" class="content-section">
                    <div class="documents-header">
                        <h1 class="documents-title">Supporting Documents</h1>
                        <p class="documents-subtitle">Upload required supporting documents for tax registration verification</p>

                        <!-- Information Message -->
                        <div class="info-message">
                            <i class="fas fa-info-circle"></i>
                            <div class="info-content">
                                <p>Please upload the required supporting documents below. Mandatory documents are required for registration completion, while optional documents may be needed based on your specific circumstances.</p>
                            </div>
                        </div>

                        <!-- Progress Summary -->
                        <?php
                        $mandatory_count = 0;
                        $mandatory_uploaded = 0;
                        $total_uploaded = 0;

                        foreach ($supporting_documents as $doc) {
                            if ($doc['type'] === 'MANDATORY') {
                                $mandatory_count++;
                                if (isset($document_statuses[$doc['id']])) {
                                    $mandatory_uploaded++;
                                }
                            }
                            if (isset($document_statuses[$doc['id']])) {
                                $total_uploaded++;
                            }
                        }

                        $progress_percentage = $mandatory_count > 0 ? ($mandatory_uploaded / $mandatory_count) * 100 : 0;
                        ?>

                        <div class="documents-summary">
                            <div class="summary-card">
                                <div class="summary-icon">
                                    <i class="fas fa-file-check text-success"></i>
                                </div>
                                <div class="summary-info">
                                    <span class="summary-count"><?php echo $total_uploaded; ?></span>
                                    <span class="summary-label">Uploaded</span>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">
                                    <i class="fas fa-file-times text-warning"></i>
                                </div>
                                <div class="summary-info">
                                    <span class="summary-count"><?php echo count($supporting_documents) - $total_uploaded; ?></span>
                                    <span class="summary-label">Pending</span>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">
                                    <i class="fas fa-files text-primary"></i>
                                </div>
                                <div class="summary-info">
                                    <span class="summary-count"><?php echo count($supporting_documents); ?></span>
                                    <span class="summary-label">Total Documents</span>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">
                                    <i class="fas fa-chart-pie text-info"></i>
                                </div>
                                <div class="summary-info">
                                    <span class="summary-count"><?php echo round($progress_percentage); ?>%</span>
                                    <span class="summary-label">Completion</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="documents-grid">
                        <?php foreach ($supporting_documents as $index => $doc): ?>
                            <?php $doc_status = getDocumentStatus($doc['id'], $document_statuses); ?>
                            <div class="document-card">
                                <div class="document-number <?php echo $doc['type'] === 'MANDATORY' ? 'mandatory' : ''; ?>"><?php echo $index + 1; ?></div>
                                <div class="document-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="document-info">
                                    <h3 class="document-title"><?php echo htmlspecialchars($doc['title']); ?></h3>
                                    <p class="document-description"><?php echo htmlspecialchars($doc['description']); ?></p>
                                    <div class="document-status">
                                        <?php if ($doc_status['has_file']): ?>
                                            <span class="status-badge status-uploaded">
                                                <i class="fas fa-check-circle"></i>
                                                Uploaded
                                            </span>
                                            <span class="document-date">
                                                <?php echo $doc_status['upload_date'] ? $doc_status['upload_date'] : 'Recently uploaded'; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="status-badge status-pending">
                                                <i class="fas fa-clock"></i>
                                                <?php echo $doc['type'] === 'MANDATORY' ? 'Required' : 'Optional'; ?>
                                            </span>
                                            <span class="document-date"><?php echo $doc['type'] === 'MANDATORY' ? 'Required for compliance' : 'Upload if applicable'; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="document-actions">
                                    <button class="btn-action btn-upload" onclick="uploadSupportingDocument('<?php echo $doc['id']; ?>')">
                                        <i class="fas fa-upload"></i>
                                        <?php echo $doc_status['has_file'] ? 'Replace' : 'Upload'; ?>
                                    </button>
                                    <?php if ($doc_status['has_file']): ?>
                                    <button class="btn-action btn-view" onclick="viewSupportingDocument('<?php echo $doc['id']; ?>')">
                                        <i class="fas fa-eye"></i>
                                        View
                                    </button>
                                    <button class="btn-action btn-download" onclick="downloadSupportingDocument('<?php echo $doc['id']; ?>')">
                                        <i class="fas fa-download"></i>
                                        Download
                                    </button>
                                    <button class="btn-action btn-delete" onclick="deleteSupportingDocument('<?php echo $doc['id']; ?>')">
                                        <i class="fas fa-trash"></i>
                                        Delete
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Supporting Document Upload Modal -->
                    <div id="supportingUploadModal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3 id="supportingModalTitle">Upload Supporting Document</h3>
                                <span class="close" onclick="closeSupportingUploadModal()">&times;</span>
                            </div>
                            <div class="modal-body">
                                <form id="supportingDocumentUploadForm" enctype="multipart/form-data">
                                    <input type="hidden" id="supportingDocumentType" name="document_type">
                                    <div class="upload-area">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <p>Drag and drop your file here or click to browse</p>
                                        <input type="file" id="supportingDocumentFile" name="document" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
                                        <div class="file-info">
                                            <small>Supported formats: PDF, JPG, PNG, DOC, DOCX (Max size: 10MB)</small>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn-secondary" onclick="closeSupportingUploadModal()">Cancel</button>
                                        <button type="submit" class="btn-primary">Upload Document</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other sections -->
                <div id="shareholders" class="content-section">
                    <!-- Header -->
                    <div class="header">
                        <h1>Shareholders</h1>
                        <button class="make-updates-btn" onclick="openShareholdersModal()">
                            Make Updates
                        </button>
                    </div>

                    <!-- Tabs -->
                    <div class="tabs-container">
                        <div class="tabs">
                            <a href="#" class="tab active" onclick="switchShareholdersTab(this, 'verified')">
                                Verified
                                <span class="tab-count" id="shareholders-verified-count">0</span>
                            </a>
                            <a href="#" class="tab" onclick="switchShareholdersTab(this, 'pending')">
                                Pending Verification
                                <span class="tab-count" id="shareholders-pending-count">0</span>
                            </a>
                            <a href="#" class="tab" onclick="switchShareholdersTab(this, 'history')">
                                History
                                <span class="tab-count" id="shareholders-history-count">0</span>
                            </a>
                        </div>
                    </div>

                    <!-- Info Alert -->
                    <div class="info-alert">
                        <div class="info-icon">i</div>
                        <div class="info-text">
                            These are the current list of shareholders you've provided. If you wish to make any updates,
                            please click the 'Make Updates' button to proceed.
                        </div>
                    </div>

                    <!-- Content Area -->
                    <div id="shareholders-content-area">
                        <!-- Empty State (default) -->
                        <div class="empty-state" id="shareholders-empty-state">
                            <h2>This Section has no verified Record</h2>
                            <p>Click on the Update Button to make Updates</p>
                        </div>

                        <!-- Data Table (hidden by default) -->
                        <div class="data-table" id="shareholders-data-table" style="display: none;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Number of Shares</th>
                                        <th>Percentage</th>
                                        <th>Date Added</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="shareholders-table-body">
                                    <!-- Sample data - will be replaced with dynamic content -->
                                    <tr style="display: none;">
                                        <td>John Smith</td>
                                        <td>10,000</td>
                                        <td>25.5%</td>
                                        <td>Jan 15, 2024</td>
                                        <td><span class="status-badge status-verified">Verified</span></td>
                                        <td>
                                            <button class="action-btn edit" onclick="editShareholder('john-smith')">Edit</button>
                                            <button class="action-btn delete" onclick="deleteShareholder('john-smith')">Delete</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>



                <div id="directors" class="content-section">
                    <!-- Header -->
                    <div class="header">
                        <h1>Directors</h1>
                        <button class="make-updates-btn" onclick="openDirectorsModal()">
                            Make Updates
                        </button>
                    </div>

                    <!-- Tabs -->
                    <div class="tabs-container">
                        <div class="tabs">
                            <a href="#" class="tab active" onclick="switchDirectorsTab(this, 'verified')">
                                Verified
                                <span class="tab-count" id="directors-verified-count">0</span>
                            </a>
                            <a href="#" class="tab" onclick="switchDirectorsTab(this, 'pending')">
                                Pending Verification
                                <span class="tab-count" id="directors-pending-count">0</span>
                            </a>
                            <a href="#" class="tab" onclick="switchDirectorsTab(this, 'history')">
                                History
                                <span class="tab-count" id="directors-history-count">0</span>
                            </a>
                        </div>
                    </div>

                    <!-- Info Alert -->
                    <div class="info-alert">
                        <div class="info-icon">i</div>
                        <div class="info-text">
                            These are the current list of directors you've provided. If you wish to make any updates,
                            please click the 'Make Updates' button to proceed.
                        </div>
                    </div>

                    <!-- Content Area -->
                    <div id="directors-content-area">
                        <!-- Empty State (default) -->
                        <div class="empty-state" id="directors-empty-state">
                            <h2>This Section has no verified Record</h2>
                            <p>Click on the Update Button to make Updates</p>
                        </div>

                        <!-- Data Table (hidden by default) -->
                        <div class="data-table" id="directors-data-table" style="display: none;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Position</th>
                                        <th>Appointment Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="directors-table-body">
                                    <!-- Sample data - will be replaced with dynamic content -->
                                    <tr style="display: none;">
                                        <td>John Smith</td>
                                        <td>Managing Director</td>
                                        <td>Jan 15, 2024</td>
                                        <td><span class="status-badge status-verified">Verified</span></td>
                                        <td>
                                            <button class="action-btn edit" onclick="editDirector('john-smith')">Edit</button>
                                            <button class="action-btn delete" onclick="deleteDirector('john-smith')">Delete</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div id="company-secretaries" class="content-section">
                    <!-- Header -->
                    <div class="header">
                        <h1>Company Secretaries</h1>
                        <button class="make-updates-btn" onclick="openSecretariesModal()">
                            Make Updates
                        </button>
                    </div>

                    <!-- Tabs -->
                    <div class="tabs-container">
                        <div class="tabs">
                            <a href="#" class="tab active" onclick="switchSecretariesTab(this, 'verified')">
                                Verified
                                <span class="tab-count" id="secretaries-verified-count">0</span>
                            </a>
                            <a href="#" class="tab" onclick="switchSecretariesTab(this, 'pending')">
                                Pending Verification
                                <span class="tab-count" id="secretaries-pending-count">0</span>
                            </a>
                            <a href="#" class="tab" onclick="switchSecretariesTab(this, 'history')">
                                History
                                <span class="tab-count" id="secretaries-history-count">0</span>
                            </a>
                        </div>
                    </div>

                    <!-- Info Alert -->
                    <div class="info-alert">
                        <div class="info-icon">i</div>
                        <div class="info-text">
                            These are the current list of company secretaries you've provided. If you wish to make any updates,
                            please click the 'Make Updates' button to proceed.
                        </div>
                    </div>

                    <!-- Content Area -->
                    <div id="secretaries-content-area">
                        <!-- Empty State (default) -->
                        <div class="empty-state" id="secretaries-empty-state">
                            <h2>This Section has no verified Record</h2>
                            <p>Click on the Update Button to make Updates</p>
                        </div>

                        <!-- Data Table (hidden by default) -->
                        <div class="data-table" id="secretaries-data-table" style="display: none;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Qualification</th>
                                        <th>Appointment Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="secretaries-table-body">
                                    <!-- Sample data - will be replaced with dynamic content -->
                                    <tr style="display: none;">
                                        <td>Jane Doe</td>
                                        <td>ICSAN Certified</td>
                                        <td>Feb 10, 2024</td>
                                        <td><span class="status-badge status-verified">Verified</span></td>
                                        <td>
                                            <button class="action-btn edit" onclick="editSecretary('jane-doe')">Edit</button>
                                            <button class="action-btn delete" onclick="deleteSecretary('jane-doe')">Delete</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Shareholders Modal -->
                <div id="shareholdersModal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>Add New Shareholder</h2>
                            <span class="close" onclick="closeShareholdersModal()">&times;</span>
                        </div>
                        <form id="shareholdersForm" onsubmit="submitShareholder(event)">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="shareholder-name">Full Name *</label>
                                    <input type="text" id="shareholder-name" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label for="shareholder-email">Email Address</label>
                                    <input type="email" id="shareholder-email" name="email">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="shareholder-phone">Phone Number</label>
                                    <input type="tel" id="shareholder-phone" name="phone">
                                </div>
                                <div class="form-group">
                                    <label for="shareholder-shares">Number of Shares *</label>
                                    <input type="number" id="shareholder-shares" name="number_of_shares" required min="1">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="shareholder-percentage">Share Percentage *</label>
                                    <input type="number" id="shareholder-percentage" name="share_percentage" required min="0" max="100" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label for="shareholder-class">Share Class</label>
                                    <select id="shareholder-class" name="share_class">
                                        <option value="Ordinary">Ordinary</option>
                                        <option value="Preference">Preference</option>
                                        <option value="Redeemable">Redeemable</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="shareholder-date">Appointment Date</label>
                                    <input type="date" id="shareholder-date" name="appointment_date">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="shareholder-address">Address</label>
                                <textarea id="shareholder-address" name="address" rows="3"></textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn-secondary" onclick="closeShareholdersModal()">Cancel</button>
                                <button type="submit" class="btn-primary">Add Shareholder</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Directors Modal -->
                <div id="directorsModal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>Add New Director</h2>
                            <span class="close" onclick="closeDirectorsModal()">&times;</span>
                        </div>
                        <form id="directorsForm" onsubmit="submitDirector(event)">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="director-name">Full Name *</label>
                                    <input type="text" id="director-name" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label for="director-email">Email Address</label>
                                    <input type="email" id="director-email" name="email">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="director-phone">Phone Number</label>
                                    <input type="tel" id="director-phone" name="phone">
                                </div>
                                <div class="form-group">
                                    <label for="director-position">Position *</label>
                                    <select id="director-position" name="position" required>
                                        <option value="">Select Position</option>
                                        <option value="Managing Director">Managing Director</option>
                                        <option value="Executive Director">Executive Director</option>
                                        <option value="Non-Executive Director">Non-Executive Director</option>
                                        <option value="Independent Director">Independent Director</option>
                                        <option value="Chairman">Chairman</option>
                                        <option value="Deputy Chairman">Deputy Chairman</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="director-nationality">Nationality</label>
                                    <input type="text" id="director-nationality" name="nationality" value="Nigerian">
                                </div>
                                <div class="form-group">
                                    <label for="director-date">Appointment Date</label>
                                    <input type="date" id="director-date" name="appointment_date">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="director-id-type">Identification Type</label>
                                    <select id="director-id-type" name="identification_type">
                                        <option value="NIN">NIN</option>
                                        <option value="International Passport">International Passport</option>
                                        <option value="Driver License">Driver License</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="director-id-number">Identification Number</label>
                                    <input type="text" id="director-id-number" name="identification_number">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="director-address">Address</label>
                                <textarea id="director-address" name="address" rows="3"></textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn-secondary" onclick="closeDirectorsModal()">Cancel</button>
                                <button type="submit" class="btn-primary">Add Director</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Company Secretaries Modal -->
                <div id="secretariesModal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>Add New Company Secretary</h2>
                            <span class="close" onclick="closeSecretariesModal()">&times;</span>
                        </div>
                        <form id="secretariesForm" onsubmit="submitSecretary(event)">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="secretary-name">Full Name *</label>
                                    <input type="text" id="secretary-name" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label for="secretary-email">Email Address</label>
                                    <input type="email" id="secretary-email" name="email">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="secretary-phone">Phone Number</label>
                                    <input type="tel" id="secretary-phone" name="phone">
                                </div>
                                <div class="form-group">
                                    <label for="secretary-qualification">Qualification *</label>
                                    <input type="text" id="secretary-qualification" name="qualification" required placeholder="e.g., ICSAN Certified">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="secretary-body">Professional Body</label>
                                    <select id="secretary-body" name="professional_body">
                                        <option value="">Select Professional Body</option>
                                        <option value="ICSAN">Institute of Chartered Secretaries and Administrators of Nigeria (ICSAN)</option>
                                        <option value="ICAN">Institute of Chartered Accountants of Nigeria (ICAN)</option>
                                        <option value="NBA">Nigerian Bar Association (NBA)</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="secretary-membership">Membership Number</label>
                                    <input type="text" id="secretary-membership" name="membership_number">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="secretary-date">Appointment Date</label>
                                    <input type="date" id="secretary-date" name="appointment_date">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="secretary-address">Address</label>
                                <textarea id="secretary-address" name="address" rows="3"></textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn-secondary" onclick="closeSecretariesModal()">Cancel</button>
                                <button type="submit" class="btn-primary">Add Company Secretary</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Tax Clearance Modal -->
                <div id="taxClearanceModal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>Add New Tax Clearance Certificate</h2>
                            <span class="close" onclick="closeTaxClearanceModal()">&times;</span>
                        </div>
                        <form id="taxClearanceForm" onsubmit="submitTaxClearance(event)">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="tax-certificate-number">Certificate Number *</label>
                                    <input type="text" id="tax-certificate-number" name="certificate_number" required placeholder="e.g., 22547787799">
                                </div>
                                <div class="form-group">
                                    <label for="tax-company-name">Company Name *</label>
                                    <input type="text" id="tax-company-name" name="company_name" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="tax-issue-date">Issue Date *</label>
                                    <input type="date" id="tax-issue-date" name="issue_date" required>
                                </div>
                                <div class="form-group">
                                    <label for="tax-expiry-date">Expiry Date *</label>
                                    <input type="date" id="tax-expiry-date" name="expiry_date" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="tax-office">Tax Office *</label>
                                    <select id="tax-office" name="tax_office" required>
                                        <option value="">Select Tax Office</option>
                                        <option value="MSTO ISOLO">MSTO ISOLO</option>
                                        <option value="LAGOS ISLAND">LAGOS ISLAND</option>
                                        <option value="IKEJA">IKEJA</option>
                                        <option value="VICTORIA ISLAND">VICTORIA ISLAND</option>
                                        <option value="ABUJA CENTRAL">ABUJA CENTRAL</option>
                                        <option value="KANO">KANO</option>
                                        <option value="PORT HARCOURT">PORT HARCOURT</option>
                                        <option value="IBADAN">IBADAN</option>
                                        <option value="ENUGU">ENUGU</option>
                                        <option value="KADUNA">KADUNA</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="tax-amount">Tax Amount (₦)</label>
                                    <input type="number" id="tax-amount" name="tax_amount" min="0" step="0.01" placeholder="0.00">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="tax-year">Tax Year *</label>
                                    <select id="tax-year" name="tax_year" required>
                                        <option value="">Select Tax Year</option>
                                        <option value="2024">2024</option>
                                        <option value="2023">2023</option>
                                        <option value="2022">2022</option>
                                        <option value="2021">2021</option>
                                        <option value="2020">2020</option>
                                        <option value="2019">2019</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="tax-status">Status</label>
                                    <select id="tax-status" name="status">
                                        <option value="ISSUED">ISSUED</option>
                                        <option value="EXPIRED">EXPIRED</option>
                                        <option value="REVOKED">REVOKED</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="tax-notes">Additional Notes</label>
                                <textarea id="tax-notes" name="notes" rows="3" placeholder="Any additional information about this certificate..."></textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn-secondary" onclick="closeTaxClearanceModal()">Cancel</button>
                                <button type="submit" class="btn-primary">Add Certificate</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div id="tax-clearance" class="content-section">
                    <!-- Header -->
                    <div class="tax-clearance-header">
                        <h1>FIRS Tax Clearance Certificate Records</h1>
                        <button class="add-record-btn" onclick="openTaxClearanceModal()">
                            Add New Record
                        </button>
                    </div>

                    <!-- Search and Filter Controls -->
                    <div class="tax-clearance-controls">
                        <div class="search-container">
                            <input type="text" id="tax-search" placeholder="Search..." class="search-input">
                            <button class="search-btn" onclick="searchTaxRecords()">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.35-4.35"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="display-controls">
                            <label for="display-select">Display</label>
                            <select id="display-select" onchange="updateDisplayCount()">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>Rows</span>
                        </div>
                        <div class="view-controls">
                            <button class="view-btn" onclick="toggleView('grid')" title="Grid View">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="7" height="7"></rect>
                                    <rect x="14" y="3" width="7" height="7"></rect>
                                    <rect x="14" y="14" width="7" height="7"></rect>
                                    <rect x="3" y="14" width="7" height="7"></rect>
                                </svg>
                            </button>
                            <button class="view-btn" onclick="toggleView('list')" title="List View">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="8" y1="6" x2="21" y2="6"></line>
                                    <line x1="8" y1="12" x2="21" y2="12"></line>
                                    <line x1="8" y1="18" x2="21" y2="18"></line>
                                    <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                    <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                    <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                </svg>
                            </button>
                            <button class="view-btn" onclick="toggleView('table')" title="Table View">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Tax Clearance Records Content -->
                    <div id="tax-clearance-content">
                        <!-- Empty State -->
                        <div class="tax-empty-state" id="tax-empty-state">
                            <div class="empty-icon">📄</div>
                            <h3>No Tax Clearance Records</h3>
                            <p>Click "Add New Record" to add your first tax clearance certificate</p>
                        </div>

                        <!-- Records Display -->
                        <div class="tax-records-container" id="tax-records-container" style="display: none;">
                            <!-- Records will be loaded here -->
                        </div>

                        <!-- Pagination -->
                        <div class="tax-pagination" id="tax-pagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="records-count">0 Record(s)</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="pagination-btn" onclick="previousPage()" id="prev-btn" disabled>‹</button>
                                <span class="page-numbers" id="page-numbers">1</span>
                                <button class="pagination-btn" onclick="nextPage()" id="next-btn" disabled>›</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="equipment" class="content-section">
                    <h1>Equipment</h1>
                    <p>Equipment information will be displayed here.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Home button functionality
        function goHome() {
            if (confirm('Are you sure you want to go to the home page? Any unsaved changes will be lost.')) {
                window.location.href = '../index.php';
            }
        }

        // Logout confirmation
        function confirmLogout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = 'logout.php';
            }
        }

        // Show specific content section
        function showSection(sectionId) {
            // Hide all content sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            // Show the selected section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // Add active class to corresponding nav link
            const correspondingNavLink = document.querySelector(`[onclick="showSection('${sectionId}')"]`);
            if (correspondingNavLink) {
                correspondingNavLink.classList.add('active');
            }
        }

        // Document management functions
        function viewDocument(documentType) {
            // Open document in new tab
            window.open(`view_document.php?type=${documentType}`, '_blank');
        }

        // Show message function
        function showMessage(message, type) {
            // Create message element
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            // Set background color based on type
            if (type === 'success') {
                messageDiv.style.backgroundColor = '#28a745';
            } else if (type === 'error') {
                messageDiv.style.backgroundColor = '#dc3545';
            } else {
                messageDiv.style.backgroundColor = '#007bff';
            }

            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            // Remove after 3 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        function deleteDocument(documentType) {
            // Confirm deletion
            if (confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
                // Find the delete button
                const deleteBtn = event.target.closest('.btn-delete');
                if (!deleteBtn) {
                    console.error('Delete button not found');
                    return;
                }

                const originalText = deleteBtn.innerHTML;

                // Show loading state
                deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
                deleteBtn.disabled = true;

                // Delete document
                fetch('delete_document.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        document_type: documentType
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        showMessage(data.message, 'success');

                        // Update document status (this will remove the delete button)
                        updateDocumentStatusAfterDelete(documentType);

                        // Update counters
                        updateDocumentCounts();

                        // Note: Don't restore button here as it gets removed by updateDocumentStatusAfterDelete
                    } else {
                        // Show error message
                        showMessage(data.message, 'error');

                        // Restore button
                        deleteBtn.innerHTML = originalText;
                        deleteBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    showMessage('An error occurred while deleting the document', 'error');

                    // Restore button if it still exists
                    if (deleteBtn && deleteBtn.parentNode) {
                        deleteBtn.innerHTML = originalText;
                        deleteBtn.disabled = false;
                    }
                });
            }
        }

        function uploadDocument(documentType) {
            const modal = document.getElementById('uploadModal');
            const modalTitle = document.getElementById('modalTitle');
            const documentTypeInput = document.getElementById('documentType');

            // Set modal title based on document type
            const documentTitles = {
                'memart': 'Upload CTC of Memorandum & Articles of Association',
                'cac_status': 'Upload CAC Company Status Report',
                'tax_clearance': 'Upload Tax Clearance Certificate',
                'utility_bill': 'Upload Current Utility Bill',
                'incorporation_cert': 'Upload CAC Certificate of Incorporation'
            };

            modalTitle.textContent = documentTitles[documentType] || 'Upload Document';
            documentTypeInput.value = documentType;
            modal.style.display = 'block';
        }

        function closeUploadModal() {
            const modal = document.getElementById('uploadModal');
            modal.style.display = 'none';

            // Reset form
            document.getElementById('documentUploadForm').reset();
        }

        // Contact modal functions
        function openContactModal() {
            document.getElementById('contactModal').style.display = 'block';
        }

        function closeContactModal() {
            document.getElementById('contactModal').style.display = 'none';
            document.getElementById('contactForm').reset();
        }

        // Handle contact form submission
        document.addEventListener('DOMContentLoaded', function() {
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;

                    // Show loading state
                    submitBtn.textContent = 'Sending...';
                    submitBtn.disabled = true;

                    // Send message to admin
                    fetch('send_contact_message.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showMessage('Message sent successfully! We will get back to you soon.', 'success');
                            closeContactModal();
                        } else {
                            showMessage('Failed to send message: ' + data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Contact error:', error);
                        showMessage('An error occurred while sending your message', 'error');
                    })
                    .finally(() => {
                        // Restore button
                        submitBtn.textContent = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }
        });

        function updateDocumentStatusAfterDelete(documentType) {
            // Find the document card by looking for buttons with the document type
            const documentCards = document.querySelectorAll('.document-card');
            let targetCard = null;

            documentCards.forEach(card => {
                const uploadBtn = card.querySelector('.btn-upload');
                if (uploadBtn && uploadBtn.getAttribute('onclick').includes(documentType)) {
                    targetCard = card;
                }
            });

            if (targetCard) {
                const statusBadge = targetCard.querySelector('.status-badge');
                const dateSpan = targetCard.querySelector('.document-date');
                const uploadBtn = targetCard.querySelector('.btn-upload');
                const deleteBtn = targetCard.querySelector('.btn-delete');

                // Update status badge to pending
                statusBadge.className = 'status-badge status-pending';
                statusBadge.innerHTML = '<i class="fas fa-clock"></i> Pending Upload';

                // Update date text
                if (dateSpan) {
                    dateSpan.textContent = 'Required for compliance';
                }

                // Change upload button back to "Upload"
                if (uploadBtn) {
                    uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload';
                }

                // Remove delete button
                if (deleteBtn) {
                    deleteBtn.remove();
                }

                // Add animation
                targetCard.style.transform = 'scale(0.98)';
                targetCard.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                setTimeout(() => {
                    targetCard.style.transform = 'scale(1)';
                }, 300);
            }
        }

        function updateDocumentStatus(documentType, status, uploadDate) {
            // Find the document card by looking for buttons with the document type
            const documentCards = document.querySelectorAll('.document-card');
            let targetCard = null;

            documentCards.forEach(card => {
                const uploadBtn = card.querySelector('.btn-upload');
                if (uploadBtn && uploadBtn.getAttribute('onclick').includes(documentType)) {
                    targetCard = card;
                }
            });

            if (targetCard) {
                const statusBadge = targetCard.querySelector('.status-badge');
                const dateSpan = targetCard.querySelector('.document-date');
                const viewBtn = targetCard.querySelector('.btn-view');
                const downloadBtn = targetCard.querySelector('.btn-download');
                const uploadBtn = targetCard.querySelector('.btn-upload');

                if (status === 'uploaded') {
                    // Add animation to the card
                    targetCard.style.transform = 'scale(1.02)';
                    targetCard.style.boxShadow = '0 8px 25px rgba(40, 167, 69, 0.3)';

                    // Update status badge with animation
                    statusBadge.className = 'status-badge status-uploaded';
                    statusBadge.innerHTML = '<i class="fas fa-check-circle"></i> Uploaded';
                    statusBadge.style.transform = 'scale(1.1)';

                    // Update date
                    if (dateSpan) {
                        dateSpan.textContent = 'Last updated: ' + uploadDate;
                    }

                    // Enable view and download buttons with animation
                    if (viewBtn) {
                        viewBtn.disabled = false;
                        viewBtn.style.opacity = '1';
                        viewBtn.style.transform = 'scale(1.05)';
                        setTimeout(() => viewBtn.style.transform = 'scale(1)', 300);
                    }
                    if (downloadBtn) {
                        downloadBtn.disabled = false;
                        downloadBtn.style.opacity = '1';
                        downloadBtn.style.transform = 'scale(1.05)';
                        setTimeout(() => downloadBtn.style.transform = 'scale(1)', 300);
                    }

                    // Change upload button to "Replace"
                    if (uploadBtn) {
                        uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Replace';
                    }

                    // Add delete button if it doesn't exist
                    const actionsDiv = targetCard.querySelector('.document-actions');
                    let deleteBtn = targetCard.querySelector('.btn-delete');
                    if (!deleteBtn && actionsDiv) {
                        deleteBtn = document.createElement('button');
                        deleteBtn.className = 'btn-action btn-delete';
                        deleteBtn.setAttribute('onclick', `deleteDocument('${documentType}')`);
                        deleteBtn.innerHTML = '<i class="fas fa-trash"></i> Delete';
                        actionsDiv.appendChild(deleteBtn);
                    }

                    // Reset card animation after delay
                    setTimeout(() => {
                        targetCard.style.transform = 'scale(1)';
                        targetCard.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                        statusBadge.style.transform = 'scale(1)';
                    }, 500);
                }
            }
        }

        // Handle form submission
        document.addEventListener('DOMContentLoaded', function() {
            const uploadForm = document.getElementById('documentUploadForm');
            if (uploadForm) {
                uploadForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const documentType = formData.get('document_type');
                    const fileInput = this.querySelector('input[type="file"]');
                    const submitBtn = this.querySelector('button[type="submit"]');

                    // Debug logging
                    console.log('Form submission debug:');
                    console.log('Document type:', documentType);
                    console.log('File input:', fileInput);
                    console.log('Selected file:', fileInput ? fileInput.files[0] : 'No file input found');
                    console.log('FormData entries:');
                    for (let pair of formData.entries()) {
                        console.log(pair[0] + ': ', pair[1]);
                    }

                    // Validate file selection
                    if (!fileInput.files[0]) {
                        alert('Please select a file to upload');
                        return;
                    }

                    // Show loading state
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

                    // Upload the file
                    fetch('upload_simple.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccessMessage('Document uploaded successfully!');
                            closeUploadModal();
                            // Update document status in the UI
                            updateDocumentStatus(documentType, 'uploaded', data.upload_date);
                            updateDocumentCounts();
                        } else {
                            alert('Upload failed: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Upload error:', error);
                        alert('Upload failed: ' + error.message);
                    })
                    .finally(() => {
                        // Reset button state
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = 'Upload Document';
                    });
                });
            }
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('uploadModal');
            if (event.target === modal) {
                closeUploadModal();
            }
        });

        // Logo upload functionality
        function triggerLogoUpload() {
            document.getElementById('logoInput').click();
        }

        function handleLogoUpload(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];

                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPEG, PNG, or GIF)');
                    return;
                }

                // Validate file size (max 5MB)
                const maxSize = 5 * 1024 * 1024; // 5MB in bytes
                if (file.size > maxSize) {
                    alert('File size must be less than 5MB');
                    return;
                }

                // Show loading state
                const changeBtn = document.querySelector('.change-btn');
                const originalText = changeBtn.textContent;
                changeBtn.textContent = 'Uploading...';
                changeBtn.disabled = true;

                // Create form data
                const formData = new FormData();
                formData.append('logo', file);

                // Upload the file
                fetch('upload_logo.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the logo display
                        const logoContainer = document.querySelector('.logo-container');
                        const logoPlaceholder = logoContainer.querySelector('.logo-placeholder');
                        const existingLogo = logoContainer.querySelector('.company-logo');

                        if (existingLogo) {
                            // Update existing logo
                            existingLogo.src = data.logo_path + '?t=' + new Date().getTime(); // Add timestamp to prevent caching
                        } else if (logoPlaceholder) {
                            // Replace placeholder with actual logo
                            logoPlaceholder.outerHTML = `<img src="${data.logo_path}?t=${new Date().getTime()}" alt="Company Logo" class="company-logo">`;
                        }

                        // Show success message
                        showSuccessMessage('Logo uploaded successfully!');
                    } else {
                        alert('Error uploading logo: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while uploading the logo. Please try again.');
                })
                .finally(() => {
                    // Reset button state
                    changeBtn.textContent = originalText;
                    changeBtn.disabled = false;
                    // Clear the input
                    input.value = '';
                });
            }
        }

        // Success message function
        function showSuccessMessage(message) {
            // Create success popup
            const popup = document.createElement('div');
            popup.className = 'success-popup';
            popup.innerHTML = `
                <div class="success-content">
                    <i class="fas fa-check-circle"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(popup);

            // Show popup
            setTimeout(() => popup.classList.add('show'), 100);

            // Hide popup after 3 seconds
            setTimeout(() => {
                popup.classList.remove('show');
                setTimeout(() => document.body.removeChild(popup), 300);
            }, 3000);
        }

        // Edit functionality
        function toggleEdit(section) {
            const displayDiv = document.getElementById(section + '-display');
            const editForm = document.getElementById(section + '-edit-form');
            const editBtn = document.getElementById(section + '-edit-btn');

            if (editForm.style.display === 'none') {
                // Show edit form
                displayDiv.style.display = 'none';
                editForm.style.display = 'block';
                editBtn.innerHTML = '<i class="fas fa-times"></i>';
                editBtn.title = 'Cancel Edit';
            } else {
                // Hide edit form
                displayDiv.style.display = 'block';
                editForm.style.display = 'none';
                editBtn.innerHTML = '<i class="fas fa-edit"></i>';
                editBtn.title = 'Edit';
            }
        }

        function cancelEdit(section) {
            const displayDiv = document.getElementById(section + '-display');
            const editForm = document.getElementById(section + '-edit-form');
            const editBtn = document.getElementById(section + '-edit-btn');

            // Hide edit form
            displayDiv.style.display = 'block';
            editForm.style.display = 'none';
            editBtn.innerHTML = '<i class="fas fa-edit"></i>';
            editBtn.title = 'Edit';

            // Reset form to original values
            editForm.reset();
        }

        // Save profile data
        function saveProfileData(section, formData) {
            console.log('Saving profile data:', section, formData);

            return fetch('update_profile.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    section: section,
                    data: formData
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    // Update display values
                    updateDisplayValues(section, formData);
                    // Hide edit form
                    cancelEdit(section);
                    // Show success message
                    showSuccessMessage(data.message || 'Profile updated successfully!');
                } else {
                    throw new Error(data.message || 'Failed to update profile');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating profile: ' + error.message);
            });
        }

        function updateDisplayValues(section, data) {
            // Map field names to display element IDs
            const fieldMappings = {
                'address': {
                    'address': 'street',
                    'city': 'city',
                    'state': 'state',
                    'postal_code': 'postal'
                },
                'contact': {
                    'email': 'email',
                    'alternative_email': 'alt-email',
                    'phone': 'phone',
                    'website': 'website'
                },
                'ceo': {
                    'ceo_name': 'name',
                    'ceo_email': 'email',
                    'ceo_phone': 'phone'
                }
            };

            const mapping = fieldMappings[section];
            if (mapping) {
                Object.keys(data).forEach(key => {
                    const displayKey = mapping[key];
                    if (displayKey) {
                        const displayElement = document.getElementById(section + '-' + displayKey + '-display');
                        if (displayElement) {
                            displayElement.textContent = data[key] || 'Not Available';
                        }
                    }
                });
            }
        }

        // Update document counts
        function updateDocumentCounts() {
            // Only count official documents, not supporting documents
            const officialDocumentsSection = document.getElementById('official-documents');
            const documentCards = officialDocumentsSection.querySelectorAll('.document-card');
            let uploadedCount = 0;
            let pendingCount = 0;
            let totalCount = documentCards.length;

            documentCards.forEach(card => {
                const statusBadge = card.querySelector('.status-badge');
                if (statusBadge) {
                    if (statusBadge.classList.contains('status-uploaded')) {
                        uploadedCount++;
                    } else if (statusBadge.classList.contains('status-pending')) {
                        pendingCount++;
                    }
                }
            });

            // Update summary counts with animation
            const uploadedCountEl = document.getElementById('uploaded-count');
            const pendingCountEl = document.getElementById('pending-count');
            const totalCountEl = document.getElementById('total-count');

            if (uploadedCountEl) {
                uploadedCountEl.style.transform = 'scale(1.2)';
                uploadedCountEl.textContent = uploadedCount;
                setTimeout(() => uploadedCountEl.style.transform = 'scale(1)', 300);
            }
            if (pendingCountEl) {
                pendingCountEl.style.transform = 'scale(1.2)';
                pendingCountEl.textContent = pendingCount;
                setTimeout(() => pendingCountEl.style.transform = 'scale(1)', 300);
            }
            if (totalCountEl) {
                totalCountEl.textContent = totalCount;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Show the default section (official-documents)
            showSection('official-documents');

            // Update document counts
            updateDocumentCounts();

            // Add form submission handlers
            const addressForm = document.getElementById('address-edit-form');
            const contactForm = document.getElementById('contact-edit-form');
            const ceoForm = document.getElementById('ceo-edit-form');

            if (addressForm) {
                addressForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const formData = new FormData(this);
                    const data = Object.fromEntries(formData.entries());

                    const submitBtn = this.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.classList.add('loading');
                    submitBtn.textContent = 'Saving...';

                    saveProfileData('address', data).finally(() => {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('loading');
                        submitBtn.textContent = 'Save Changes';
                    });
                });
            }

            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const formData = new FormData(this);
                    const data = Object.fromEntries(formData.entries());

                    const submitBtn = this.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.classList.add('loading');
                    submitBtn.textContent = 'Saving...';

                    saveProfileData('contact', data).finally(() => {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('loading');
                        submitBtn.textContent = 'Save Changes';
                    });
                });
            }

            if (ceoForm) {
                ceoForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const formData = new FormData(this);
                    const data = Object.fromEntries(formData.entries());

                    const submitBtn = this.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.classList.add('loading');
                    submitBtn.textContent = 'Saving...';

                    saveProfileData('ceo', data).finally(() => {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('loading');
                        submitBtn.textContent = 'Save Changes';
                    });
                });
            }
        });

        // Supporting Documents Functions
        function uploadSupportingDocument(documentType) {
            document.getElementById('supportingDocumentType').value = documentType;
            document.getElementById('supportingModalTitle').textContent = 'Upload ' + documentType.replace('_', ' ').toUpperCase();
            document.getElementById('supportingUploadModal').style.display = 'block';
        }

        function closeSupportingUploadModal() {
            document.getElementById('supportingUploadModal').style.display = 'none';
            document.getElementById('supportingDocumentUploadForm').reset();
        }

        function viewSupportingDocument(documentType) {
            window.open(`view_document.php?type=${documentType}`, '_blank');
        }

        function downloadSupportingDocument(documentType) {
            window.open(`download_document.php?type=${documentType}`, '_blank');
        }

        function deleteSupportingDocument(documentType) {
            if (confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
                const deleteBtn = event.target.closest('.btn-delete');
                if (!deleteBtn) {
                    console.error('Delete button not found');
                    return;
                }

                const originalText = deleteBtn.innerHTML;
                deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
                deleteBtn.disabled = true;

                fetch('delete_document.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        document_type: documentType
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        // Reload the page to update the document status
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showMessage(data.message, 'error');
                        deleteBtn.innerHTML = originalText;
                        deleteBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    showMessage('An error occurred while deleting the document', 'error');
                    if (deleteBtn && deleteBtn.parentNode) {
                        deleteBtn.innerHTML = originalText;
                        deleteBtn.disabled = false;
                    }
                });
            }
        }

        // Supporting document upload form handler
        document.getElementById('supportingDocumentUploadForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

            fetch('upload_document.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message || 'Document uploaded successfully!', 'success');
                    closeSupportingUploadModal();
                    // Reload the page to update the document status
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showMessage(data.message || 'Upload failed', 'error');
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                showMessage('An error occurred while uploading the document', 'error');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });



        // Shareholders Functions
        let currentShareholdersTab = 'verified';

        function switchShareholdersTab(tabElement, tabType) {
            // Remove active class from all tabs
            document.querySelectorAll('#shareholders .tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Add active class to clicked tab
            tabElement.classList.add('active');

            currentShareholdersTab = tabType;
            loadShareholders(tabType);
        }

        function loadShareholders(status = 'verified') {
            fetch(`api/shareholders.php?status=${status}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading shareholders:', data.error);
                        return;
                    }

                    updateShareholdersDisplay(data.shareholders, status);
                    updateShareholdersCounts(data.counts);
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function updateShareholdersDisplay(shareholders, status) {
            const emptyState = document.getElementById('shareholders-empty-state');
            const dataTable = document.getElementById('shareholders-data-table');
            const tableBody = document.getElementById('shareholders-table-body');

            if (shareholders.length === 0) {
                emptyState.style.display = 'block';
                dataTable.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                dataTable.style.display = 'block';

                tableBody.innerHTML = '';
                shareholders.forEach(shareholder => {
                    const row = createShareholderRow(shareholder);
                    tableBody.appendChild(row);
                });
            }
        }

        function createShareholderRow(shareholder) {
            const row = document.createElement('tr');
            const appointmentDate = new Date(shareholder.appointment_date).toLocaleDateString();
            const statusClass = `status-${shareholder.status}`;

            row.innerHTML = `
                <td>${shareholder.name}</td>
                <td>${shareholder.number_of_shares.toLocaleString()}</td>
                <td>${shareholder.share_percentage}%</td>
                <td>${appointmentDate}</td>
                <td><span class="status-badge ${statusClass}">${shareholder.status.charAt(0).toUpperCase() + shareholder.status.slice(1)}</span></td>
                <td>
                    <button class="action-btn edit" onclick="editShareholder(${shareholder.id})">Edit</button>
                    <button class="action-btn delete" onclick="deleteShareholder(${shareholder.id})">Delete</button>
                </td>
            `;
            return row;
        }

        function updateShareholdersCounts(counts) {
            document.getElementById('shareholders-verified-count').textContent = counts.verified || 0;
            document.getElementById('shareholders-pending-count').textContent = counts.pending || 0;
            document.getElementById('shareholders-history-count').textContent = (counts.verified + counts.pending + counts.rejected) || 0;
        }

        function openShareholdersModal() {
            document.getElementById('shareholdersModal').style.display = 'block';
            // Set default date to today
            document.getElementById('shareholder-date').value = new Date().toISOString().split('T')[0];
        }

        function closeShareholdersModal() {
            document.getElementById('shareholdersModal').style.display = 'none';
            document.getElementById('shareholdersForm').reset();
        }

        function submitShareholder(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // Set status to pending (admin will verify)
            data.status = 'pending';

            // Convert numeric fields to proper types
            data.number_of_shares = parseInt(data.number_of_shares);
            data.share_percentage = parseFloat(data.share_percentage);

            console.log('Submitting shareholder data:', data);

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Adding...';
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;

            fetch('api/shareholders.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(result => {
                console.log('Response data:', result);
                if (result.success) {
                    showSuccessPopup('Shareholder added successfully! Pending admin verification.');
                    closeShareholdersModal();
                    loadShareholders(currentShareholdersTab);
                } else {
                    alert('Error: ' + result.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the shareholder: ' + error.message);
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
            });
        }

        function editShareholder(shareholderId) {
            alert('Edit shareholder: ' + shareholderId);
        }

        function deleteShareholder(shareholderId) {
            if (confirm('Are you sure you want to delete this shareholder?')) {
                fetch('api/shareholders.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: shareholderId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Shareholder deleted successfully');
                        loadShareholders(currentShareholdersTab);
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the shareholder');
                });
            }
        }

        // Directors Functions
        let currentDirectorsTab = 'verified';

        function switchDirectorsTab(tabElement, tabType) {
            // Remove active class from all tabs
            document.querySelectorAll('#directors .tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Add active class to clicked tab
            tabElement.classList.add('active');

            currentDirectorsTab = tabType;
            loadDirectors(tabType);
        }

        function loadDirectors(status = 'verified') {
            fetch(`api/directors.php?status=${status}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading directors:', data.error);
                        return;
                    }

                    updateDirectorsDisplay(data.directors, status);
                    updateDirectorsCounts(data.counts);
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function updateDirectorsDisplay(directors, status) {
            const emptyState = document.getElementById('directors-empty-state');
            const dataTable = document.getElementById('directors-data-table');
            const tableBody = document.getElementById('directors-table-body');

            if (directors.length === 0) {
                emptyState.style.display = 'block';
                dataTable.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                dataTable.style.display = 'block';

                tableBody.innerHTML = '';
                directors.forEach(director => {
                    const row = createDirectorRow(director);
                    tableBody.appendChild(row);
                });
            }
        }

        function createDirectorRow(director) {
            const row = document.createElement('tr');
            const appointmentDate = new Date(director.appointment_date).toLocaleDateString();
            const statusClass = `status-${director.status}`;

            row.innerHTML = `
                <td>${director.name}</td>
                <td>${director.position}</td>
                <td>${appointmentDate}</td>
                <td><span class="status-badge ${statusClass}">${director.status.charAt(0).toUpperCase() + director.status.slice(1)}</span></td>
                <td>
                    <button class="action-btn edit" onclick="editDirector(${director.id})">Edit</button>
                    <button class="action-btn delete" onclick="deleteDirector(${director.id})">Delete</button>
                </td>
            `;
            return row;
        }

        function updateDirectorsCounts(counts) {
            document.getElementById('directors-verified-count').textContent = counts.verified || 0;
            document.getElementById('directors-pending-count').textContent = counts.pending || 0;
            document.getElementById('directors-history-count').textContent = (counts.verified + counts.pending + counts.rejected) || 0;
        }

        function openDirectorsModal() {
            document.getElementById('directorsModal').style.display = 'block';
            // Set default date to today
            document.getElementById('director-date').value = new Date().toISOString().split('T')[0];
        }

        function closeDirectorsModal() {
            document.getElementById('directorsModal').style.display = 'none';
            document.getElementById('directorsForm').reset();
        }

        function submitDirector(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // Set status to pending (admin will verify)
            data.status = 'pending';

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Adding...';
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;

            fetch('api/directors.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showSuccessPopup('Director added successfully! Pending admin verification.');
                    closeDirectorsModal();
                    loadDirectors(currentDirectorsTab);
                } else {
                    alert('Error: ' + result.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the director');
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
            });
        }

        function editDirector(directorId) {
            alert('Edit director: ' + directorId);
        }

        function deleteDirector(directorId) {
            if (confirm('Are you sure you want to delete this director?')) {
                fetch('api/directors.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: directorId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Director deleted successfully');
                        loadDirectors(currentDirectorsTab);
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the director');
                });
            }
        }

        // Company Secretaries Functions
        let currentSecretariesTab = 'verified';

        function switchSecretariesTab(tabElement, tabType) {
            // Remove active class from all tabs
            document.querySelectorAll('#company-secretaries .tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Add active class to clicked tab
            tabElement.classList.add('active');

            currentSecretariesTab = tabType;
            loadSecretaries(tabType);
        }

        function loadSecretaries(status = 'verified') {
            fetch(`api/secretaries.php?status=${status}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading secretaries:', data.error);
                        return;
                    }

                    updateSecretariesDisplay(data.secretaries, status);
                    updateSecretariesCounts(data.counts);
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function updateSecretariesDisplay(secretaries, status) {
            const emptyState = document.getElementById('secretaries-empty-state');
            const dataTable = document.getElementById('secretaries-data-table');
            const tableBody = document.getElementById('secretaries-table-body');

            if (secretaries.length === 0) {
                emptyState.style.display = 'block';
                dataTable.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                dataTable.style.display = 'block';

                tableBody.innerHTML = '';
                secretaries.forEach(secretary => {
                    const row = createSecretaryRow(secretary);
                    tableBody.appendChild(row);
                });
            }
        }

        function createSecretaryRow(secretary) {
            const row = document.createElement('tr');
            const appointmentDate = new Date(secretary.appointment_date).toLocaleDateString();
            const statusClass = `status-${secretary.status}`;

            row.innerHTML = `
                <td>${secretary.name}</td>
                <td>${secretary.qualification}</td>
                <td>${appointmentDate}</td>
                <td><span class="status-badge ${statusClass}">${secretary.status.charAt(0).toUpperCase() + secretary.status.slice(1)}</span></td>
                <td>
                    <button class="action-btn edit" onclick="editSecretary(${secretary.id})">Edit</button>
                    <button class="action-btn delete" onclick="deleteSecretary(${secretary.id})">Delete</button>
                </td>
            `;
            return row;
        }

        function updateSecretariesCounts(counts) {
            document.getElementById('secretaries-verified-count').textContent = counts.verified || 0;
            document.getElementById('secretaries-pending-count').textContent = counts.pending || 0;
            document.getElementById('secretaries-history-count').textContent = (counts.verified + counts.pending + counts.rejected) || 0;
        }

        function openSecretariesModal() {
            document.getElementById('secretariesModal').style.display = 'block';
            // Set default date to today
            document.getElementById('secretary-date').value = new Date().toISOString().split('T')[0];
        }

        function closeSecretariesModal() {
            document.getElementById('secretariesModal').style.display = 'none';
            document.getElementById('secretariesForm').reset();
        }

        function submitSecretary(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // Set status to pending (admin will verify)
            data.status = 'pending';

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Adding...';
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;

            fetch('api/secretaries.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showSuccessPopup('Company Secretary added successfully! Pending admin verification.');
                    closeSecretariesModal();
                    loadSecretaries(currentSecretariesTab);
                } else {
                    alert('Error: ' + result.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the company secretary');
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
            });
        }

        function editSecretary(secretaryId) {
            alert('Edit secretary: ' + secretaryId);
        }

        function deleteSecretary(secretaryId) {
            if (confirm('Are you sure you want to delete this secretary?')) {
                fetch('api/secretaries.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: secretaryId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Company Secretary deleted successfully');
                        loadSecretaries(currentSecretariesTab);
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the secretary');
                });
            }
        }

        // Success popup function
        function showSuccessPopup(message) {
            // Create popup element
            const popup = document.createElement('div');
            popup.className = 'success-popup';
            popup.innerHTML = `
                <div class="success-popup-content">
                    <div class="success-icon">✓</div>
                    <div class="success-message">${message}</div>
                </div>
            `;

            // Add popup styles if not already added
            if (!document.getElementById('success-popup-styles')) {
                const style = document.createElement('style');
                style.id = 'success-popup-styles';
                style.textContent = `
                    .success-popup {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        background: #10b981;
                        color: white;
                        padding: 1rem 1.5rem;
                        border-radius: 0.5rem;
                        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                        animation: slideInRight 0.3s ease-out;
                        max-width: 400px;
                    }

                    .success-popup-content {
                        display: flex;
                        align-items: center;
                        gap: 0.75rem;
                    }

                    .success-icon {
                        width: 24px;
                        height: 24px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: bold;
                        flex-shrink: 0;
                    }

                    .success-message {
                        font-size: 0.875rem;
                        font-weight: 500;
                    }

                    @keyframes slideInRight {
                        from {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                        to {
                            transform: translateX(0);
                            opacity: 1;
                        }
                    }

                    @keyframes slideOutRight {
                        from {
                            transform: translateX(0);
                            opacity: 1;
                        }
                        to {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(popup);

            // Auto remove after 4 seconds
            setTimeout(() => {
                popup.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (popup.parentNode) {
                        popup.parentNode.removeChild(popup);
                    }
                }, 300);
            }, 4000);
        }

        // Modal click outside to close
        window.onclick = function(event) {
            const shareholdersModal = document.getElementById('shareholdersModal');
            const directorsModal = document.getElementById('directorsModal');
            const secretariesModal = document.getElementById('secretariesModal');
            const taxClearanceModal = document.getElementById('taxClearanceModal');

            if (event.target == shareholdersModal) {
                closeShareholdersModal();
            }
            if (event.target == directorsModal) {
                closeDirectorsModal();
            }
            if (event.target == secretariesModal) {
                closeSecretariesModal();
            }
            if (event.target == taxClearanceModal) {
                closeTaxClearanceModal();
            }

            // Keep existing modal close functionality
            if (event.target == uploadModal) {
                closeUploadModal();
            }
            if (event.target == supportingUploadModal) {
                closeSupportingUploadModal();
            }
            if (event.target == contactModal) {
                closeContactModal();
            }
        }

        // Tax Clearance Functions
        let currentTaxPage = 1;
        let currentTaxLimit = 10;
        let currentTaxSearch = '';

        function openTaxClearanceModal() {
            document.getElementById('taxClearanceModal').style.display = 'block';
            // Set default dates
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('tax-issue-date').value = today;

            // Set expiry date to end of current year
            const endOfYear = new Date(new Date().getFullYear(), 11, 31).toISOString().split('T')[0];
            document.getElementById('tax-expiry-date').value = endOfYear;

            // Set current year
            document.getElementById('tax-year').value = new Date().getFullYear();
        }

        function closeTaxClearanceModal() {
            document.getElementById('taxClearanceModal').style.display = 'none';
            document.getElementById('taxClearanceForm').reset();
        }

        function submitTaxClearance(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Adding...';
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;

            fetch('api/tax_clearance.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showSuccessPopup('Tax clearance certificate added successfully!');
                    closeTaxClearanceModal();
                    loadTaxClearanceRecords();
                } else {
                    alert('Error: ' + result.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the tax clearance record');
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
            });
        }

        function loadTaxClearanceRecords() {
            const offset = (currentTaxPage - 1) * currentTaxLimit;
            const url = `api/tax_clearance.php?limit=${currentTaxLimit}&offset=${offset}&search=${encodeURIComponent(currentTaxSearch)}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading tax clearance records:', data.error);
                        return;
                    }

                    displayTaxClearanceRecords(data.records);
                    updateTaxPagination(data.total, data.limit, data.offset);
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function displayTaxClearanceRecords(records) {
            const emptyState = document.getElementById('tax-empty-state');
            const container = document.getElementById('tax-records-container');
            const pagination = document.getElementById('tax-pagination');

            if (records.length === 0) {
                emptyState.style.display = 'block';
                container.style.display = 'none';
                pagination.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                container.style.display = 'block';
                pagination.style.display = 'flex';

                container.innerHTML = '';
                records.forEach(record => {
                    const recordCard = createTaxRecordCard(record);
                    container.appendChild(recordCard);
                });
            }
        }

        function createTaxRecordCard(record) {
            const card = document.createElement('div');
            card.className = 'tax-record-card';

            const issueDate = new Date(record.issue_date).toLocaleDateString('en-US', {
                year: 'numeric', month: 'short', day: 'numeric'
            });
            const expiryDate = new Date(record.expiry_date).toLocaleDateString('en-US', {
                year: 'numeric', month: 'short', day: 'numeric'
            });
            const createdDate = new Date(record.created_at).toLocaleDateString('en-US', {
                year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'
            });

            const statusClass = record.status.toLowerCase();
            const statusColor = {
                'issued': '#10b981',
                'expired': '#f59e0b',
                'revoked': '#ef4444'
            }[statusClass] || '#6b7280';

            card.innerHTML = `
                <div class="record-number">${record.certificate_number}</div>
                <div class="record-company">${record.company_name}</div>
                <div class="record-details">
                    <div class="record-field">
                        <div class="record-field-label">Issue Date</div>
                        <div class="record-field-value">${issueDate}</div>
                    </div>
                    <div class="record-field">
                        <div class="record-field-label">Expiry Date</div>
                        <div class="record-field-value">${expiryDate}</div>
                    </div>
                    <div class="record-field">
                        <div class="record-field-label">Tax Office</div>
                        <div class="record-field-value">${record.tax_office}</div>
                    </div>
                    <div class="record-field">
                        <div class="record-field-label">TCC Status</div>
                        <div class="record-field-value" style="color: ${statusColor}; font-weight: 600;">${record.status}</div>
                    </div>
                    <div class="record-field">
                        <div class="record-field-label">Date Created</div>
                        <div class="record-field-value">${createdDate}</div>
                    </div>
                    <div class="record-field">
                        <div class="record-field-label">Created By</div>
                        <div class="record-field-value">${record.created_by}</div>
                    </div>
                </div>
                <div class="record-actions">
                    <button class="record-action-btn" onclick="viewTaxRecord(${record.id})" title="View Details">👁</button>
                    <button class="record-action-btn" onclick="editTaxRecord(${record.id})" title="Edit">✏️</button>
                    <button class="record-action-btn" onclick="deleteTaxRecord(${record.id})" title="Delete">🗑️</button>
                </div>
            `;

            return card;
        }

        function updateTaxPagination(total, limit, offset) {
            const recordsCount = document.getElementById('records-count');
            const pageNumbers = document.getElementById('page-numbers');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');

            recordsCount.textContent = `${total} Record(s)`;

            const totalPages = Math.ceil(total / limit);
            const currentPage = Math.floor(offset / limit) + 1;

            pageNumbers.textContent = totalPages > 0 ? `${currentPage}` : '1';

            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }

        function searchTaxRecords() {
            currentTaxSearch = document.getElementById('tax-search').value;
            currentTaxPage = 1;
            loadTaxClearanceRecords();
        }

        function updateDisplayCount() {
            currentTaxLimit = parseInt(document.getElementById('display-select').value);
            currentTaxPage = 1;
            loadTaxClearanceRecords();
        }

        function previousPage() {
            if (currentTaxPage > 1) {
                currentTaxPage--;
                loadTaxClearanceRecords();
            }
        }

        function nextPage() {
            currentTaxPage++;
            loadTaxClearanceRecords();
        }

        function toggleView(viewType) {
            // Remove active class from all view buttons
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            event.target.closest('.view-btn').classList.add('active');

            // For now, we'll just show the current view
            // You can implement different view layouts here
            console.log('Switched to view:', viewType);
        }

        function viewTaxRecord(recordId) {
            alert('View tax record: ' + recordId);
        }

        function editTaxRecord(recordId) {
            alert('Edit tax record: ' + recordId);
        }

        function deleteTaxRecord(recordId) {
            if (confirm('Are you sure you want to delete this tax clearance record?')) {
                fetch('api/tax_clearance.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: recordId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccessPopup('Tax clearance record deleted successfully');
                        loadTaxClearanceRecords();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the record');
                });
            }
        }

        // Initialize data on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Load initial data for all sections
            loadShareholders('verified');
            loadDirectors('verified');
            loadSecretaries('verified');
            loadTaxClearanceRecords();

            // Set default active view button
            document.querySelector('.view-btn').classList.add('active');
        });
    </script>
</body>
</html>
