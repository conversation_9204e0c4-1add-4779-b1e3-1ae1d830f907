<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['error' => 'Not logged in']);
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>🔬 Minimal Persistence Test</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Step 1: Insert a test record directly
echo "<h3>Step 1: Insert Test Record</h3>";

$test_type = 'test_doc_' . time();
$test_filename = 'test_file.txt';
$test_filepath = '../uploads/documents/firm_' . $firm_id . '/test_file.txt';
$test_size = 100;

// Create directory if needed
$upload_dir = dirname($test_filepath);
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// Create test file
file_put_contents($test_filepath, 'Test content for persistence check');

// Insert into database
$insert_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status, upload_date) 
               VALUES (?, ?, ?, ?, ?, 'uploaded', NOW())";

$insert_stmt = $conn->prepare($insert_sql);
$insert_stmt->bind_param("isssi", $firm_id, $test_type, $test_filename, $test_filepath, $test_size);

if ($insert_stmt->execute()) {
    $insert_id = $conn->insert_id;
    echo "<p style='color: green;'>✅ Test record inserted with ID: $insert_id</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to insert: " . $insert_stmt->error . "</p>";
    exit();
}

// Step 2: Immediately retrieve the record
echo "<h3>Step 2: Immediate Retrieval</h3>";

$select_sql = "SELECT * FROM firm_documents WHERE id = ?";
$select_stmt = $conn->prepare($select_sql);
$select_stmt->bind_param("i", $insert_id);
$select_stmt->execute();
$result = $select_stmt->get_result();

if ($result->num_rows > 0) {
    $doc = $result->fetch_assoc();
    echo "<p style='color: green;'>✅ Record found immediately after insert</p>";
    echo "<ul>";
    echo "<li>ID: " . $doc['id'] . "</li>";
    echo "<li>Type: " . $doc['document_type'] . "</li>";
    echo "<li>Status: " . $doc['status'] . "</li>";
    echo "<li>Date: " . $doc['upload_date'] . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Record not found immediately after insert</p>";
    exit();
}

// Step 3: Test the dashboard query
echo "<h3>Step 3: Dashboard Query Test</h3>";

$dashboard_sql = "SELECT document_type, file_name, file_path, status, upload_date FROM firm_documents WHERE firm_id = ?";
$dashboard_stmt = $conn->prepare($dashboard_sql);
$dashboard_stmt->bind_param("i", $firm_id);
$dashboard_stmt->execute();
$dashboard_result = $dashboard_stmt->get_result();

$found_test_doc = false;
$total_docs = 0;

while ($doc = $dashboard_result->fetch_assoc()) {
    $total_docs++;
    if ($doc['document_type'] === $test_type) {
        $found_test_doc = true;
        echo "<p style='color: green;'>✅ Test document found in dashboard query</p>";
    }
}

echo "<p>Total documents found by dashboard query: $total_docs</p>";

if (!$found_test_doc) {
    echo "<p style='color: red;'>❌ Test document NOT found in dashboard query</p>";
}

// Step 4: Test document status function
echo "<h3>Step 4: Document Status Function Test</h3>";

// Simulate the getDocumentStatus function
$document_statuses = [];
$dashboard_stmt->execute();
$dashboard_result = $dashboard_stmt->get_result();

while ($doc = $dashboard_result->fetch_assoc()) {
    $document_statuses[$doc['document_type']] = $doc;
}

if (isset($document_statuses[$test_type])) {
    $doc = $document_statuses[$test_type];
    $has_file = ($doc['status'] === 'uploaded');
    
    echo "<p style='color: green;'>✅ Test document found in status array</p>";
    echo "<ul>";
    echo "<li>Status: " . $doc['status'] . "</li>";
    echo "<li>Has File: " . ($has_file ? 'Yes' : 'No') . "</li>";
    echo "<li>Upload Date: " . $doc['upload_date'] . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Test document NOT found in status array</p>";
}

// Step 5: Simulate page refresh by reconnecting
echo "<h3>Step 5: Simulate Page Refresh</h3>";

// Close and reconnect to database
$conn->close();
require_once '../includes/config.php';

// Query again after "refresh"
$refresh_sql = "SELECT * FROM firm_documents WHERE id = ?";
$refresh_stmt = $conn->prepare($refresh_sql);
$refresh_stmt->bind_param("i", $insert_id);
$refresh_stmt->execute();
$refresh_result = $refresh_stmt->get_result();

if ($refresh_result->num_rows > 0) {
    $doc = $refresh_result->fetch_assoc();
    echo "<p style='color: green;'>✅ Record still exists after simulated refresh</p>";
    echo "<ul>";
    echo "<li>ID: " . $doc['id'] . "</li>";
    echo "<li>Type: " . $doc['document_type'] . "</li>";
    echo "<li>Status: " . $doc['status'] . "</li>";
    echo "<li>Date: " . $doc['upload_date'] . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Record disappeared after simulated refresh</p>";
}

// Step 6: Check if it's a real document type issue
echo "<h3>Step 6: Test with Real Document Type</h3>";

$real_type = 'memart';
$real_filename = 'memart_test.txt';
$real_filepath = '../uploads/documents/firm_' . $firm_id . '/memart_test.txt';

// Create test file
file_put_contents($real_filepath, 'Test MEMART document for persistence check');

// Check if real document type already exists
$check_real_sql = "SELECT id FROM firm_documents WHERE firm_id = ? AND document_type = ?";
$check_real_stmt = $conn->prepare($check_real_sql);
$check_real_stmt->bind_param("is", $firm_id, $real_type);
$check_real_stmt->execute();
$existing_real = $check_real_stmt->get_result()->fetch_assoc();

if ($existing_real) {
    // Update existing
    $update_real_sql = "UPDATE firm_documents SET 
                        file_name = ?, 
                        file_path = ?, 
                        file_size = ?, 
                        upload_date = NOW(),
                        status = 'uploaded'
                        WHERE firm_id = ? AND document_type = ?";
    
    $update_real_stmt = $conn->prepare($update_real_sql);
    $update_real_stmt->bind_param("sssis", $real_filename, $real_filepath, $test_size, $firm_id, $real_type);
    
    if ($update_real_stmt->execute()) {
        echo "<p style='color: green;'>✅ Real document type updated successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to update real document: " . $update_real_stmt->error . "</p>";
    }
} else {
    // Insert new
    $insert_real_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status, upload_date) 
                        VALUES (?, ?, ?, ?, ?, 'uploaded', NOW())";
    
    $insert_real_stmt = $conn->prepare($insert_real_sql);
    $insert_real_stmt->bind_param("isssi", $firm_id, $real_type, $real_filename, $real_filepath, $test_size);
    
    if ($insert_real_stmt->execute()) {
        echo "<p style='color: green;'>✅ Real document type inserted successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to insert real document: " . $insert_real_stmt->error . "</p>";
    }
}

// Clean up test document
$cleanup_sql = "DELETE FROM firm_documents WHERE id = ?";
$cleanup_stmt = $conn->prepare($cleanup_sql);
$cleanup_stmt->bind_param("i", $insert_id);
$cleanup_stmt->execute();
unlink($test_filepath);

echo "<p style='color: blue;'>🧹 Test document cleaned up</p>";

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
    <h3 style="color: #155724;">✅ Persistence Test Complete</h3>
    <p style="color: #155724;">The test above shows if documents can be inserted and retrieved properly.</p>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Check Dashboard</a>
        <a href="diagnose_upload_issue.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Full Diagnostic</a>
    </p>
</div>
