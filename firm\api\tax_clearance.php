<?php
session_start();
require_once '../../includes/config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

$firm_id = $_SESSION['firm_id'];
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getTaxClearanceRecords($conn, $firm_id);
        break;
    case 'POST':
        addTaxClearanceRecord($conn, $firm_id);
        break;
    case 'PUT':
        updateTaxClearanceRecord($conn, $firm_id);
        break;
    case 'DELETE':
        deleteTaxClearanceRecord($conn, $firm_id);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

function getTaxClearanceRecords($conn, $firm_id) {
    $search = $_GET['search'] ?? '';
    $limit = intval($_GET['limit'] ?? 10);
    $offset = intval($_GET['offset'] ?? 0);
    
    $sql = "SELECT * FROM firm_tax_clearance WHERE firm_id = ?";
    $params = [$firm_id];
    $types = "i";
    
    if (!empty($search)) {
        $sql .= " AND (certificate_number LIKE ? OR company_name LIKE ? OR tax_office LIKE ?)";
        $searchParam = "%$search%";
        $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
        $types .= "sss";
    }
    
    // Get total count
    $countSql = str_replace("SELECT *", "SELECT COUNT(*) as total", $sql);
    $countStmt = $conn->prepare($countSql);
    $countStmt->bind_param($types, ...$params);
    $countStmt->execute();
    $countResult = $countStmt->get_result();
    $totalRecords = $countResult->fetch_assoc()['total'];
    
    // Get paginated results
    $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $params = array_merge($params, [$limit, $offset]);
    $types .= "ii";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $records = [];
    while ($row = $result->fetch_assoc()) {
        // Check if certificate is expired
        if ($row['status'] === 'ISSUED' && strtotime($row['expiry_date']) < time()) {
            // Auto-update expired certificates
            $updateStmt = $conn->prepare("UPDATE firm_tax_clearance SET status = 'EXPIRED' WHERE id = ?");
            $updateStmt->bind_param("i", $row['id']);
            $updateStmt->execute();
            $row['status'] = 'EXPIRED';
        }
        $records[] = $row;
    }
    
    echo json_encode([
        'records' => $records,
        'total' => $totalRecords,
        'limit' => $limit,
        'offset' => $offset
    ]);
}

function addTaxClearanceRecord($conn, $firm_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $required_fields = ['certificate_number', 'company_name', 'issue_date', 'expiry_date', 'tax_office', 'tax_year'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    // Check for duplicate certificate number for this firm
    $checkStmt = $conn->prepare("SELECT id FROM firm_tax_clearance WHERE firm_id = ? AND certificate_number = ?");
    $checkStmt->bind_param("is", $firm_id, $input['certificate_number']);
    $checkStmt->execute();
    if ($checkStmt->get_result()->num_rows > 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Certificate number already exists for this firm']);
        return;
    }
    
    $sql = "INSERT INTO firm_tax_clearance (firm_id, certificate_number, company_name, issue_date, expiry_date, tax_office, tax_amount, tax_year, status, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isssssdisss", 
        $firm_id,
        $input['certificate_number'],
        $input['company_name'],
        $input['issue_date'],
        $input['expiry_date'],
        $input['tax_office'],
        $input['tax_amount'] ?? 0.00,
        $input['tax_year'],
        $input['status'] ?? 'ISSUED',
        $input['notes'] ?? null,
        $_SESSION['firm_name'] ?? 'System'
    );
    
    if ($stmt->execute()) {
        $new_id = $conn->insert_id;
        echo json_encode(['success' => true, 'id' => $new_id, 'message' => 'Tax clearance record added successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to add tax clearance record: ' . $stmt->error]);
    }
}

function updateTaxClearanceRecord($conn, $firm_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Record ID is required']);
        return;
    }
    
    $sql = "UPDATE firm_tax_clearance SET certificate_number = ?, company_name = ?, issue_date = ?, expiry_date = ?, tax_office = ?, tax_amount = ?, tax_year = ?, status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND firm_id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssdissii",
        $input['certificate_number'],
        $input['company_name'],
        $input['issue_date'],
        $input['expiry_date'],
        $input['tax_office'],
        $input['tax_amount'],
        $input['tax_year'],
        $input['status'],
        $input['notes'],
        $input['id'],
        $firm_id
    );
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Tax clearance record updated successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Record not found']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update tax clearance record']);
    }
}

function deleteTaxClearanceRecord($conn, $firm_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Record ID is required']);
        return;
    }
    
    $sql = "DELETE FROM firm_tax_clearance WHERE id = ? AND firm_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $input['id'], $firm_id);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Tax clearance record deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Record not found']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete tax clearance record']);
    }
}

$conn->close();
?>
