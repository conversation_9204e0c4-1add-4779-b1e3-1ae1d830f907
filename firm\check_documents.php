<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Document Status Check</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Check if firm_documents table exists
$table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");
if (!$table_check || $table_check->num_rows === 0) {
    echo "<p style='color: red;'>❌ firm_documents table does not exist</p>";
    echo "<p><a href='fix_documents_table.php'>Fix Table Structure</a></p>";
    exit();
}

echo "<p style='color: green;'>✅ firm_documents table exists</p>";

// Get all documents for this firm
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY document_type";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

echo "<h3>Documents in Database:</h3>";

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Document Type</th><th>File Name</th><th>File Path</th><th>Status</th><th>Size</th><th>Upload Date</th><th>File Exists</th><th>Actions</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']) ? '✅ Yes' : '❌ No';
        $file_exists_style = file_exists($doc['file_path']) ? 'color: green;' : 'color: red;';
        
        echo "<tr>";
        echo "<td>" . $doc['id'] . "</td>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_path']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . number_format($doc['file_size']) . " bytes</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td style='$file_exists_style'>" . $file_exists . "</td>";
        echo "<td>";
        
        if (file_exists($doc['file_path'])) {
            echo "<a href='view_document.php?type=" . $doc['document_type'] . "' target='_blank' style='margin-right: 5px;'>View</a>";
            echo "<a href='download_document.php?type=" . $doc['document_type'] . "'>Download</a>";
        } else {
            echo "<span style='color: red;'>File Missing</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ No documents found for this firm</p>";
}

// Check upload directory
echo "<h3>Upload Directory Check:</h3>";
$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';

if (file_exists($upload_dir)) {
    echo "<p style='color: green;'>✅ Upload directory exists: $upload_dir</p>";
    
    $files = scandir($upload_dir);
    $files = array_diff($files, array('.', '..'));
    
    if (count($files) > 0) {
        echo "<h4>Files in directory:</h4>";
        echo "<ul>";
        foreach ($files as $file) {
            $file_path = $upload_dir . $file;
            $file_size = filesize($file_path);
            echo "<li>$file (" . number_format($file_size) . " bytes)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Directory is empty</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Upload directory does not exist: $upload_dir</p>";
}

// Test each document type
echo "<h3>Document Type Test:</h3>";
$document_types = ['memart', 'cac_status', 'tax_clearance', 'utility_bill', 'incorporation_cert'];

foreach ($document_types as $type) {
    $test_sql = "SELECT * FROM firm_documents WHERE firm_id = ? AND document_type = ? AND status = 'uploaded'";
    $test_stmt = $conn->prepare($test_sql);
    $test_stmt->bind_param("is", $firm_id, $type);
    $test_stmt->execute();
    $test_result = $test_stmt->get_result();
    
    if ($test_result->num_rows > 0) {
        $doc = $test_result->fetch_assoc();
        $file_exists = file_exists($doc['file_path']);
        $status = $file_exists ? '✅ Ready' : '❌ File Missing';
        $color = $file_exists ? 'green' : 'red';
        
        echo "<p style='color: $color;'>$type: $status</p>";
    } else {
        echo "<p style='color: orange;'>$type: ⚠️ Not uploaded</p>";
    }
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
    <h3>Quick Actions:</h3>
    <p>
        <a href="add_sample_documents.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Add Sample Documents</a>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="test_upload.html" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Test Upload</a>
    </p>
</div>
