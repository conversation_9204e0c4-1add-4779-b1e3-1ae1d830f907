<?php
// Start session and show detailed debug info
session_start();

// Function to show session status
function showSessionStatus() {
    echo "<h3>Session Status:</h3>";
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    echo "<p><strong>Session Status:</strong> " . (session_status() == PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";
    echo "<p><strong>Session Save Path:</strong> " . session_save_path() . "</p>";
    echo "<p><strong>Session Name:</strong> " . session_name() . "</p>";
    
    echo "<h4>Session Data:</h4>";
    if (empty($_SESSION)) {
        echo "<p style='color: red;'>No session data found</p>";
    } else {
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        print_r($_SESSION);
        echo "</pre>";
    }
}

// Handle actions
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'login':
            $_SESSION['firm_id'] = 1;
            $_SESSION['firm_name'] = "Test Firm";
            $_SESSION['firm_email'] = "<EMAIL>";
            echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0;'>";
            echo "✅ Test login created!";
            echo "</div>";
            break;
            
        case 'logout':
            echo "<div style='color: blue; padding: 10px; background: #d1ecf1; border-radius: 5px; margin: 10px 0;'>";
            echo "🔄 Performing logout...";
            echo "</div>";
            
            // Show what we're destroying
            echo "<h4>Before Logout:</h4>";
            showSessionStatus();
            
            // Perform logout
            $_SESSION = array();
            
            // Delete session cookie
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }
            
            // Destroy session
            session_destroy();
            
            // Start new session
            session_start();
            $_SESSION['logout_message'] = "Logout successful!";
            
            echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0;'>";
            echo "✅ Logout completed!";
            echo "</div>";
            break;
            
        case 'clear':
            session_destroy();
            session_start();
            echo "<div style='color: orange; padding: 10px; background: #fff3cd; border-radius: 5px; margin: 10px 0;'>";
            echo "🧹 Session cleared!";
            echo "</div>";
            break;
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Session Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .btn { padding: 10px 15px; margin: 5px; text-decoration: none; border-radius: 4px; display: inline-block; color: white; }
        .btn-primary { background-color: #007bff; }
        .btn-danger { background-color: #dc3545; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-success { background-color: #28a745; }
        .section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Session Debug Tool</h1>
    
    <div class="section">
        <?php showSessionStatus(); ?>
    </div>
    
    <div class="section">
        <h3>Actions:</h3>
        <a href="?action=login" class="btn btn-success">Create Test Login</a>
        <a href="?action=logout" class="btn btn-danger">Test Logout Process</a>
        <a href="?action=clear" class="btn btn-warning">Clear Session</a>
        <a href="?" class="btn btn-primary">Refresh</a>
    </div>
    
    <div class="section">
        <h3>Test Links:</h3>
        <a href="dashboard.php" class="btn btn-primary">Go to Dashboard</a>
        <a href="login.php" class="btn btn-primary">Go to Login Page</a>
        <a href="logout.php" class="btn btn-danger">Direct Logout</a>
    </div>
    
    <div class="section">
        <h3>Cookie Information:</h3>
        <p><strong>All Cookies:</strong></p>
        <pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'><?php print_r($_COOKIE); ?></pre>
    </div>
</body>
</html>
