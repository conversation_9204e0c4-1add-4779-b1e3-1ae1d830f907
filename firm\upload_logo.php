<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in as a firm
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Include database connection
require_once '../config/db_connect.php';

try {
    // Check if file was uploaded
    if (!isset($_FILES['logo']) || $_FILES['logo']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No file uploaded or upload error occurred');
    }

    $file = $_FILES['logo'];
    $firm_id = $_SESSION['firm_id'];

    // Validate file type
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    $file_type = mime_content_type($file['tmp_name']);
    
    if (!in_array($file_type, $allowed_types)) {
        throw new Exception('Invalid file type. Only JPEG, PNG, and GIF images are allowed');
    }

    // Validate file size (max 5MB)
    $max_size = 5 * 1024 * 1024; // 5MB in bytes
    if ($file['size'] > $max_size) {
        throw new Exception('File size exceeds 5MB limit');
    }

    // Create upload directory if it doesn't exist
    $upload_dir = '../uploads/logos/';
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            throw new Exception('Failed to create upload directory');
        }
    }

    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'logo_' . $firm_id . '_' . time() . '.' . $file_extension;
    $upload_path = $upload_dir . $filename;
    $relative_path = 'uploads/logos/' . $filename;

    // Remove old logo if exists
    $stmt = $conn->prepare("SELECT logo_path FROM tax_firms WHERE id = ?");
    $stmt->bind_param("i", $firm_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        if (!empty($row['logo_path']) && file_exists('../' . $row['logo_path'])) {
            unlink('../' . $row['logo_path']);
        }
    }
    $stmt->close();

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        throw new Exception('Failed to move uploaded file');
    }

    // Update database with new logo path
    $stmt = $conn->prepare("UPDATE tax_firms SET logo_path = ? WHERE id = ?");
    $stmt->bind_param("si", $relative_path, $firm_id);
    
    if (!$stmt->execute()) {
        // If database update fails, remove the uploaded file
        unlink($upload_path);
        throw new Exception('Failed to update database');
    }
    
    $stmt->close();

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Logo uploaded successfully',
        'logo_path' => $relative_path
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
