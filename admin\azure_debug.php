<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/azure_ad_integration.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    echo "Please log in as admin first";
    exit();
}

echo "<h2>Azure AD Debug Information</h2>";

// Test 1: Check if function exists
echo "<h3>1. Function Check</h3>";
if (function_exists('initialize_azure_ad')) {
    echo "✓ initialize_azure_ad function exists<br>";
} else {
    echo "✗ initialize_azure_ad function missing<br>";
}

if (function_exists('get_azure_auth_url')) {
    echo "✓ get_azure_auth_url function exists<br>";
} else {
    echo "✗ get_azure_auth_url function missing<br>";
}

// Test 2: Check configuration
echo "<h3>2. Configuration Check</h3>";
$config = initialize_azure_ad();
if ($config) {
    echo "✓ Configuration loaded successfully<br>";
    echo "<strong>Configuration keys found:</strong><br>";
    foreach ($config as $key => $value) {
        if ($key === 'client_secret') {
            echo "- $key: [HIDDEN]<br>";
        } else {
            echo "- $key: " . htmlspecialchars($value) . "<br>";
        }
    }
} else {
    echo "✗ Configuration failed to load<br>";
    
    // Check if system_config table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'system_config'");
    if ($table_check->num_rows == 0) {
        echo "<strong>Issue:</strong> system_config table doesn't exist<br>";
        echo "<strong>Solution:</strong> <a href='azure_config_setup.php'>Run Azure Config Setup</a><br>";
    } else {
        echo "system_config table exists<br>";
        
        // Check for Azure AD config
        $config_check = $conn->query("SELECT * FROM system_config WHERE config_group = 'azure_ad'");
        if ($config_check->num_rows == 0) {
            echo "<strong>Issue:</strong> No Azure AD configuration found in database<br>";
            echo "<strong>Solution:</strong> <a href='azure_config_setup.php'>Configure Azure AD Settings</a><br>";
        } else {
            echo "Azure AD configuration entries found: " . $config_check->num_rows . "<br>";
            while ($row = $config_check->fetch_assoc()) {
                if ($row['config_key'] === 'client_secret') {
                    echo "- " . $row['config_key'] . ": [HIDDEN]<br>";
                } else {
                    echo "- " . $row['config_key'] . ": " . htmlspecialchars($row['config_value']) . "<br>";
                }
            }
        }
    }
}

// Test 3: Try to generate auth URL
echo "<h3>3. Auth URL Generation Test</h3>";
$auth_url = get_azure_auth_url();
if ($auth_url) {
    echo "✓ Auth URL generated successfully<br>";
    echo "URL: " . htmlspecialchars(substr($auth_url, 0, 100)) . "...<br>";
} else {
    echo "✗ Failed to generate auth URL<br>";
    echo "This is why you're getting the error message.<br>";
}

// Test 4: Manual configuration test
echo "<h3>4. Manual Configuration Test</h3>";
echo "If the above failed, you can test with manual configuration:<br>";
echo "<form method='post'>";
echo "Tenant ID: <input type='text' name='tenant_id' placeholder='your-tenant-id'><br><br>";
echo "Client ID: <input type='text' name='client_id' placeholder='your-client-id'><br><br>";
echo "Client Secret: <input type='password' name='client_secret' placeholder='your-client-secret'><br><br>";
echo "<input type='submit' value='Test Manual Config'>";
echo "</form>";

if ($_POST) {
    echo "<h4>Manual Test Results:</h4>";
    if (!empty($_POST['tenant_id']) && !empty($_POST['client_id']) && !empty($_POST['client_secret'])) {
        // Temporarily store config for testing
        $_SESSION['temp_azure_config'] = [
            'tenant_id' => $_POST['tenant_id'],
            'client_id' => $_POST['client_id'],
            'client_secret' => $_POST['client_secret'],
            'redirect_uri' => 'http://localhost/tax_registration_system/admin/azure_login.php',
            'scopes' => 'openid profile email User.Read'
        ];
        
        $test_auth_url = "https://login.microsoftonline.com/{$_POST['tenant_id']}/oauth2/v2.0/authorize?" . 
                        http_build_query([
                            'client_id' => $_POST['client_id'],
                            'response_type' => 'code',
                            'redirect_uri' => 'http://localhost/tax_registration_system/admin/azure_login.php',
                            'response_mode' => 'query',
                            'scope' => 'openid profile email User.Read',
                            'state' => bin2hex(random_bytes(16))
                        ]);
        
        echo "✓ Manual auth URL generated:<br>";
        echo "<a href='" . htmlspecialchars($test_auth_url) . "' target='_blank'>Test Azure Login</a><br>";
        echo "<br><strong>If this works, save these settings using the <a href='azure_config_setup.php'>Azure Config Setup</a></strong><br>";
    } else {
        echo "✗ Please fill in all fields<br>";
    }
}

echo "<br><br>";
echo "<a href='azure_config_setup.php'>Configure Azure AD</a> | ";
echo "<a href='test_azure_integration.php'>Run Full Test</a> | ";
echo "<a href='dashboard.php'>Back to Dashboard</a>";
?>
