<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        
        .upload-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Final Persistence Test</h1>
        
        <?php
        session_start();
        require_once '../includes/config.php';

        if (!isset($_SESSION['firm_id'])) {
            echo "<div class='container error'><p>Please login first.</p></div>";
            exit();
        }

        $firm_id = $_SESSION['firm_id'];
        echo "<p><strong>Firm ID:</strong> $firm_id</p>";
        echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
        ?>
        
        <div class="info" style="padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li><strong>Check Current Documents</strong> - See what's in the database now</li>
                <li><strong>Upload a New Document</strong> - Use the form below</li>
                <li><strong>Refresh This Page</strong> - See if the document persists</li>
                <li><strong>Go to Dashboard</strong> - Check if it shows there</li>
                <li><strong>Refresh Dashboard</strong> - Verify it stays there</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <h2>📊 Current Documents in Database</h2>
        
        <?php
        $docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY upload_date DESC";
        $docs_stmt = $conn->prepare($docs_sql);
        $docs_stmt->bind_param("i", $firm_id);
        $docs_stmt->execute();
        $docs_result = $docs_stmt->get_result();

        if ($docs_result->num_rows > 0) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Type</th><th>File Name</th><th>Status</th><th>Upload Date</th><th>File Exists</th></tr>";
            
            while ($doc = $docs_result->fetch_assoc()) {
                $file_exists = file_exists($doc['file_path']);
                echo "<tr>";
                echo "<td>" . $doc['id'] . "</td>";
                echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
                echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
                echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
                echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
                echo "<td style='color: " . ($file_exists ? 'green' : 'red') . ";'>" . ($file_exists ? '✅ Yes' : '❌ No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<div class='success' style='padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>✅ Found " . $docs_result->num_rows . " documents in database</strong>";
            echo "</div>";
        } else {
            echo "<div class='warning' style='padding: 15px; border-radius: 5px;'>";
            echo "<p><strong>⚠️ No documents found in database</strong></p>";
            echo "<p>This could mean:</p>";
            echo "<ul>";
            echo "<li>No documents have been uploaded yet</li>";
            echo "<li>Documents were uploaded but not saved to database</li>";
            echo "<li>There's an issue with the database connection</li>";
            echo "</ul>";
            echo "</div>";
        }
        ?>
    </div>

    <div class="container">
        <h2>📤 Upload New Document</h2>
        
        <div class="upload-form">
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="document_type">Document Type:</label>
                    <select name="document_type" id="document_type" required>
                        <option value="">Select document type</option>
                        <option value="memart">CTC of Memorandum & Articles of Association</option>
                        <option value="cac_status">CAC Company Status Report</option>
                        <option value="tax_clearance">Current Tax Clearance Certificate</option>
                        <option value="utility_bill">Current Utility Bill</option>
                        <option value="incorporation_cert">CAC Certificate of Incorporation</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="document">Select File:</label>
                    <input type="file" name="document" id="document" required>
                    <small style="color: #666;">Any file type, max 10MB</small>
                </div>
                
                <button type="submit">Upload Document</button>
            </form>
        </div>
        
        <div id="uploadResult" style="margin-top: 20px;"></div>
    </div>

    <div class="container">
        <h2>🔗 Quick Actions</h2>
        <p>
            <a href="dashboard.php" class="btn btn-primary">Go to Dashboard</a>
            <a href="?refresh=1" class="btn btn-success">Refresh This Page</a>
            <a href="fix_persistence_final.php" class="btn btn-secondary">Run Final Fix</a>
            <a href="?clear=1" class="btn btn-danger" onclick="return confirm('Clear all documents?')">Clear All Documents</a>
        </p>
    </div>

    <?php
    // Handle clear all documents
    if (isset($_GET['clear'])) {
        $clear_sql = "DELETE FROM firm_documents WHERE firm_id = ?";
        $clear_stmt = $conn->prepare($clear_sql);
        $clear_stmt->bind_param("i", $firm_id);
        if ($clear_stmt->execute()) {
            echo "<script>alert('All documents cleared'); window.location.href = 'final_persistence_test.php';</script>";
        }
    }

    $conn->close();
    ?>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('uploadResult');
            
            // Show loading
            resultDiv.innerHTML = '<div class="info" style="padding: 15px; border-radius: 5px;">⏳ Uploading document...</div>';
            
            fetch('upload_simple.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success" style="padding: 15px; border-radius: 5px;">
                            <h4>✅ Upload Successful!</h4>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <p><strong>Document Type:</strong> ${data.document_type}</p>
                            <p><strong>File Name:</strong> ${data.file_name}</p>
                            <p><strong>Database ID:</strong> ${data.database_id}</p>
                            <p><strong>Upload Date:</strong> ${data.upload_date}</p>
                            <p style="margin-top: 15px;">
                                <strong>🔄 Now refresh this page to test persistence!</strong>
                            </p>
                            <p>
                                <a href="?refresh=1" class="btn btn-success">Refresh Page</a>
                                <a href="dashboard.php" class="btn btn-primary">Go to Dashboard</a>
                            </p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error" style="padding: 15px; border-radius: 5px;">
                            <h4>❌ Upload Failed</h4>
                            <p><strong>Error:</strong> ${data.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                resultDiv.innerHTML = `
                    <div class="error" style="padding: 15px; border-radius: 5px;">
                        <h4>❌ Upload Error</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Check the browser console for more details.</p>
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
