<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in as a firm
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Include database connection
require_once '../config/db_connect.php';

try {
    $firm_id = $_SESSION['firm_id'];
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'update_company_name':
            // Company name cannot be modified after registration
            echo json_encode([
                'success' => false,
                'message' => 'Company name cannot be modified. It is set during registration and is permanent.',
                'error_code' => 'COMPANY_NAME_READONLY'
            ]);
            break;

        case 'update_registration_details':
            $registration_number = trim($_POST['registration_number'] ?? '');
            $cac_number = trim($_POST['cac_number'] ?? '');
            $company_type = trim($_POST['company_type'] ?? '');
            $ownership_type = trim($_POST['ownership_type'] ?? '');
            $incorporation_date = trim($_POST['incorporation_date'] ?? '');
            $registration_date = trim($_POST['registration_date'] ?? '');
            $tin_number = trim($_POST['tin_number'] ?? '');

            // Debug: Log the received data
            error_log("Registration update - Firm ID: $firm_id, RC: $registration_number, CAC: $cac_number, Type: $company_type");

            // Validate required fields
            if (empty($registration_number)) {
                throw new Exception('Registration number is required');
            }

            // Validate date formats if provided
            if (!empty($incorporation_date) && !DateTime::createFromFormat('Y-m-d', $incorporation_date)) {
                throw new Exception('Invalid incorporation date format');
            }

            if (!empty($registration_date) && !DateTime::createFromFormat('Y-m-d', $registration_date)) {
                throw new Exception('Invalid registration date format');
            }

            // Check which fields exist in the database
            $fields_to_update = [];
            $values = [];
            $types = '';

            // Check if each field exists before adding to query
            $field_checks = [
                'registration_number' => $registration_number,
                'cac_number' => $cac_number,
                'company_type' => $company_type,
                'ownership_type' => $ownership_type,
                'incorporation_date' => $incorporation_date,
                'registration_date' => $registration_date,
                'tin_number' => $tin_number
            ];

            foreach ($field_checks as $field => $value) {
                // Check if field exists in table
                $check_field = $conn->query("SHOW COLUMNS FROM tax_firms LIKE '$field'");
                if ($check_field && $check_field->num_rows > 0) {
                    $fields_to_update[] = "$field = ?";
                    $values[] = $value;
                    $types .= 's';
                }
            }

            if (empty($fields_to_update)) {
                throw new Exception('No valid fields to update');
            }

            // Add firm_id to values and types
            $values[] = $firm_id;
            $types .= 'i';

            // Build and execute query
            $sql = "UPDATE tax_firms SET " . implode(', ', $fields_to_update) . " WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param($types, ...$values);

            if (!$stmt->execute()) {
                throw new Exception('Failed to update registration details in database: ' . $stmt->error);
            }

            $affected_rows = $stmt->affected_rows;
            error_log("Registration update - Affected rows: $affected_rows");

            $stmt->close();

            // Check if all required fields are now complete and auto-verify
            $verification_result = checkAndUpdateVerificationStatus($conn, $firm_id);
            error_log("Registration update - Verification result: " . json_encode($verification_result));

            echo json_encode([
                'success' => true,
                'message' => 'Registration details updated successfully',
                'verification_status' => $verification_result['status'],
                'verification_message' => $verification_result['message']
            ]);
            break;

        case 'update_address':
            $address = trim($_POST['address'] ?? '');
            $city = trim($_POST['city'] ?? '');
            $state = trim($_POST['state'] ?? '');
            $postal_code = trim($_POST['postal_code'] ?? '');

            // Validate required fields
            if (empty($address)) {
                throw new Exception('Address is required');
            }

            // Check which fields exist in the database
            $fields_to_update = [];
            $values = [];
            $types = '';

            $field_checks = [
                'address' => $address,
                'city' => $city,
                'state' => $state,
                'postal_code' => $postal_code
            ];

            foreach ($field_checks as $field => $value) {
                // Check if field exists in table
                $check_field = $conn->query("SHOW COLUMNS FROM tax_firms LIKE '$field'");
                if ($check_field && $check_field->num_rows > 0) {
                    $fields_to_update[] = "$field = ?";
                    $values[] = $value;
                    $types .= 's';
                }
            }

            if (empty($fields_to_update)) {
                throw new Exception('No valid address fields to update');
            }

            // Add firm_id to values and types
            $values[] = $firm_id;
            $types .= 'i';

            // Build and execute query
            $sql = "UPDATE tax_firms SET " . implode(', ', $fields_to_update) . " WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param($types, ...$values);

            if (!$stmt->execute()) {
                throw new Exception('Failed to update address in database');
            }

            $stmt->close();

            // Check if all required fields are now complete and auto-verify
            $verification_result = checkAndUpdateVerificationStatus($conn, $firm_id);

            echo json_encode([
                'success' => true,
                'message' => 'Address updated successfully',
                'verification_status' => $verification_result['status'],
                'verification_message' => $verification_result['message']
            ]);
            break;

        case 'update_contact_info':
            $email = trim($_POST['email'] ?? '');
            $alternative_email = trim($_POST['alternative_email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $alternative_phone = trim($_POST['alternative_phone'] ?? '');
            $website = trim($_POST['website'] ?? '');

            // Validate required fields
            if (empty($email)) {
                throw new Exception('Email is required');
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email format');
            }

            if (!empty($alternative_email) && !filter_var($alternative_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid alternative email format');
            }

            if (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
                throw new Exception('Invalid website URL format');
            }

            // Prepare fields to update
            $fields_to_update = [];
            $values = [];
            $types = '';

            $field_checks = [
                'email' => $email,
                'alternative_email' => $alternative_email,
                'phone' => $phone,
                'alternative_phone' => $alternative_phone,
                'website' => $website
            ];

            foreach ($field_checks as $field => $value) {
                $fields_to_update[] = "$field = ?";
                $values[] = $value;
                $types .= 's';
            }

            if (empty($fields_to_update)) {
                throw new Exception('No valid contact fields to update');
            }

            // Add firm_id to values and types
            $values[] = $firm_id;
            $types .= 'i';

            // Build and execute query
            $sql = "UPDATE tax_firms SET " . implode(', ', $fields_to_update) . " WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param($types, ...$values);

            if (!$stmt->execute()) {
                throw new Exception('Failed to update contact information in database');
            }

            // Update session email if changed
            if ($email !== $_SESSION['firm_email']) {
                $_SESSION['firm_email'] = $email;
            }

            $stmt->close();

            // Check if all required fields are now complete and auto-verify
            $verification_result = checkAndUpdateVerificationStatus($conn, $firm_id);

            echo json_encode([
                'success' => true,
                'message' => 'Contact information updated successfully',
                'verification_status' => $verification_result['status'],
                'verification_message' => $verification_result['message']
            ]);
            break;

        case 'update_address_info':
            $address = trim($_POST['address'] ?? '');
            $city = trim($_POST['city'] ?? '');
            $state = trim($_POST['state'] ?? '');
            $postal_code = trim($_POST['postal_code'] ?? '');
            $country = trim($_POST['country'] ?? 'Nigeria');

            // Validate required fields
            if (empty($address)) {
                throw new Exception('Street address is required');
            }

            if (empty($city)) {
                throw new Exception('City is required');
            }

            if (empty($state)) {
                throw new Exception('State is required');
            }

            // Prepare fields to update
            $fields_to_update = [];
            $values = [];
            $types = '';

            $field_checks = [
                'address' => $address,
                'city' => $city,
                'state' => $state,
                'postal_code' => $postal_code,
                'country' => $country
            ];

            foreach ($field_checks as $field => $value) {
                $fields_to_update[] = "$field = ?";
                $values[] = $value;
                $types .= 's';
            }

            if (empty($fields_to_update)) {
                throw new Exception('No valid address fields to update');
            }

            // Add firm_id to values and types
            $values[] = $firm_id;
            $types .= 'i';

            // Build and execute query
            $sql = "UPDATE tax_firms SET " . implode(', ', $fields_to_update) . " WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param($types, ...$values);

            if (!$stmt->execute()) {
                throw new Exception('Failed to update address information in database');
            }

            $stmt->close();

            // Check if all required fields are now complete and auto-verify
            $verification_result = checkAndUpdateVerificationStatus($conn, $firm_id);

            echo json_encode([
                'success' => true,
                'message' => 'Address information updated successfully',
                'verification_status' => $verification_result['status'],
                'verification_message' => $verification_result['message']
            ]);
            break;

        case 'update_tin_info':
            $tin_number = trim($_POST['tin_number'] ?? '');

            // Validate TIN number
            if (empty($tin_number)) {
                throw new Exception('TIN number is required');
            }

            // Update database
            $stmt = $conn->prepare("UPDATE tax_firms SET tin_number = ? WHERE id = ?");
            $stmt->bind_param("si", $tin_number, $firm_id);

            if (!$stmt->execute()) {
                throw new Exception('Failed to update TIN number in database');
            }

            $stmt->close();

            // Check if all required fields are now complete and auto-verify
            $verification_result = checkAndUpdateVerificationStatus($conn, $firm_id);

            echo json_encode([
                'success' => true,
                'message' => 'TIN information updated successfully',
                'verification_status' => $verification_result['status'],
                'verification_message' => $verification_result['message']
            ]);
            break;

        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Simple function to check if all required fields are filled and update status
function checkAndUpdateVerificationStatus($conn, $firm_id) {
    try {
        // Get current firm data
        $stmt = $conn->prepare("SELECT * FROM tax_firms WHERE id = ?");
        $stmt->bind_param("i", $firm_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $firm_data = $result->fetch_assoc();
        $stmt->close();

        if (!$firm_data) {
            return ['status' => 'error', 'message' => 'Firm not found'];
        }

        // Check all required fields
        $required_fields = [
            'name', 'email', 'phone', 'address', 'city', 'state',
            'registration_number', 'cac_number', 'tax_id', 'tin_number',
            'business_type', 'company_type', 'ownership_type', 'incorporation_date'
        ];

        $all_filled = true;
        foreach ($required_fields as $field) {
            $value = $firm_data[$field] ?? '';
            if (empty($value) || trim($value) === '' || $value === 'Not provided') {
                $all_filled = false;
                break;
            }
        }

        // Update registration status based on completion
        $new_status = $all_filled ? 'Active' : 'Pending';
        if ($firm_data['registration_status'] !== $new_status) {
            $update_status = $conn->prepare("UPDATE tax_firms SET registration_status = ? WHERE id = ?");
            $update_status->bind_param("si", $new_status, $firm_id);
            $update_status->execute();
            $update_status->close();
        }

        return [
            'status' => $all_filled ? 'verified' : 'pending',
            'message' => $all_filled ? 'All required information is complete. Status updated to VERIFIED!' : 'Please complete all required fields to get verified status.',
            'is_complete' => $all_filled
        ];

    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => 'Error checking verification status: ' . $e->getMessage()
        ];
    }
}

$conn->close();
?>
