<?php
session_start();
require_once '../includes/config.php';

// Quick login for testing
if (isset($_GET['test_login'])) {
    $_SESSION['firm_id'] = 1;
    $_SESSION['firm_name'] = "Test Tax Consulting Firm";
    $_SESSION['firm_email'] = "<EMAIL>";
    
    echo "<h2>Test Login Successful!</h2>";
    echo "<p>Session created with:</p>";
    echo "<ul>";
    echo "<li>Firm ID: " . $_SESSION['firm_id'] . "</li>";
    echo "<li>Firm Name: " . $_SESSION['firm_name'] . "</li>";
    echo "<li>Firm Email: " . $_SESSION['firm_email'] . "</li>";
    echo "</ul>";
    
    echo "<h3>Test Links:</h3>";
    echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>";
    echo "<p><a href='supporting_documents.php'>Go to Supporting Documents</a></p>";
    exit;
}

// Check current session
echo "<h2>Current Session Status</h2>";
if (isset($_SESSION['firm_id'])) {
    echo "<p style='color: green;'>✓ Logged in as Firm ID: " . $_SESSION['firm_id'] . "</p>";
    echo "<p>Firm Name: " . ($_SESSION['firm_name'] ?? 'Not set') . "</p>";
    echo "<p>Firm Email: " . ($_SESSION['firm_email'] ?? 'Not set') . "</p>";
    
    echo "<h3>Available Pages:</h3>";
    echo "<p><a href='dashboard.php'>Dashboard</a></p>";
    echo "<p><a href='supporting_documents.php'>Supporting Documents</a></p>";
    echo "<p><a href='logout.php'>Logout</a></p>";
} else {
    echo "<p style='color: red;'>✗ Not logged in</p>";
    echo "<p><a href='?test_login=1'>Click here for test login</a></p>";
    echo "<p><a href='login.php'>Go to regular login page</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Quick Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>Firm Login Test Page</h1>
    
    <?php if (!isset($_SESSION['firm_id'])): ?>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3>Test Login Credentials:</h3>
        <p><strong>Email:</strong> <EMAIL></p>
        <p><strong>Password:</strong> password123</p>
        <p><a href="login.php">Use Regular Login Form</a></p>
    </div>
    <?php endif; ?>
</body>
</html>
