<?php
session_start();
require_once '../includes/config.php';

// Set up a test session if not already logged in
if (!isset($_SESSION['firm_id'])) {
    // Get the first firm for testing
    $firm_result = $conn->query("SELECT * FROM tax_firms LIMIT 1");
    if ($firm_result && $firm_result->num_rows > 0) {
        $firm = $firm_result->fetch_assoc();
        $_SESSION['firm_id'] = $firm['id'];
        $_SESSION['firm_name'] = $firm['name'];
        $_SESSION['firm_email'] = $firm['email'];
    } else {
        die("No firms found in database. Please create a firm first.");
    }
}

echo "<h2>Testing Shareholders API</h2>";
echo "<p>Firm ID: " . $_SESSION['firm_id'] . "</p>";
echo "<p>Firm Name: " . $_SESSION['firm_name'] . "</p>";

// Test 1: Check if table exists
echo "<h3>1. Checking if firm_shareholders table exists</h3>";
$table_check = $conn->query("SHOW TABLES LIKE 'firm_shareholders'");
if ($table_check->num_rows > 0) {
    echo "<p style='color: green;'>✅ firm_shareholders table exists</p>";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE firm_shareholders");
    echo "<h4>Table Structure:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ firm_shareholders table does not exist</p>";
    echo "<p><a href='setup_company_structure_tables.php'>Create tables</a></p>";
}

// Test 2: Try to fetch existing shareholders
echo "<h3>2. Fetching existing shareholders</h3>";
$shareholders_query = "SELECT * FROM firm_shareholders WHERE firm_id = ?";
$stmt = $conn->prepare($shareholders_query);
$stmt->bind_param("i", $_SESSION['firm_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo "<p style='color: green;'>Found " . $result->num_rows . " shareholders:</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Shares</th><th>Percentage</th><th>Status</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['number_of_shares'] . "</td>";
        echo "<td>" . $row['share_percentage'] . "%</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>No shareholders found for this firm</p>";
}

// Test 3: Test API endpoint directly
echo "<h3>3. Testing API Endpoint</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_add'])) {
    // Simulate API call
    $test_data = [
        'name' => 'Test Shareholder',
        'email' => '<EMAIL>',
        'phone' => '+234-************',
        'address' => 'Test Address',
        'number_of_shares' => 1000,
        'share_percentage' => 10.5,
        'share_class' => 'Ordinary',
        'appointment_date' => date('Y-m-d'),
        'status' => 'pending'
    ];
    
    $sql = "INSERT INTO firm_shareholders (firm_id, name, email, phone, address, number_of_shares, share_percentage, share_class, appointment_date, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isssidsss", 
        $_SESSION['firm_id'],
        $test_data['name'],
        $test_data['email'],
        $test_data['phone'],
        $test_data['address'],
        $test_data['number_of_shares'],
        $test_data['share_percentage'],
        $test_data['share_class'],
        $test_data['appointment_date'],
        $test_data['status']
    );
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Test shareholder added successfully! ID: " . $conn->insert_id . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to add test shareholder: " . $stmt->error . "</p>";
    }
}

?>

<form method="POST">
    <button type="submit" name="test_add" style="padding: 10px 20px; background: #059669; color: white; border: none; border-radius: 5px; cursor: pointer;">
        Add Test Shareholder
    </button>
</form>

<h3>4. Test API via JavaScript</h3>
<button onclick="testAPICall()" style="padding: 10px 20px; background: #6366f1; color: white; border: none; border-radius: 5px; cursor: pointer;">
    Test API Call
</button>
<div id="api-result" style="margin-top: 10px; padding: 10px; background: #f3f4f6; border-radius: 5px;"></div>

<script>
function testAPICall() {
    const testData = {
        name: 'API Test Shareholder',
        email: '<EMAIL>',
        phone: '+234-************',
        address: 'API Test Address',
        number_of_shares: 500,
        share_percentage: 5.25,
        share_class: 'Ordinary',
        appointment_date: new Date().toISOString().split('T')[0],
        status: 'pending'
    };
    
    console.log('Sending data:', testData);
    
    fetch('api/shareholders.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        try {
            const data = JSON.parse(text);
            console.log('Parsed response:', data);
            document.getElementById('api-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (e) {
            console.error('JSON parse error:', e);
            document.getElementById('api-result').innerHTML = '<p style="color: red;">Error parsing response: ' + text + '</p>';
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        document.getElementById('api-result').innerHTML = '<p style="color: red;">Fetch error: ' + error.message + '</p>';
    });
}
</script>

<p style="margin-top: 20px;">
    <a href="dashboard.php">← Back to Dashboard</a>
</p>
