<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/auth_check.php';

// Check admin authentication
require_admin_auth();

// Check permissions
if (!has_permission('system_settings')) {
    $_SESSION['alert'] = [
        'type' => 'danger',
        'message' => 'You do not have permission to access system settings.'
    ];
    header('Location: dashboard.php');
    exit;
}

// Create system_settings table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS system_settings (
    id INT(11) NOT NULL AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type ENUM('text', 'number', 'boolean', 'email', 'url', 'textarea') DEFAULT 'text',
    category VARCHAR(50) NOT NULL,
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT(11) NULL,
    PRIMARY KEY (id),
    KEY idx_category (category),
    KEY idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

$conn->query($create_table_sql);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $success_count = 0;
    $error_count = 0;
    
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'setting_') === 0) {
            $setting_key = substr($key, 8); // Remove 'setting_' prefix
            
            // Update or insert setting
            $sql = "INSERT INTO system_settings (setting_key, setting_value, updated_by) 
                    VALUES (?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                    setting_value = VALUES(setting_value), 
                    updated_by = VALUES(updated_by),
                    updated_at = CURRENT_TIMESTAMP";
            
            $stmt = $conn->prepare($sql);
            if ($stmt && $stmt->bind_param("ssi", $setting_key, $value, $_SESSION['admin_id']) && $stmt->execute()) {
                $success_count++;
            } else {
                $error_count++;
            }
        }
    }
    
    // Log the action
    log_admin_action($_SESSION['admin_id'], 'update_system_settings', "Updated {$success_count} system settings");
    
    $message = "Settings updated successfully! ({$success_count} settings saved";
    if ($error_count > 0) {
        $message .= ", {$error_count} failed";
    }
    $message .= ")";
    
    $_SESSION['alert'] = [
        'type' => $error_count > 0 ? 'warning' : 'success',
        'message' => $message
    ];
    
    header('Location: system_settings.php');
    exit;
}

// Initialize default settings if they don't exist
$default_settings = [
    // General Settings
    'system_name' => ['value' => 'Tax Registration System', 'type' => 'text', 'category' => 'general', 'description' => 'Name of the system displayed in headers and emails'],
    'system_email' => ['value' => '<EMAIL>', 'type' => 'email', 'category' => 'general', 'description' => 'System email address for notifications'],
    'system_phone' => ['value' => '+234-XXX-XXX-XXXX', 'type' => 'text', 'category' => 'general', 'description' => 'System contact phone number'],
    'system_address' => ['value' => 'Tax Authority Headquarters, Abuja, Nigeria', 'type' => 'textarea', 'category' => 'general', 'description' => 'Official system address'],
    
    // Registration Settings
    'auto_approve_registrations' => ['value' => 'false', 'type' => 'boolean', 'category' => 'registration', 'description' => 'Automatically approve new registrations'],
    'require_nin_verification' => ['value' => 'true', 'type' => 'boolean', 'category' => 'registration', 'description' => 'Require NIN verification during registration'],
    'allow_duplicate_emails' => ['value' => 'false', 'type' => 'boolean', 'category' => 'registration', 'description' => 'Allow multiple registrations with same email'],
    'registration_fee' => ['value' => '0', 'type' => 'number', 'category' => 'registration', 'description' => 'Registration fee amount (in Naira)'],
    
    // CITN Settings
    'citn_verification_enabled' => ['value' => 'true', 'type' => 'boolean', 'category' => 'citn', 'description' => 'Enable CITN verification functionality'],
    'citn_api_timeout' => ['value' => '30', 'type' => 'number', 'category' => 'citn', 'description' => 'CITN API timeout in seconds'],
    'citn_mock_mode' => ['value' => 'true', 'type' => 'boolean', 'category' => 'citn', 'description' => 'Use mock responses for CITN verification (disable in production)'],
    'citn_auto_verify' => ['value' => 'true', 'type' => 'boolean', 'category' => 'citn', 'description' => 'Automatically verify CITN during registration'],
    
    // Email Settings
    'smtp_enabled' => ['value' => 'false', 'type' => 'boolean', 'category' => 'email', 'description' => 'Enable SMTP email sending'],
    'smtp_host' => ['value' => 'smtp.gmail.com', 'type' => 'text', 'category' => 'email', 'description' => 'SMTP server hostname'],
    'smtp_port' => ['value' => '587', 'type' => 'number', 'category' => 'email', 'description' => 'SMTP server port'],
    'smtp_username' => ['value' => '', 'type' => 'email', 'category' => 'email', 'description' => 'SMTP username/email'],
    
    // Security Settings
    'session_timeout' => ['value' => '3600', 'type' => 'number', 'category' => 'security', 'description' => 'Session timeout in seconds'],
    'max_login_attempts' => ['value' => '5', 'type' => 'number', 'category' => 'security', 'description' => 'Maximum login attempts before lockout'],
    'password_min_length' => ['value' => '8', 'type' => 'number', 'category' => 'security', 'description' => 'Minimum password length'],
    'enable_two_factor' => ['value' => 'false', 'type' => 'boolean', 'category' => 'security', 'description' => 'Enable two-factor authentication'],
    
    // Maintenance Settings
    'maintenance_mode' => ['value' => 'false', 'type' => 'boolean', 'category' => 'maintenance', 'description' => 'Enable maintenance mode'],
    'maintenance_message' => ['value' => 'System is under maintenance. Please try again later.', 'type' => 'textarea', 'category' => 'maintenance', 'description' => 'Message displayed during maintenance'],
    'backup_enabled' => ['value' => 'true', 'type' => 'boolean', 'category' => 'maintenance', 'description' => 'Enable automatic database backups'],
    'log_retention_days' => ['value' => '90', 'type' => 'number', 'category' => 'maintenance', 'description' => 'Number of days to retain log files']
];

// Insert default settings if they don't exist
foreach ($default_settings as $key => $setting) {
    $check_sql = "SELECT id FROM system_settings WHERE setting_key = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("s", $key);
    $check_stmt->execute();
    
    if ($check_stmt->get_result()->num_rows === 0) {
        $insert_sql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, category, description) VALUES (?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("sssss", $key, $setting['value'], $setting['type'], $setting['category'], $setting['description']);
        $insert_stmt->execute();
    }
}

// Get all settings grouped by category
$settings_sql = "SELECT * FROM system_settings ORDER BY category, setting_key";
$settings_result = $conn->query($settings_sql);

$settings_by_category = [];
while ($row = $settings_result->fetch_assoc()) {
    $settings_by_category[$row['category']][] = $row;
}

$page_title = "System Settings";
include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">System Settings</h1>
                    <p class="text-muted">Configure system-wide settings and preferences</p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (isset($_SESSION['alert'])): ?>
    <div class="alert alert-<?php echo $_SESSION['alert']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['alert']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['alert']); endif; ?>

    <!-- Settings Form -->
    <form method="POST" id="settingsForm">
        <div class="row">
            <div class="col-lg-9">
                <!-- Settings Categories -->
                <div class="accordion" id="settingsAccordion">
                    <?php 
                    $category_icons = [
                        'general' => 'fas fa-cog',
                        'registration' => 'fas fa-user-plus',
                        'citn' => 'fas fa-certificate',
                        'email' => 'fas fa-envelope',
                        'security' => 'fas fa-shield-alt',
                        'maintenance' => 'fas fa-tools'
                    ];
                    
                    $category_names = [
                        'general' => 'General Settings',
                        'registration' => 'Registration Settings',
                        'citn' => 'CITN Integration',
                        'email' => 'Email Configuration',
                        'security' => 'Security Settings',
                        'maintenance' => 'Maintenance & Backup'
                    ];
                    
                    $index = 0;
                    foreach ($settings_by_category as $category => $settings): 
                        $is_first = $index === 0;
                    ?>
                    <div class="card mb-3">
                        <div class="card-header" id="heading<?php echo $category; ?>">
                            <h2 class="mb-0">
                                <button class="btn btn-link btn-block text-left <?php echo !$is_first ? 'collapsed' : ''; ?>" 
                                        type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#collapse<?php echo $category; ?>" 
                                        aria-expanded="<?php echo $is_first ? 'true' : 'false'; ?>" 
                                        aria-controls="collapse<?php echo $category; ?>">
                                    <i class="<?php echo $category_icons[$category] ?? 'fas fa-cog'; ?> me-2"></i>
                                    <?php echo $category_names[$category] ?? ucfirst($category); ?>
                                </button>
                            </h2>
                        </div>

                        <div id="collapse<?php echo $category; ?>" 
                             class="collapse <?php echo $is_first ? 'show' : ''; ?>" 
                             aria-labelledby="heading<?php echo $category; ?>" 
                             data-bs-parent="#settingsAccordion">
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($settings as $setting): ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="setting_<?php echo $setting['setting_key']; ?>" class="form-label">
                                            <?php echo ucwords(str_replace('_', ' ', $setting['setting_key'])); ?>
                                            <?php if (!empty($setting['description'])): ?>
                                            <i class="fas fa-info-circle text-info ms-1" 
                                               data-bs-toggle="tooltip" 
                                               title="<?php echo htmlspecialchars($setting['description']); ?>"></i>
                                            <?php endif; ?>
                                        </label>
                                        
                                        <?php if ($setting['setting_type'] === 'boolean'): ?>
                                            <select class="form-select" id="setting_<?php echo $setting['setting_key']; ?>" 
                                                    name="setting_<?php echo $setting['setting_key']; ?>">
                                                <option value="true" <?php echo $setting['setting_value'] === 'true' ? 'selected' : ''; ?>>Enabled</option>
                                                <option value="false" <?php echo $setting['setting_value'] === 'false' ? 'selected' : ''; ?>>Disabled</option>
                                            </select>
                                        <?php elseif ($setting['setting_type'] === 'textarea'): ?>
                                            <textarea class="form-control" id="setting_<?php echo $setting['setting_key']; ?>" 
                                                      name="setting_<?php echo $setting['setting_key']; ?>" 
                                                      rows="3"><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>
                                        <?php else: ?>
                                            <input type="<?php echo $setting['setting_type']; ?>" 
                                                   class="form-control" 
                                                   id="setting_<?php echo $setting['setting_key']; ?>" 
                                                   name="setting_<?php echo $setting['setting_key']; ?>" 
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                   <?php echo $setting['setting_type'] === 'number' ? 'min="0"' : ''; ?>>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($setting['description'])): ?>
                                        <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php 
                    $index++;
                    endforeach; 
                    ?>
                </div>
            </div>

            <div class="col-lg-3">
                <!-- Save Button -->
                <div class="card mb-4 sticky-top" style="top: 20px;">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-save me-2"></i>Save Settings</h6>
                    </div>
                    <div class="card-body">
                        <button type="submit" class="btn btn-success w-100 mb-3">
                            <i class="fas fa-save me-2"></i>Save All Settings
                        </button>
                        
                        <button type="button" class="btn btn-outline-secondary w-100 mb-3" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>Reset Changes
                        </button>
                        
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Changes will take effect immediately after saving.
                            </small>
                        </div>
                    </div>
                </div>

                <!-- System Info -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info me-2"></i>System Information</h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
                            <strong>MySQL Version:</strong> <?php echo $conn->server_info; ?><br>
                            <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
                            <strong>Last Updated:</strong> 
                            <?php
                            $last_update_sql = "SELECT MAX(updated_at) as last_update FROM system_settings";
                            $last_update_result = $conn->query($last_update_sql);
                            if ($last_update_result && $row = $last_update_result->fetch_assoc()) {
                                echo $row['last_update'] ? date('M d, Y H:i', strtotime($row['last_update'])) : 'Never';
                            } else {
                                echo 'Never';
                            }
                            ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});

// Reset form function
function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will reload the page.')) {
        location.reload();
    }
}

// Form submission with loading state
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    submitBtn.disabled = true;
});

// Auto-hide alerts
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('show')) {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }
    });
}, 5000);

// Highlight unsaved changes
let originalFormData = new FormData(document.getElementById('settingsForm'));
document.getElementById('settingsForm').addEventListener('input', function() {
    const currentFormData = new FormData(this);
    let hasChanges = false;
    
    for (let [key, value] of currentFormData.entries()) {
        if (originalFormData.get(key) !== value) {
            hasChanges = true;
            break;
        }
    }
    
    const saveBtn = this.querySelector('button[type="submit"]');
    if (hasChanges) {
        saveBtn.classList.add('btn-warning');
        saveBtn.classList.remove('btn-success');
        saveBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Save Changes';
    } else {
        saveBtn.classList.add('btn-success');
        saveBtn.classList.remove('btn-warning');
        saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>Save All Settings';
    }
});
</script>

<?php include '../includes/admin_footer.php'; ?>
