<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: view_associations.php");
    exit();
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

$association_id = (int)$_GET['id'];
$page_title = "Association Details";

// Get association details
$sql = "SELECT pfa.*, 
               tp.id as practitioner_id, tp.full_name as practitioner_name, 
               tp.email as practitioner_email, tp.phone as practitioner_phone,
               tp.registration_number as practitioner_reg, tp.qualification, 
               tp.specialization, tp.registration_status as practitioner_status,
               tp.address as practitioner_address, tp.registration_date as practitioner_reg_date,
               tf.id as firm_id, tf.name as firm_name, tf.email as firm_email,
               tf.phone as firm_phone, tf.address as firm_address,
               tf.registration_number as firm_reg, tf.business_type, 
               tf.registration_status as firm_status, tf.registration_date as firm_reg_date
        FROM practitioner_firm_associations pfa
        JOIN tax_practitioners tp ON pfa.practitioner_id = tp.id
        JOIN tax_firms tf ON pfa.firm_id = tf.id
        WHERE pfa.id = ?";

$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $association_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: view_associations.php");
    exit();
}

$association = $result->fetch_assoc();

// Process status update if submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    $action = $_POST['action'];
    $reason = $_POST['reason'] ?? '';
    
    $new_status = '';
    $end_date = null;
    
    switch ($action) {
        case 'activate':
            $new_status = 'active';
            break;
        case 'deactivate':
            $new_status = 'inactive';
            break;
        case 'terminate':
            $new_status = 'inactive';
            $end_date = date('Y-m-d');
            break;
    }
    
    if (!empty($new_status)) {
        if ($end_date) {
            $update_sql = "UPDATE practitioner_firm_associations SET status = ?, end_date = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ssi", $new_status, $end_date, $association_id);
        } else {
            $update_sql = "UPDATE practitioner_firm_associations SET status = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("si", $new_status, $association_id);
        }
        
        if ($update_stmt->execute()) {
            // Log the action
            if (function_exists('log_admin_action')) {
                log_admin_action($_SESSION['admin_id'], $action . '_association', "Association ID: {$association_id}, Reason: {$reason}");
            }
            
            $_SESSION['alert'] = [
                'type' => 'success',
                'message' => "Association status updated successfully."
            ];
            
            header("Location: view_association_details.php?id=$association_id");
            exit();
        } else {
            $_SESSION['alert'] = [
                'type' => 'danger',
                'message' => 'Failed to update association status.'
            ];
        }
    }
}

// Calculate association duration
$start_date = new DateTime($association['start_date']);
$end_date = $association['end_date'] ? new DateTime($association['end_date']) : new DateTime();
$duration = $start_date->diff($end_date);

include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Association Details</h1>
                    <p class="text-muted">View and manage practitioner-firm association</p>
                </div>
                <div>
                    <a href="view_associations.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Associations
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (isset($_SESSION['alert'])): ?>
    <div class="alert alert-<?php echo $_SESSION['alert']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['alert']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['alert']); endif; ?>

    <div class="row">
        <!-- Association Overview -->
        <div class="col-lg-8">
            <!-- Association Status Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>Association Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Association ID</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info fs-6">#<?php echo $association['id']; ?></span>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status</label>
                                <p class="form-control-plaintext">
                                    <?php
                                    $status_class = $association['status'] === 'active' ? 'success' : 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_class; ?> fs-6">
                                        <?php echo ucfirst($association['status']); ?>
                                    </span>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Start Date</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo date('F d, Y', strtotime($association['start_date'])); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">End Date</label>
                                <p class="form-control-plaintext">
                                    <?php if ($association['end_date']): ?>
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo date('F d, Y', strtotime($association['end_date'])); ?>
                                    <?php else: ?>
                                        <span class="text-success">
                                            <i class="fas fa-infinity me-1"></i>Ongoing
                                        </span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Duration</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-clock me-1"></i>
                                    <?php echo $duration->format('%y years, %m months, %d days'); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Practitioner Details -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-tie me-2"></i>Practitioner Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Full Name</label>
                                <p class="form-control-plaintext"><?php echo htmlspecialchars($association['practitioner_name']); ?></p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-envelope me-1"></i>
                                    <a href="mailto:<?php echo htmlspecialchars($association['practitioner_email']); ?>">
                                        <?php echo htmlspecialchars($association['practitioner_email']); ?>
                                    </a>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Phone</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($association['practitioner_phone']); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Registration Number</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info fs-6"><?php echo htmlspecialchars($association['practitioner_reg']); ?></span>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Registration Status</label>
                                <p class="form-control-plaintext">
                                    <?php
                                    $p_status_class = $association['practitioner_status'] === 'Active' ? 'success' : 'warning';
                                    ?>
                                    <span class="badge bg-<?php echo $p_status_class; ?>">
                                        <?php echo htmlspecialchars($association['practitioner_status']); ?>
                                    </span>
                                </p>
                            </div>
                            
                            <?php if (!empty($association['qualification'])): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Qualification</label>
                                <p class="form-control-plaintext"><?php echo htmlspecialchars($association['qualification']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($association['specialization'])): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Specialization</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info"><?php echo htmlspecialchars($association['specialization']); ?></span>
                                </p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="view_consultant.php?id=<?php echo $association['practitioner_id']; ?>" class="btn btn-outline-success">
                            <i class="fas fa-eye me-1"></i>View Full Practitioner Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Firm Details -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Firm Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Firm Name</label>
                                <p class="form-control-plaintext"><?php echo htmlspecialchars($association['firm_name']); ?></p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-envelope me-1"></i>
                                    <a href="mailto:<?php echo htmlspecialchars($association['firm_email']); ?>">
                                        <?php echo htmlspecialchars($association['firm_email']); ?>
                                    </a>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Phone</label>
                                <p class="form-control-plaintext">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($association['firm_phone']); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Registration Number</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info fs-6"><?php echo htmlspecialchars($association['firm_reg']); ?></span>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Business Type</label>
                                <p class="form-control-plaintext"><?php echo htmlspecialchars($association['business_type'] ?? 'Not specified'); ?></p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Registration Status</label>
                                <p class="form-control-plaintext">
                                    <?php
                                    $f_status_class = $association['firm_status'] === 'Active' ? 'success' : 'warning';
                                    ?>
                                    <span class="badge bg-<?php echo $f_status_class; ?>">
                                        <?php echo htmlspecialchars($association['firm_status']); ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="view_firm.php?id=<?php echo $association['firm_id']; ?>" class="btn btn-outline-info">
                            <i class="fas fa-eye me-1"></i>View Full Firm Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Panel -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($association['status'] == 'active'): ?>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#actionModal" data-action="deactivate">
                                <i class="fas fa-pause me-1"></i>Deactivate Association
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#actionModal" data-action="terminate">
                                <i class="fas fa-stop me-1"></i>Terminate Association
                            </button>
                        </div>
                    <?php elseif ($association['status'] == 'inactive'): ?>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#actionModal" data-action="activate">
                                <i class="fas fa-play me-1"></i>Reactivate Association
                            </button>
                        </div>
                    <?php endif; ?>
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        <a href="edit_association.php?id=<?php echo $association_id; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-1"></i>Edit Association
                        </a>
                        <a href="view_associations.php?search=<?php echo urlencode($association['practitioner_email']); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-search me-1"></i>Find Similar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Quick Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-0">
                                    <?php echo $duration->format('%a'); ?>
                                </h4>
                                <small class="text-muted">Days</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info mb-0">
                                <?php echo $association['status'] === 'active' ? 'Active' : 'Inactive'; ?>
                            </h4>
                            <small class="text-muted">Status</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="actionModalTitle">Confirm Action</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" id="actionInput">
                    <p id="actionMessage"></p>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason (Optional)</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="Enter reason for this action..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="actionButton">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Handle action modal
document.addEventListener('DOMContentLoaded', function() {
    const actionModal = document.getElementById('actionModal');
    const actionInput = document.getElementById('actionInput');
    const actionModalTitle = document.getElementById('actionModalTitle');
    const actionMessage = document.getElementById('actionMessage');
    const actionButton = document.getElementById('actionButton');
    
    actionModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const action = button.getAttribute('data-action');
        
        actionInput.value = action;
        
        const practitionerName = '<?php echo addslashes($association['practitioner_name']); ?>';
        const firmName = '<?php echo addslashes($association['firm_name']); ?>';
        
        switch(action) {
            case 'activate':
                actionModalTitle.textContent = 'Reactivate Association';
                actionMessage.textContent = `Are you sure you want to reactivate the association between "${practitionerName}" and "${firmName}"?`;
                actionButton.textContent = 'Reactivate';
                actionButton.className = 'btn btn-success';
                break;
            case 'deactivate':
                actionModalTitle.textContent = 'Deactivate Association';
                actionMessage.textContent = `Are you sure you want to deactivate the association between "${practitionerName}" and "${firmName}"?`;
                actionButton.textContent = 'Deactivate';
                actionButton.className = 'btn btn-warning';
                break;
            case 'terminate':
                actionModalTitle.textContent = 'Terminate Association';
                actionMessage.textContent = `Are you sure you want to terminate the association between "${practitionerName}" and "${firmName}"? This will set an end date.`;
                actionButton.textContent = 'Terminate';
                actionButton.className = 'btn btn-danger';
                break;
        }
    });
});

// Auto-hide alerts
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('show')) {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }
    });
}, 5000);
</script>

<?php include '../includes/admin_footer.php'; ?>
