<?php
session_start();
require_once '../includes/config.php';

// Simple admin check (you can enhance this with proper admin authentication)
if (!isset($_SESSION['admin_logged_in'])) {
    // For demo purposes, we'll create a simple admin session
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_name'] = 'System Administrator';
}

// Handle verification actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $type = $_POST['type'] ?? '';
    $id = $_POST['id'] ?? '';
    $status = $_POST['status'] ?? '';
    
    if ($action === 'verify' && $id && $status && $type) {
        $table_map = [
            'shareholders' => 'firm_shareholders',
            'directors' => 'firm_directors',
            'secretaries' => 'firm_secretaries'
        ];
        
        if (isset($table_map[$type])) {
            $table = $table_map[$type];
            $sql = "UPDATE $table SET status = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("si", $status, $id);
            
            if ($stmt->execute()) {
                $message = ucfirst($type) . " record " . ($status === 'verified' ? 'approved' : 'rejected') . " successfully!";
                $message_type = 'success';
            } else {
                $message = "Error updating record.";
                $message_type = 'error';
            }
        }
    }
}

// Get pending records
function getPendingRecords($conn, $table, $type) {
    $sql = "SELECT s.*, f.name as firm_name FROM $table s 
            JOIN tax_firms f ON s.firm_id = f.id 
            WHERE s.status = 'pending' 
            ORDER BY s.created_at DESC";
    $result = $conn->query($sql);
    return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
}

$pending_shareholders = getPendingRecords($conn, 'firm_shareholders', 'shareholders');
$pending_directors = getPendingRecords($conn, 'firm_directors', 'directors');
$pending_secretaries = getPendingRecords($conn, 'firm_secretaries', 'secretaries');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Verify Company Structure</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: white;
            padding: 2rem;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #059669;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #6b7280;
        }

        .section {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .section-header {
            background: #f8fafc;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .section-header h2 {
            color: #374151;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f9fafb;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tbody tr:hover {
            background: #f8fafc;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            border: none;
            margin-right: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-approve {
            background: #10b981;
            color: white;
        }

        .btn-approve:hover {
            background: #059669;
        }

        .btn-reject {
            background: #ef4444;
            color: white;
        }

        .btn-reject:hover {
            background: #dc2626;
        }

        .message {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .message.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .message.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Company Structure Verification</h1>
            <p>Review and verify pending shareholders, directors, and company secretaries</p>
        </div>

        <?php if (isset($message)): ?>
            <div class="message <?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Pending Shareholders -->
        <div class="section">
            <div class="section-header">
                <h2>Pending Shareholders (<?php echo count($pending_shareholders); ?>)</h2>
            </div>
            <?php if (empty($pending_shareholders)): ?>
                <div class="empty-state">
                    <p>No pending shareholders to verify</p>
                </div>
            <?php else: ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Firm</th>
                            <th>Name</th>
                            <th>Shares</th>
                            <th>Percentage</th>
                            <th>Date Added</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pending_shareholders as $shareholder): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($shareholder['firm_name']); ?></td>
                                <td><?php echo htmlspecialchars($shareholder['name']); ?></td>
                                <td><?php echo number_format($shareholder['number_of_shares']); ?></td>
                                <td><?php echo $shareholder['share_percentage']; ?>%</td>
                                <td><?php echo date('M j, Y', strtotime($shareholder['created_at'])); ?></td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="verify">
                                        <input type="hidden" name="type" value="shareholders">
                                        <input type="hidden" name="id" value="<?php echo $shareholder['id']; ?>">
                                        <input type="hidden" name="status" value="verified">
                                        <button type="submit" class="btn btn-approve">Approve</button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="verify">
                                        <input type="hidden" name="type" value="shareholders">
                                        <input type="hidden" name="id" value="<?php echo $shareholder['id']; ?>">
                                        <input type="hidden" name="status" value="rejected">
                                        <button type="submit" class="btn btn-reject">Reject</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>

        <!-- Pending Directors -->
        <div class="section">
            <div class="section-header">
                <h2>Pending Directors (<?php echo count($pending_directors); ?>)</h2>
            </div>
            <?php if (empty($pending_directors)): ?>
                <div class="empty-state">
                    <p>No pending directors to verify</p>
                </div>
            <?php else: ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Firm</th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Appointment Date</th>
                            <th>Date Added</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pending_directors as $director): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($director['firm_name']); ?></td>
                                <td><?php echo htmlspecialchars($director['name']); ?></td>
                                <td><?php echo htmlspecialchars($director['position']); ?></td>
                                <td><?php echo date('M j, Y', strtotime($director['appointment_date'])); ?></td>
                                <td><?php echo date('M j, Y', strtotime($director['created_at'])); ?></td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="verify">
                                        <input type="hidden" name="type" value="directors">
                                        <input type="hidden" name="id" value="<?php echo $director['id']; ?>">
                                        <input type="hidden" name="status" value="verified">
                                        <button type="submit" class="btn btn-approve">Approve</button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="verify">
                                        <input type="hidden" name="type" value="directors">
                                        <input type="hidden" name="id" value="<?php echo $director['id']; ?>">
                                        <input type="hidden" name="status" value="rejected">
                                        <button type="submit" class="btn btn-reject">Reject</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>

        <!-- Pending Company Secretaries -->
        <div class="section">
            <div class="section-header">
                <h2>Pending Company Secretaries (<?php echo count($pending_secretaries); ?>)</h2>
            </div>
            <?php if (empty($pending_secretaries)): ?>
                <div class="empty-state">
                    <p>No pending company secretaries to verify</p>
                </div>
            <?php else: ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Firm</th>
                            <th>Name</th>
                            <th>Qualification</th>
                            <th>Professional Body</th>
                            <th>Date Added</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pending_secretaries as $secretary): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($secretary['firm_name']); ?></td>
                                <td><?php echo htmlspecialchars($secretary['name']); ?></td>
                                <td><?php echo htmlspecialchars($secretary['qualification']); ?></td>
                                <td><?php echo htmlspecialchars($secretary['professional_body'] ?? 'N/A'); ?></td>
                                <td><?php echo date('M j, Y', strtotime($secretary['created_at'])); ?></td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="verify">
                                        <input type="hidden" name="type" value="secretaries">
                                        <input type="hidden" name="id" value="<?php echo $secretary['id']; ?>">
                                        <input type="hidden" name="status" value="verified">
                                        <button type="submit" class="btn btn-approve">Approve</button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="verify">
                                        <input type="hidden" name="type" value="secretaries">
                                        <input type="hidden" name="id" value="<?php echo $secretary['id']; ?>">
                                        <input type="hidden" name="status" value="rejected">
                                        <button type="submit" class="btn btn-reject">Reject</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 2rem;">
            <a href="../firm/dashboard.php" style="color: #059669; text-decoration: none; font-weight: 500;">← Back to Firm Dashboard</a>
        </div>
    </div>
</body>
</html>
