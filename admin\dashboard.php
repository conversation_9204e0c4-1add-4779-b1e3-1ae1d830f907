<?php
session_start();
// Check if logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit;
}
require_once '../includes/config.php'; // Changed from '../config.php' to correct path
require_once '../includes/functions.php';
$page_title = "Admin Dashboard";

// Initialize counters with default values
$pending_count = 0;
$approved_count = 0;
$firms_count = 0;
$associations_count = 0;

// Check if tables exist before querying
$tables_exist = true;

// Check if tax_practitioners table exists
$check_table_sql = "SHOW TABLES LIKE 'tax_practitioners'";
$check_result = $conn->query($check_table_sql);
if ($check_result && $check_result->num_rows > 0) {
    // Count pending registrations
    $pending_sql = "SELECT COUNT(*) as count FROM tax_practitioners WHERE registration_status = 'Pending'";
    $pending_result = $conn->query($pending_sql);
    if ($pending_result) {
        $pending_count = $pending_result->fetch_assoc()['count'];
    }

    // Count approved practitioners (Active status)
    $approved_sql = "SELECT COUNT(*) as count FROM tax_practitioners WHERE registration_status = 'Active'";
    $approved_result = $conn->query($approved_sql);
    if ($approved_result) {
        $approved_count = $approved_result->fetch_assoc()['count'];
    }

    // Count rejected registrations
    $rejected_sql = "SELECT COUNT(*) as count FROM tax_practitioners WHERE registration_status = 'Rejected'";
    $rejected_result = $conn->query($rejected_sql);
    if ($rejected_result) {
        $rejected_count = $rejected_result->fetch_assoc()['count'];
    } else {
        $rejected_count = 0;
    }

    // Count CITN verification required
    $citn_required_sql = "SELECT COUNT(*) as count FROM tax_practitioners WHERE registration_status = 'CITN_Verification_Required'";
    $citn_required_result = $conn->query($citn_required_sql);
    if ($citn_required_result) {
        $citn_required_count = $citn_required_result->fetch_assoc()['count'];
    } else {
        $citn_required_count = 0;
    }

    // Get recent registrations
    $recent_sql = "SELECT * FROM tax_practitioners ORDER BY registration_date DESC LIMIT 5";
    $recent_result = $conn->query($recent_sql);
    // No fetch_assoc here, we'll check in the HTML section
} else {
    $tables_exist = false;
    $rejected_count = 0;
    $citn_required_count = 0;
}

// Check if tax_firms table exists
$check_table_sql = "SHOW TABLES LIKE 'tax_firms'";
$check_result = $conn->query($check_table_sql);
if ($check_result && $check_result->num_rows > 0) {
    // Count total firms
    $firms_sql = "SELECT COUNT(*) as count FROM tax_firms";
    $firms_result = $conn->query($firms_sql);
    if ($firms_result) {
        $firms_count = $firms_result->fetch_assoc()['count'];
    }
}

// Check if practitioner_firm_associations table exists and get counts
$check_table_sql = "SHOW TABLES LIKE 'practitioner_firm_associations'";
$check_result = $conn->query($check_table_sql);
if ($check_result && $check_result->num_rows > 0) {
    // Count total associations
    $associations_sql = "SELECT COUNT(*) as count FROM practitioner_firm_associations";
    $associations_result = $conn->query($associations_sql);
    if ($associations_result) {
        $associations_count = $associations_result->fetch_assoc()['count'];
    } else {
        $associations_count = 0;
    }

    // Count active associations
    $active_associations_sql = "SELECT COUNT(*) as count FROM practitioner_firm_associations WHERE status = 'active'";
    $active_associations_result = $conn->query($active_associations_sql);
    if ($active_associations_result) {
        $active_associations_count = $active_associations_result->fetch_assoc()['count'];
    } else {
        $active_associations_count = 0;
    }
} else {
    $associations_count = 0;
    $active_associations_count = 0;
}
$check_result = $conn->query($check_table_sql);
if ($check_result && $check_result->num_rows > 0) {
    // Count total associations
    $associations_sql = "SELECT COUNT(*) as count FROM practitioner_firm_associations WHERE status = 'active'";
    $associations_result = $conn->query($associations_sql);
    if ($associations_result) {
        $associations_count = $associations_result->fetch_assoc()['count'];
    }
}

include '../includes/header.php';
?>
<div class="container mt-4">    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</h2>
        <div>
            <div class="btn-group me-2" role="group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-eye me-2"></i>Firm Monitoring
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="firm_monitoring.php">
                        <i class="fas fa-chart-line me-2"></i>Activity Overview
                    </a></li>
                    <li><a class="dropdown-item" href="manage_firms.php">
                        <i class="fas fa-building me-2"></i>All Firms
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="firm_monitoring.php?activity_type=documents">
                        <i class="fas fa-file-upload me-2"></i>Document Activity
                    </a></li>
                    <li><a class="dropdown-item" href="firm_monitoring.php?activity_type=shareholders">
                        <i class="fas fa-users me-2"></i>Shareholder Changes
                    </a></li>
                    <li><a class="dropdown-item" href="firm_monitoring.php?activity_type=directors">
                        <i class="fas fa-user-tie me-2"></i>Director Changes
                    </a></li>
                    <li><a class="dropdown-item" href="firm_monitoring.php?activity_type=tax_clearance">
                        <i class="fas fa-certificate me-2"></i>Tax Records
                    </a></li>
                </ul>
            </div>
            <a href="logout.php" class="btn btn-outline-danger">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
        </div>
    </div>
    <!-- Stats Cards -->    <div class="row mb-4">
        <div class="col-md-3 mb-3">            <div class="card bg-primary text-white h-100">
                <div class="card-body">                    <div class="d-flex justify-content-between align-items-center">
                        <div>                            <h6 class="text-uppercase">Pending Registrations</h6>
                            <h1 class="display-4"><?php echo $pending_count; ?></h1>                        </div>
                        <i class="fas fa-user-clock fa-3x opacity-50"></i>                    </div>
                </div>                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="pending_registrations.php" class="text-white text-decoration-none">View Details</a>                    <i class="fas fa-arrow-circle-right text-white"></i>
                </div>            </div>
        </div>        
        <div class="col-md-3 mb-3">            <div class="card bg-success text-white h-100">
                <div class="card-body">                    <div class="d-flex justify-content-between align-items-center">
                        <div>                            <h6 class="text-uppercase">Active Practitioners</h6>
                            <h1 class="display-4"><?php echo $approved_count; ?></h1>                        </div>
                        <i class="fas fa-user-check fa-3x opacity-50"></i>                    </div>
                </div>                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="pending_registrations.php?status=Active" class="text-white text-decoration-none">View Details</a>                    <i class="fas fa-arrow-circle-right text-white"></i>
                </div>            </div>
        </div>        
        <div class="col-md-3 mb-3">            <div class="card bg-info text-white h-100">
                <div class="card-body">                    <div class="d-flex justify-content-between align-items-center">
                        <div>                            <h6 class="text-uppercase">Registered Firms</h6>
                            <h1 class="display-4"><?php echo $firms_count; ?></h1>                        </div>
                        <i class="fas fa-building fa-3x opacity-50"></i>                    </div>
                </div>                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="manage_firms.php" class="text-white text-decoration-none">View Details</a>                    <i class="fas fa-arrow-circle-right text-white"></i>
                </div>            </div>
        </div>        
        <div class="col-md-3 mb-3">            <div class="card bg-warning text-white h-100">
                <div class="card-body">                    <div class="d-flex justify-content-between align-items-center">
                        <div>                            <h6 class="text-uppercase">Active Associations</h6>
                            <h1 class="display-4"><?php echo $active_associations_count; ?></h1>                        </div>
                        <i class="fas fa-link fa-3x opacity-50"></i>                    </div>
                </div>                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="view_associations.php" class="text-white text-decoration-none">View Details</a>                    <i class="fas fa-arrow-circle-right text-white"></i>
                </div>            </div>
        </div>    </div>

    <!-- Additional Registration Stats -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Rejected Applications</h6>
                            <h1 class="display-4"><?php echo $rejected_count; ?></h1>
                        </div>
                        <i class="fas fa-user-times fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="pending_registrations.php?status=Rejected" class="text-white text-decoration-none">View Details</a>
                    <i class="fas fa-arrow-circle-right text-white"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                        <h4>
                            <?php
                            $today_reg_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners WHERE DATE(registration_date) = CURDATE()");
                            $today_registrations = $today_reg_result ? $today_reg_result->fetch_assoc()['count'] : 0;
                            echo $today_registrations;
                            ?>
                        </h4>
                    <p class="mb-0">Today's Registrations</p>
                </div>
            </div>
        </div>
     </div>
    </div>

    <!-- Firm Monitoring Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>Firm Activity Monitoring</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-upload fa-2x text-primary mb-2"></i>
                                    <h4>
                                        <?php
                                        $recent_uploads = $conn->query("SELECT COUNT(*) as count FROM firm_documents WHERE DATE(upload_date) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)");
                                        echo $recent_uploads ? $recent_uploads->fetch_assoc()['count'] : 0;
                                        ?>
                                    </h4>
                                    <p class="mb-0">Documents Uploaded (7 days)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                                    <h4>
                                        <?php
                                        $recent_shareholders = $conn->query("SELECT COUNT(*) as count FROM firm_shareholders WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)");
                                        echo $recent_shareholders ? $recent_shareholders->fetch_assoc()['count'] : 0;
                                        ?>
                                    </h4>
                                    <p class="mb-0">New Shareholders (7 days)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-tie fa-2x text-info mb-2"></i>
                                    <h4>
                                        <?php
                                        $recent_directors = $conn->query("SELECT COUNT(*) as count FROM firm_directors WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)");
                                        echo $recent_directors ? $recent_directors->fetch_assoc()['count'] : 0;
                                        ?>
                                    </h4>
                                    <p class="mb-0">New Directors (7 days)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-certificate fa-2x text-warning mb-2"></i>
                                    <h4>
                                        <?php
                                        $recent_tax_clearance = $conn->query("SELECT COUNT(*) as count FROM firm_tax_clearance WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)");
                                        echo $recent_tax_clearance ? $recent_tax_clearance->fetch_assoc()['count'] : 0;
                                        ?>
                                    </h4>
                                    <p class="mb-0">Tax Clearance Records (7 days)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <a href="firm_monitoring.php" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>View Detailed Firm Monitoring
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CITN Management Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-certificate me-2"></i>CITN Management</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php
                        // Get CITN statistics
                        $citn_stats = [
                            'total_practitioners' => 0,
                            'with_citn' => 0,
                            'verified' => 0,
                            'pending' => 0
                        ];

                        // Check if tax_practitioners table exists
                        $table_check = $conn->query("SHOW TABLES LIKE 'tax_practitioners'");
                        if ($table_check && $table_check->num_rows > 0) {
                            // Get total practitioners
                            $total_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners");
                            if ($total_result) {
                                $citn_stats['total_practitioners'] = $total_result->fetch_assoc()['count'];
                            }

                            // Get practitioners with CITN numbers
                            $citn_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners WHERE citn_number IS NOT NULL AND citn_number != ''");
                            if ($citn_result) {
                                $citn_stats['with_citn'] = $citn_result->fetch_assoc()['count'];
                            }

                            // Get verified CITN
                            $verified_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners WHERE citn_verification_status = 'verified'");
                            if ($verified_result) {
                                $citn_stats['verified'] = $verified_result->fetch_assoc()['count'];
                            }

                            // Get pending CITN
                            $pending_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners WHERE citn_verification_status = 'pending'");
                            if ($pending_result) {
                                $citn_stats['pending'] = $pending_result->fetch_assoc()['count'];
                            }
                        }
                        ?>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <h4><?php echo $citn_stats['total_practitioners']; ?></h4>
                                    <p class="mb-0">Total Practitioners</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-certificate fa-2x mb-2"></i>
                                    <h4><?php echo $citn_stats['with_citn']; ?></h4>
                                    <p class="mb-0">With CITN Numbers</p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                                    <h4><?php echo $citn_stats['verified']; ?></h4>
                                    <p class="mb-0">CITN Verified</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <h4><?php echo $citn_stats['pending']; ?></h4>
                                    <p class="mb-0">Pending Verification</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Statistics Row -->
                    <div class="row">
                    <div class="row mt-3">
                        <div class="col-md-6 mb-3">
                            <a href="citn_management.php" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-cog me-2"></i>CITN Management Dashboard
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="citn_reports.php" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-chart-line me-2"></i>CITN Reports & Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="generate_reports.php" class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-file-export fa-2x mb-2"></i><br>
                                Generate Reports
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="pending_registrations.php" class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-clock fa-2x mb-2"></i><br>
                                Pending Registrations
                                <?php
                                // Get pending count for badge
                                $pending_count_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners WHERE registration_status = 'Pending'");
                                if ($pending_count_result) {
                                    $pending_count = $pending_count_result->fetch_assoc()['count'];
                                    if ($pending_count > 0) {
                                        echo "<br><span class='badge bg-danger'>{$pending_count}</span>";
                                    }
                                }
                                ?>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="system_settings.php" class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-cogs fa-2x mb-2"></i><br>
                                System Settings
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="add_admin.php" class="btn btn-outline-danger w-100 py-3">
                                <i class="fas fa-user-shield fa-2x mb-2"></i><br>
                                Register New Admin
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Remove or comment out the debugging section -->
    <!--
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="alert alert-info">
                <p><strong>Current directory:</strong> <?php echo __DIR__; ?></p>
                <p><strong>Document root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
                <p><strong>Script filename:</strong> <?php echo $_SERVER['SCRIPT_FILENAME']; ?></p>
            </div>
        </div>
    </div>
    -->
    <!-- Recent Registrations -->    <div class="row">
        <div class="col-md-8 mb-4">            <div class="card">
                <div class="card-header bg-primary text-white">                    <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Recent Registrations</h5>
                </div>                <div class="card-body">
                    <?php if (isset($recent_result) && $recent_result && $recent_result->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($consultant = $recent_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($consultant['full_name']); ?></td>
                                            <td><?php echo htmlspecialchars($consultant['email']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $consultant['registration_status'] == 'Active' ? 'success' : ($consultant['registration_status'] == 'Pending' ? 'warning' : 'danger'); ?>">
                                                    <?php echo htmlspecialchars($consultant['registration_status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($consultant['registration_date'])); ?></td>
                                            <td>
                                                <a href="pending_registrations.php?search=<?php echo urlencode($consultant['email']); ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end mt-3">
                            <a href="pending_registrations.php?status=all" class="btn btn-outline-primary">View All Practitioners</a>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No recent registrations found.</p>
                    <?php endif; ?>
                </div>
            </div>        </div>
                <!-- System Overview -->
        <div class="col-md-4 mb-4">            <div class="card">
                <div class="card-header bg-primary text-white">                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Overview</h5>
                </div>                <div class="card-body">
                    <ul class="list-group list-group-flush">                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-calendar-check me-2"></i>Today's Date</span>                            <span class="badge bg-primary rounded-pill"><?php echo date('M d, Y'); ?></span>
                        </li>                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-user-shield me-2"></i>Admin User</span>                            <span class="badge bg-success rounded-pill"><?php echo htmlspecialchars($_SESSION['admin_name'] ?? 'Admin'); ?></span>
                        </li>                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-server me-2"></i>System Status</span>                            <span class="badge bg-success rounded-pill">Online</span>
                        </li>                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-database me-2"></i>Database</span>                            <span class="badge bg-success rounded-pill">Connected</span>
                        </li>                    </ul>
                    <div class="mt-3">                        <h6><i class="fas fa-tasks me-2"></i>Quick Tasks</h6>
                        <div class="list-group">                            <a href="backup_system.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-download me-2"></i>Backup System                            </a>
                            <a href="send_notifications.php" class="list-group-item list-group-item-action">                                <i class="fas fa-bell me-2"></i>Send Notifications
                            </a>                            <a href="update_profile.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-user-edit me-2"></i>Update Profile                            </a>
                        </div>                    </div>
                </div>            </div>
        </div>    </div>
</div>
<?php include '../includes/footer.php';
$conn->close();

