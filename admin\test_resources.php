<?php
session_start();
$page_title = "Resource Test";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resource Test - Tax Registration System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom Admin CSS -->
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
            background: rgba(255,255,255,0.95);
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .resource-status {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.25rem;
        }
        
        .resource-ok {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .resource-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-cogs me-2"></i>Resource Loading Test</h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            This page tests if all CSS and JavaScript resources are loading correctly.
        </div>
        
        <h3>CSS Resources</h3>
        <div class="resource-status resource-ok">
            <i class="fas fa-check me-2"></i>Bootstrap CSS - Loaded from CDN
        </div>
        <div class="resource-status resource-ok">
            <i class="fas fa-check me-2"></i>Font Awesome - Loaded from CDN
        </div>
        <div class="resource-status resource-ok">
            <i class="fas fa-check me-2"></i>DataTables CSS - Loaded from CDN
        </div>
        <div class="resource-status resource-ok">
            <i class="fas fa-check me-2"></i>Custom Admin CSS - Local file
        </div>
        
        <h3 class="mt-4">JavaScript Resources</h3>
        <div id="js-bootstrap" class="resource-status">
            <i class="fas fa-spinner fa-spin me-2"></i>Bootstrap JS - Testing...
        </div>
        <div id="js-jquery" class="resource-status">
            <i class="fas fa-spinner fa-spin me-2"></i>jQuery - Testing...
        </div>
        <div id="js-datatables" class="resource-status">
            <i class="fas fa-spinner fa-spin me-2"></i>DataTables JS - Testing...
        </div>
        
        <h3 class="mt-4">Component Tests</h3>
        
        <!-- Bootstrap Components Test -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">Bootstrap Components</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#testModal">
                    <i class="fas fa-eye me-1"></i>Test Modal
                </button>
                <button type="button" class="btn btn-success me-2">
                    <i class="fas fa-check me-1"></i>Success Button
                </button>
                <button type="button" class="btn btn-danger">
                    <i class="fas fa-times me-1"></i>Danger Button
                </button>
            </div>
        </div>
        
        <!-- DataTable Test -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">DataTable Test</h5>
            </div>
            <div class="card-body">
                <table id="testTable" class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td>Admin</td>
                            <td><span class="badge bg-success">Active</span></td>
                        </tr>
                        <tr>
                            <td>Jane Smith</td>
                            <td><EMAIL></td>
                            <td>User</td>
                            <td><span class="badge bg-warning">Pending</span></td>
                        </tr>
                        <tr>
                            <td>Bob Johnson</td>
                            <td><EMAIL></td>
                            <td>User</td>
                            <td><span class="badge bg-danger">Inactive</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="manage_users.php" class="btn btn-primary">
                <i class="fas fa-arrow-left me-1"></i>Back to Manage Users
            </a>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="testModalLabel">Test Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>This modal tests if Bootstrap JavaScript is working correctly.</p>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>Bootstrap Modal is working!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Test jQuery
            if (typeof jQuery !== 'undefined') {
                $('#js-jquery').removeClass('resource-status').addClass('resource-status resource-ok');
                $('#js-jquery').html('<i class="fas fa-check me-2"></i>jQuery - Loaded successfully');
            } else {
                $('#js-jquery').removeClass('resource-status').addClass('resource-status resource-error');
                $('#js-jquery').html('<i class="fas fa-times me-2"></i>jQuery - Failed to load');
            }
            
            // Test Bootstrap
            if (typeof bootstrap !== 'undefined') {
                $('#js-bootstrap').removeClass('resource-status').addClass('resource-status resource-ok');
                $('#js-bootstrap').html('<i class="fas fa-check me-2"></i>Bootstrap JS - Loaded successfully');
            } else {
                $('#js-bootstrap').removeClass('resource-status').addClass('resource-status resource-error');
                $('#js-bootstrap').html('<i class="fas fa-times me-2"></i>Bootstrap JS - Failed to load');
            }
            
            // Test DataTables
            try {
                $('#testTable').DataTable({
                    "pageLength": 5,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "paging": true
                });
                $('#js-datatables').removeClass('resource-status').addClass('resource-status resource-ok');
                $('#js-datatables').html('<i class="fas fa-check me-2"></i>DataTables JS - Loaded and initialized successfully');
            } catch (e) {
                $('#js-datatables').removeClass('resource-status').addClass('resource-status resource-error');
                $('#js-datatables').html('<i class="fas fa-times me-2"></i>DataTables JS - Failed to initialize: ' + e.message);
            }
        });
    </script>
</body>
</html>
