<!DOCTYPE html>
<html>
<head>
    <title>Session Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Session Test</h1>
    
    <div>
        <button class="btn-primary" onclick="testSession()">Test Current Session</button>
        <button class="btn-success" onclick="testProfileUpdate()">Test Profile Update</button>
    </div>
    
    <div id="results"></div>
    
    <script>
        function testSession() {
            fetch('session_test.php')
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('results');
                    
                    let html = '<div class="result">';
                    html += '<h3>Session Test Results:</h3>';
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    
                    if (data.database_test === 'SUCCESS') {
                        html += '<div class="success">✅ Session is working correctly!</div>';
                    } else {
                        html += '<div class="error">❌ Session has issues: ' + (data.error || 'Unknown error') + '</div>';
                    }
                    
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('results').innerHTML = 
                        '<div class="result error">❌ Error testing session: ' + error.message + '</div>';
                });
        }
        
        function testProfileUpdate() {
            const testData = {
                section: 'address',
                data: {
                    address: 'Test Street 123',
                    city: 'Test City',
                    state: 'Test State',
                    postal_code: '12345'
                }
            };
            
            fetch('update_profile.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('results');
                
                let html = '<div class="result">';
                html += '<h3>Profile Update Test Results:</h3>';
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.success) {
                    html += '<div class="success">✅ Profile update is working!</div>';
                } else {
                    html += '<div class="error">❌ Profile update failed: ' + (data.message || 'Unknown error') + '</div>';
                }
                
                html += '</div>';
                resultsDiv.innerHTML = html;
            })
            .catch(error => {
                document.getElementById('results').innerHTML = 
                    '<div class="result error">❌ Error testing profile update: ' + error.message + '</div>';
            });
        }
        
        // Auto-test on page load
        window.onload = function() {
            testSession();
        };
    </script>
</body>
</html>
