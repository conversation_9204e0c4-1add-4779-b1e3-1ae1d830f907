<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit;
}

// Initialize variables
$success_message = '';
$error_message = '';
$name = '';
$email = '';

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Simple validation
    if (empty($name) || empty($email) || empty($password) || empty($confirm_password)) {
        $error_message = "All fields are required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = "Invalid email format";
    } elseif ($password !== $confirm_password) {
        $error_message = "Passwords do not match";
    } elseif (strlen($password) < 8) {
        $error_message = "Password must be at least 8 characters long";
    } else {
        // Check if administrators table exists
        $table_check = $conn->query("SHOW TABLES LIKE 'administrators'");
        
        if ($table_check->num_rows == 0) {
            // Create administrators table with role column
            $create_table_sql = "CREATE TABLE IF NOT EXISTS `administrators` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(100) NOT NULL,
              `email` varchar(100) NOT NULL,
              `password` varchar(255) NOT NULL,
              `role` varchar(20) NOT NULL DEFAULT 'admin',
              `last_login` datetime DEFAULT NULL,
              `created_at` datetime NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `email` (`email`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
            
            if (!$conn->query($create_table_sql)) {
                $error_message = "Error creating administrators table: " . $conn->error;
            }
        } else {
            // Check if role column exists
            $column_check = $conn->query("SHOW COLUMNS FROM `administrators` LIKE 'role'");
            
            if ($column_check->num_rows == 0) {
                // Add role column if it doesn't exist
                $add_column_sql = "ALTER TABLE `administrators` ADD COLUMN `role` varchar(20) NOT NULL DEFAULT 'admin'";
                
                if (!$conn->query($add_column_sql)) {
                    $error_message = "Error adding role column: " . $conn->error;
                }
            }
        }
        
        if (empty($error_message)) {
            // Check if email already exists
            $check_sql = "SELECT id FROM administrators WHERE email = '" . $conn->real_escape_string($email) . "'";
            $check_result = $conn->query($check_sql);
            
            if ($check_result && $check_result->num_rows > 0) {
                $error_message = "Email already exists. Please use a different email.";
            } else {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert new admin - without role column if it doesn't exist
                $column_check = $conn->query("SHOW COLUMNS FROM `administrators` LIKE 'role'");
                
                if ($column_check->num_rows > 0) {
                    // Role column exists, include it in the query
                    $sql = "INSERT INTO administrators (name, email, password, role, created_at) 
                            VALUES ('" . $conn->real_escape_string($name) . "', 
                                    '" . $conn->real_escape_string($email) . "', 
                                    '" . $conn->real_escape_string($hashed_password) . "', 
                                    'admin', NOW())";
                } else {
                    // Role column doesn't exist, exclude it from the query
                    $sql = "INSERT INTO administrators (name, email, password, created_at) 
                            VALUES ('" . $conn->real_escape_string($name) . "', 
                                    '" . $conn->real_escape_string($email) . "', 
                                    '" . $conn->real_escape_string($hashed_password) . "', 
                                    NOW())";
                }
                
                if ($conn->query($sql)) {
                    $success_message = "New administrator account created successfully!";
                    $name = $email = ''; // Clear form data
                } else {
                    $error_message = "Error: " . $conn->error;
                }
            }
        }
    }
}

// Include header
include '../includes/header.php';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-plus me-2"></i>Add New Administrator</h2>
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>
    </div>
    
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Administrator Registration Form</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($name); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($email); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Password must be at least 8 characters long</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Register Administrator</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>




