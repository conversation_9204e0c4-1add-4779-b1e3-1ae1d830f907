<?php
require_once '../includes/config.php';

echo "<h2>Setting up Documents Database Table</h2>";

// Create firm_documents table
$create_table_sql = "
CREATE TABLE IF NOT EXISTS firm_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    firm_id INT NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('uploaded', 'pending', 'rejected') DEFAULT 'uploaded',
    uploaded_by VARCHAR(100) DEFAULT 'System',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (firm_id) REFERENCES tax_firms(id) ON DELETE CASCADE,
    UNIQUE KEY unique_firm_document (firm_id, document_type),
    INDEX idx_firm_id (firm_id),
    INDEX idx_document_type (document_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

if ($conn->query($create_table_sql)) {
    echo "<p style='color: green;'>✅ firm_documents table created successfully!</p>";
} else {
    echo "<p style='color: red;'>❌ Error creating table: " . $conn->error . "</p>";
    exit;
}

// Create uploads directory structure
$base_upload_dir = '../uploads/documents/';
if (!file_exists($base_upload_dir)) {
    if (mkdir($base_upload_dir, 0755, true)) {
        echo "<p style='color: green;'>✅ Upload directory created: $base_upload_dir</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create upload directory</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ Upload directory already exists</p>";
}

// Insert sample document records for existing firms
$firms_sql = "SELECT id, name FROM tax_firms";
$firms_result = $conn->query($firms_sql);

if ($firms_result->num_rows > 0) {
    echo "<h3>Setting up sample documents for existing firms:</h3>";
    
    while ($firm = $firms_result->fetch_assoc()) {
        $firm_id = $firm['id'];
        $firm_name = $firm['name'];
        
        echo "<h4>Firm: " . htmlspecialchars($firm_name) . " (ID: $firm_id)</h4>";
        
        // Sample documents with different statuses
        $sample_documents = [
            [
                'type' => 'memart',
                'name' => 'MEMART_' . $firm_id . '_sample.pdf',
                'status' => 'uploaded',
                'size' => 1024000
            ],
            [
                'type' => 'cac_status',
                'name' => 'CAC_Status_' . $firm_id . '_sample.pdf',
                'status' => 'uploaded',
                'size' => 512000
            ],
            [
                'type' => 'utility_bill',
                'name' => 'Utility_Bill_' . $firm_id . '_sample.pdf',
                'status' => 'uploaded',
                'size' => 256000
            ],
            [
                'type' => 'incorporation_cert',
                'name' => 'Incorporation_' . $firm_id . '_sample.pdf',
                'status' => 'uploaded',
                'size' => 768000
            ]
            // Note: tax_clearance is left as pending (not inserted)
        ];
        
        foreach ($sample_documents as $doc) {
            // Create firm-specific directory
            $firm_dir = $base_upload_dir . 'firm_' . $firm_id . '/';
            if (!file_exists($firm_dir)) {
                mkdir($firm_dir, 0755, true);
            }
            
            $file_path = $firm_dir . $doc['name'];
            
            // Check if document already exists
            $check_sql = "SELECT id FROM firm_documents WHERE firm_id = ? AND document_type = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("is", $firm_id, $doc['type']);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows == 0) {
                // Insert document record
                $insert_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status) 
                               VALUES (?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_sql);
                $insert_stmt->bind_param("isssis", $firm_id, $doc['type'], $doc['name'], $file_path, $doc['size'], $doc['status']);
                
                if ($insert_stmt->execute()) {
                    echo "<p style='color: green;'>✅ Added: " . $doc['type'] . "</p>";
                    
                    // Create a dummy file (for demo purposes)
                    file_put_contents($file_path, "Sample document content for " . $doc['type']);
                } else {
                    echo "<p style='color: red;'>❌ Failed to add: " . $doc['type'] . "</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ Already exists: " . $doc['type'] . "</p>";
            }
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ No firms found in database</p>";
}

// Show current document status
echo "<h3>Current Document Status:</h3>";
$status_sql = "
    SELECT 
        tf.name as firm_name,
        fd.document_type,
        fd.file_name,
        fd.status,
        fd.upload_date
    FROM tax_firms tf
    LEFT JOIN firm_documents fd ON tf.id = fd.firm_id
    ORDER BY tf.name, fd.document_type
";

$status_result = $conn->query($status_sql);

if ($status_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Firm</th><th>Document Type</th><th>File Name</th><th>Status</th><th>Upload Date</th></tr>";
    
    while ($row = $status_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['firm_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['document_type'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($row['file_name'] ?? 'Not uploaded') . "</td>";
        echo "<td>" . htmlspecialchars($row['status'] ?? 'pending') . "</td>";
        echo "<td>" . htmlspecialchars($row['upload_date'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px;">
    <h3>Setup Complete!</h3>
    <p><strong>Next steps:</strong></p>
    <ul>
        <li>Database table created for document storage</li>
        <li>Upload directories created</li>
        <li>Sample documents added for testing</li>
        <li>Ready to test upload/view/download functionality</li>
    </ul>
    <p><a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Go to Dashboard</a></p>
</div>
