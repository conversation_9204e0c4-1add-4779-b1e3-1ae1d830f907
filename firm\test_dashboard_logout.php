<?php
session_start();

// Simple login check
if (!isset($_SESSION['firm_id']) || !isset($_SESSION['firm_email'])) {
    header("Location: login.php");
    exit();
}

$firm_id = $_SESSION['firm_id'];
$firm_name = $_SESSION['firm_name'];
$firm_email = $_SESSION['firm_email'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard - Logout Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .btn { padding: 10px 15px; margin: 5px; text-decoration: none; border-radius: 4px; display: inline-block; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-primary { background-color: #007bff; color: white; }
        .logout-section { border: 2px solid #dc3545; padding: 20px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Dashboard - Logout Functionality</h1>
        <p><strong>Logged in as:</strong> <?php echo htmlspecialchars($firm_name); ?> (<?php echo htmlspecialchars($firm_email); ?>)</p>
        <p><strong>Firm ID:</strong> <?php echo $firm_id; ?></p>
        <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
    </div>
    
    <div class="logout-section">
        <h2>Logout Tests</h2>
        <p>Try each of these logout methods:</p>
        
        <h3>1. Direct Logout (No JavaScript)</h3>
        <a href="logout.php" class="btn btn-danger">Direct Logout Link</a>
        <p><em>This should work immediately without any confirmation.</em></p>
        
        <h3>2. JavaScript Confirmation Logout</h3>
        <button onclick="confirmLogout()" class="btn btn-danger">Logout with Confirmation</button>
        <p><em>This mimics the dashboard logout button.</em></p>
        
        <h3>3. Form-based Logout</h3>
        <form method="POST" action="logout.php" style="display: inline;">
            <button type="submit" class="btn btn-danger">Form Logout</button>
        </form>
        <p><em>This uses a POST form instead of GET.</em></p>
    </div>
    
    <div>
        <h2>Navigation</h2>
        <a href="dashboard.php" class="btn btn-primary">Go to Real Dashboard</a>
        <a href="debug_session.php" class="btn btn-primary">Debug Session</a>
        <a href="login.php" class="btn btn-primary">Login Page</a>
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
        <h3>Current Session Data:</h3>
        <pre><?php print_r($_SESSION); ?></pre>
    </div>
    
    <script>
        function confirmLogout() {
            console.log('Logout button clicked');
            if (confirm('Are you sure you want to logout?')) {
                console.log('User confirmed logout, redirecting...');
                window.location.href = 'logout.php';
            } else {
                console.log('User cancelled logout');
            }
        }
        
        // Log when page loads
        console.log('Test dashboard loaded');
        console.log('Current session data available');
    </script>
</body>
</html>
