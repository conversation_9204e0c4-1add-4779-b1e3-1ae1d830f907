# none, visible or audible
set bell-style visible

# Ask before displaying >40 items
# Since $WINDIR $PATH var can be in $PATH, this could list
# all window executables in C:\WINDOWS
set completion-query-items 40

# Ignore case for the command-line-completion functionality
# on:  default on a Windows style console
# off: default on a *nix style console
set completion-ignore-case on

# disable/enable 8bit input
set input-meta on
set output-meta on
set convert-meta off

# visible-stats
# Append a mark according to the file type in a listing
set visible-stats off
set mark-directories on
set mark-symlinked-directories on

# Beep first, show all only upon double-Tab
set show-all-if-ambiguous off

# MSYSTEM is emacs based
$if mode=emacs
  # Common to Console & RXVT
  "\e[2;2~": paste-from-clipboard   # Shift-Insert
  "\e[5~": beginning-of-history     # Page up
  "\e[6~": end-of-history           # Page down

  # Mintty
  "\e[1;5D": "\eOD"                 # Ctrl-Left
  "\e[1;5C": "\eOC"                 # Ctrl-Right
  "\e[1;5A": "\eOA"                 # Ctrl-Up
  "\e[1;5B": "\eOB"                 # Ctrl-Down
  "\e[1;3D": "\e\e[D"               # Alt-Left
  "\e[1;3C": "\e\e[C"               # Alt-Right

  "\e[Z": complete                  # Shift-Tab

  $if term=msys # RXVT
    "\e[7~": beginning-of-line      # Home Key
    "\e[8~": end-of-line            # End Key
    "\e[11~": display-shell-version # F1
    "\e[15~": re-read-init-file     # F5
    "\e[12~": "Function Key 2"
    "\e[13~": "Function Key 3"
    "\e[14~": "Function Key 4"
    "\e[17~": "Function Key 6"
    "\e[18~": "Function Key 7"
    "\e[19~": "Function Key 8"
    "\e[20~": "Function Key 9"
    "\e[21~": "Function Key 10"
  $else
  # Eh, normal Console is not really cygwin anymore, is it? Using 'else' instead. -mstormo
  # $if term=cygwin # Console
    "\e[1~": beginning-of-line      # Home Key
    "\e[4~": end-of-line            # End Key
    "\e[3~": delete-char            # Delete Key
    "\e[1;5D": backward-word        # Ctrl-Left
    "\e[1;5C": forward-word         # Ctrl-Right
    "\e\e[C": forward-word          # Alt-Right
    "\e\e[D": backward-word         # Alt-Left
    "\e[17~": "Function Key 6"
    "\e[18~": "Function Key 7"
    "\e[19~": "Function Key 8"
    "\e[20~": "Function Key 9"
    "\e[21~": "Function Key 10"
    "\e[23~": "Function Key 11"
  $endif

  $if term=cygwin
    "\e[A": previous-history # Cursor Up
    "\e[B": next-history     # Cursor Down
    "\e[C": forward-char     # Cursor Right
    "\e[D": backward-char    # Cursor Left
  $endif
$endif
