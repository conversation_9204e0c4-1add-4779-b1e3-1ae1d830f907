<?php
session_start();
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

$firm_id = $_SESSION['firm_id'];

// Check if file and document type are provided
if (!isset($_FILES['document']) || !isset($_POST['document_type'])) {
    echo json_encode(['success' => false, 'message' => 'Missing file or document type']);
    exit;
}

$file = $_FILES['document'];
$document_type = $_POST['document_type'];

// Validate document type
$allowed_types = ['memart', 'cac_status', 'tax_clearance', 'utility_bill', 'incorporation_cert'];
if (!in_array($document_type, $allowed_types)) {
    echo json_encode(['success' => false, 'message' => 'Invalid document type']);
    exit;
}

// Check for upload errors
if ($file['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'File upload error: ' . $file['error']]);
    exit;
}

// Validate file size (10MB max)
if ($file['size'] > 10 * 1024 * 1024) {
    echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 10MB']);
    exit;
}

// Create upload directory
$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
if (!file_exists($upload_dir)) {
    if (!mkdir($upload_dir, 0755, true)) {
        echo json_encode(['success' => false, 'message' => 'Failed to create upload directory']);
        exit;
    }
}

// Generate unique filename
$file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
$unique_filename = $document_type . '_' . time() . '_' . uniqid() . '.' . $file_extension;
$file_path = $upload_dir . $unique_filename;

// Move uploaded file
if (!move_uploaded_file($file['tmp_name'], $file_path)) {
    echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file']);
    exit;
}

try {
    // Use REPLACE to handle both insert and update
    $sql = "REPLACE INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status, upload_date) 
            VALUES (?, ?, ?, ?, ?, 'uploaded', NOW())";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('Database prepare failed: ' . $conn->error);
    }
    
    $stmt->bind_param("isssi", $firm_id, $document_type, $file['name'], $file_path, $file['size']);
    
    if (!$stmt->execute()) {
        throw new Exception('Database execute failed: ' . $stmt->error);
    }
    
    $stmt->close();
    
    // Verify the record was saved
    $verify_sql = "SELECT * FROM firm_documents WHERE firm_id = ? AND document_type = ?";
    $verify_stmt = $conn->prepare($verify_sql);
    $verify_stmt->bind_param("is", $firm_id, $document_type);
    $verify_stmt->execute();
    $verify_result = $verify_stmt->get_result();
    
    if ($verify_result->num_rows === 0) {
        throw new Exception('Document was not saved to database');
    }
    
    $doc = $verify_result->fetch_assoc();
    $verify_stmt->close();
    
    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Document uploaded successfully',
        'document_type' => $document_type,
        'file_name' => $file['name'],
        'upload_date' => date('M j, Y'),
        'database_id' => $doc['id']
    ]);
    
} catch (Exception $e) {
    // Delete uploaded file if database operation failed
    if (file_exists($file_path)) {
        unlink($file_path);
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
