<?php
// Add debugging at the top
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once '../includes/config.php';
require_once '../includes/audit_functions.php';

// Check if firm is logged in
if (!isset($_SESSION['firm_id'])) {
    header("Location: login.php");
    exit();
}

$firm_id = $_SESSION['firm_id'];
$success = '';
$error = '';

// Check if firm_association_requests table exists
$table_check = $conn->query("SHOW TABLES LIKE 'firm_association_requests'");
if ($table_check->num_rows == 0) {
    echo "<div class='alert alert-danger mt-3'>";
    echo "<strong>Database Error:</strong> The firm_association_requests table doesn't exist.";
    echo "</div>";
    
    echo "<div class='alert alert-info mt-3'>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='create_table' value='1'>";
    echo "<button type='submit' class='btn btn-primary'>Create Missing Table</button>";
    echo "</form>";
    echo "</div>";
    
    // Create the table if requested
    if (isset($_POST['create_table'])) {
        $create_table_sql = "CREATE TABLE IF NOT EXISTS `firm_association_requests` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `practitioner_id` int(11) NOT NULL,
            `firm_id` int(11) NOT NULL,
            `notes` text,
            `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
            `request_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `response_date` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `practitioner_id` (`practitioner_id`),
            KEY `firm_id` (`firm_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        if ($conn->query($create_table_sql)) {
            echo "<div class='alert alert-success mt-3'>";
            echo "Table created successfully! Please refresh the page.";
            echo "</div>";
            echo "<meta http-equiv='refresh' content='2'>";
        } else {
            echo "<div class='alert alert-danger mt-3'>";
            echo "Error creating table: " . $conn->error;
            echo "</div>";
        }
    }
}

// Process association request approval/rejection
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['request_id']) && isset($_POST['action'])) {
    $request_id = $_POST['request_id'];
    $action = $_POST['action'];
    
    // Get practitioner information
    $get_request_sql = "SELECT r.practitioner_id, p.full_name 
                       FROM firm_association_requests r
                       JOIN tax_practitioners p ON r.practitioner_id = p.id
                       WHERE r.id = ? AND r.firm_id = ?";
    $get_request_stmt = $conn->prepare($get_request_sql);
    $get_request_stmt->bind_param("ii", $request_id, $firm_id);
    $get_request_stmt->execute();
    $get_request_result = $get_request_stmt->get_result();
    
    if ($get_request_result->num_rows > 0) {
        $request_data = $get_request_result->fetch_assoc();
        $practitioner_id = $request_data['practitioner_id'];
        $practitioner_name = $request_data['full_name'];
        
        if ($action == 'approve') {
            // Update request status
            $update_sql = "UPDATE firm_association_requests 
                          SET status = 'approved', response_date = NOW() 
                          WHERE id = ? AND firm_id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ii", $request_id, $firm_id);
            
            if ($update_stmt->execute()) {
                // Create association record
                $assoc_sql = "INSERT INTO practitioner_firm_associations 
                             (practitioner_id, firm_id, status, start_date) 
                             VALUES (?, ?, 'active', NOW())";
                $assoc_stmt = $conn->prepare($assoc_sql);
                $assoc_stmt->bind_param("ii", $practitioner_id, $firm_id);
                
                if ($assoc_stmt->execute()) {
                    $association_id = $conn->insert_id;
                    
                    // Log the event
                    log_association_event(
                        $practitioner_id,
                        $firm_id,
                        $association_id,
                        'approve_association',
                        'pending',
                        'active',
                        'Association request approved by firm',
                        'firm',
                        $firm_id,
                        $conn
                    );
                    
                    $success = "You have approved the association request from $practitioner_name.";
                } else {
                    $error = "Error creating association: " . $conn->error;
                }
            } else {
                $error = "Error updating request: " . $conn->error;
            }
        } elseif ($action == 'reject') {
            // Update request status
            $update_sql = "UPDATE firm_association_requests 
                          SET status = 'rejected', response_date = NOW() 
                          WHERE id = ? AND firm_id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ii", $request_id, $firm_id);
            
            if ($update_stmt->execute()) {
                // Log the event
                log_association_event(
                    $practitioner_id,
                    $firm_id,
                    null,
                    'reject_association',
                    'pending',
                    'rejected',
                    'Association request rejected by firm',
                    'firm',
                    $firm_id,
                    $conn
                );
                
                $success = "You have rejected the association request from $practitioner_name.";
            } else {
                $error = "Error updating request: " . $conn->error;
            }
        }
    } else {
        $error = "Request not found.";
    }
}

// Get all association requests for this firm
$requests_sql = "SELECT r.*, p.full_name, p.email, p.phone, p.registration_number, p.specialization
                FROM firm_association_requests r
                JOIN tax_practitioners p ON r.practitioner_id = p.id
                WHERE r.firm_id = ?
                ORDER BY 
                    CASE WHEN r.status = 'pending' THEN 0 ELSE 1 END,
                    r.request_date DESC";
$requests_stmt = $conn->prepare($requests_sql);
$requests_stmt->bind_param("i", $firm_id);
$requests_stmt->execute();
$requests_result = $requests_stmt->get_result();

// Count requests by status
$pending_count = 0;
$approved_count = 0;
$rejected_count = 0;
$requests = [];

while ($row = $requests_result->fetch_assoc()) {
    $requests[] = $row;
    if ($row['status'] == 'pending') {
        $pending_count++;
    } elseif ($row['status'] == 'approved') {
        $approved_count++;
    } elseif ($row['status'] == 'rejected') {
        $rejected_count++;
    }
}

include '../includes/header.php';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-clipboard-list me-2"></i>Manage Association Requests</h2>
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>
    </div>
    
    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h1 class="display-4"><?php echo $pending_count; ?></h1>
                    <p class="mb-0">Pending Requests</p>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Managing Association Requests</h5>
                    <p class="card-text">
                        Review requests from tax practitioners who want to be associated with your firm. 
                        Approving a request will create an official association between your firm and the practitioner.
                    </p>
                    <ul class="mb-0">
                        <li>Verify the practitioner's credentials before approving</li>
                        <li>Check their registration number with the tax authority</li>
                        <li>Ensure you know the practitioner professionally</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="requestTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" 
                            type="button" role="tab" aria-controls="pending" aria-selected="true">
                        Pending <span class="badge bg-primary"><?php echo $pending_count; ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="approved-tab" data-bs-toggle="tab" data-bs-target="#approved" 
                            type="button" role="tab" aria-controls="approved" aria-selected="false">
                        Approved <span class="badge bg-success"><?php echo $approved_count; ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" data-bs-target="#rejected" 
                            type="button" role="tab" aria-controls="rejected" aria-selected="false">
                        Rejected <span class="badge bg-danger"><?php echo $rejected_count; ?></span>
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="requestTabsContent">
                <!-- Pending Requests Tab -->
                <div class="tab-pane fade show active" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                    <?php if ($pending_count > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Practitioner</th>
                                        <th>Registration #</th>
                                        <th>Contact</th>
                                        <th>Request Date</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($requests as $request): ?>
                                        <?php if ($request['status'] == 'pending'): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?php echo htmlspecialchars($request['full_name']); ?></div>
                                                <small class="text-muted"><?php echo htmlspecialchars($request['specialization']); ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($request['registration_number']); ?></td>
                                            <td>
                                                <div><i class="fas fa-envelope me-1 text-muted"></i> <?php echo htmlspecialchars($request['email']); ?></div>
                                                <div><i class="fas fa-phone me-1 text-muted"></i> <?php echo htmlspecialchars($request['phone']); ?></div>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($request['request_date'])); ?></td>
                                            <td>
                                                <?php if (!empty($request['notes'])): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-info view-notes" 
                                                            data-bs-toggle="modal" data-bs-target="#notesModal"
                                                            data-notes="<?php echo htmlspecialchars($request['notes']); ?>"
                                                            data-practitioner="<?php echo htmlspecialchars($request['full_name']); ?>">
                                                        <i class="fas fa-comment-alt me-1"></i>View Message
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-muted">No message</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                                    <button type="submit" name="action" value="approve" class="btn btn-success btn-sm" 
                                                            onclick="return confirm('Are you sure you want to approve this association request?')">
                                                        <i class="fas fa-check me-1"></i>Approve
                                                    </button>
                                                    <button type="submit" name="action" value="reject" class="btn btn-danger btn-sm"
                                                            onclick="return confirm('Are you sure you want to reject this association request?')">
                                                        <i class="fas fa-times me-1"></i>Reject
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No pending association requests at this time.
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Approved Requests Tab -->
                <div class="tab-pane fade" id="approved" role="tabpanel" aria-labelledby="approved-tab">
                    <?php 
                    $approved_count = 0;
                    foreach ($requests as $request) {
                        if ($request['status'] == 'approved') {
                            $approved_count++;
                        }
                    }
                    ?>
                    
                    <?php if ($approved_count > 0): ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead class="table-light">
                                    <tr>
                                        <th>Practitioner</th>
                                        <th>Registration #</th>
                                        <th>Request Date</th>
                                        <th>Approved Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($requests as $request): ?>
                                        <?php if ($request['status'] == 'approved'): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?php echo htmlspecialchars($request['full_name']); ?></div>
                                                <small class="text-muted"><?php echo htmlspecialchars($request['specialization']); ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($request['registration_number']); ?></td>
                                            <td><?php echo date('M d, Y', strtotime($request['request_date'])); ?></td>
                                            <td><?php echo isset($request['response_date']) && $request['response_date'] ? date('M d, Y', strtotime($request['response_date'])) : 'N/A'; ?></td>
                                            <td>
                                                <button type="button" class="btn btn-outline-primary btn-sm view-profile" 
                                                        data-bs-toggle="modal" data-bs-target="#profileModal"
                                                        data-id="<?php echo $request['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($request['full_name']); ?>"
                                                        data-reg="<?php echo htmlspecialchars($request['registration_number']); ?>"
                                                        data-email="<?php echo htmlspecialchars($request['email']); ?>"
                                                        data-phone="<?php echo htmlspecialchars($request['phone']); ?>"
                                                        data-address="<?php echo isset($request['address']) ? htmlspecialchars($request['address']) : ''; ?>"
                                                        data-qualification="<?php echo isset($request['qualification']) ? htmlspecialchars($request['qualification']) : ''; ?>"
                                                        data-specialization="<?php echo htmlspecialchars($request['specialization']); ?>">
                                                    <i class="fas fa-user me-1"></i>View Profile
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                            <h5>No approved requests</h5>
                            <p class="text-muted">You haven't approved any association requests yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Rejected Requests Tab -->
                <div class="tab-pane fade" id="rejected" role="tabpanel" aria-labelledby="rejected-tab">
                    <?php 
                    $rejected_count = 0;
                    foreach ($requests as $request) {
                        if ($request['status'] == 'rejected') {
                            $rejected_count++;
                        }
                    }
                    ?>
                    
                    <?php if ($rejected_count > 0): ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead class="table-light">
                                    <tr>
                                        <th>Practitioner</th>
                                        <th>Registration #</th>
                                        <th>Request Date</th>
                                        <th>Rejected Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($requests as $request): ?>
                                        <?php if ($request['status'] == 'rejected'): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?php echo htmlspecialchars($request['full_name']); ?></div>
                                                <small class="text-muted"><?php echo htmlspecialchars($request['specialization']); ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($request['registration_number']); ?></td>
                                            <td><?php echo date('M d, Y', strtotime($request['request_date'])); ?></td>
                                            <td><?php echo isset($request['response_date']) && $request['response_date'] ? date('M d, Y', strtotime($request['response_date'])) : 'N/A'; ?></td>
                                            <td>
                                                <button type="button" class="btn btn-outline-primary btn-sm view-profile" 
                                                        data-bs-toggle="modal" data-bs-target="#profileModal"
                                                        data-id="<?php echo $request['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($request['full_name']); ?>"
                                                        data-reg="<?php echo htmlspecialchars($request['registration_number']); ?>"
                                                        data-email="<?php echo htmlspecialchars($request['email']); ?>"
                                                        data-phone="<?php echo htmlspecialchars($request['phone']); ?>"
                                                        data-address="<?php echo isset($request['address']) ? htmlspecialchars($request['address']) : ''; ?>"
                                                        data-qualification="<?php echo isset($request['qualification']) ? htmlspecialchars($request['qualification']) : ''; ?>"
                                                        data-specialization="<?php echo htmlspecialchars($request['specialization']); ?>">
                                                    <i class="fas fa-user me-1"></i>View Profile
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                            <h5>No rejected requests</h5>
                            <p class="text-muted">You haven't rejected any association requests.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Practitioner Profile Modal -->
<div class="modal fade" id="profileModal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="profileModalLabel">Practitioner Profile</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <div class="avatar-placeholder bg-light rounded-circle mx-auto mb-3" style="width: 120px; height: 120px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user fa-4x text-secondary"></i>
                        </div>
                        <h5 id="practitioner-name">Practitioner Name</h5>
                        <p class="badge bg-info" id="practitioner-specialization">Specialization</p>
                    </div>
                    <div class="col-md-8">
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Registration Number:</div>
                            <div class="col-md-8 fw-bold" id="practitioner-reg"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Email:</div>
                            <div class="col-md-8" id="practitioner-email"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Phone:</div>
                            <div class="col-md-8" id="practitioner-phone"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Address:</div>
                            <div class="col-md-8" id="practitioner-address"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 text-muted">Qualification:</div>
                            <div class="col-md-8" id="practitioner-qualification"></div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div id="action-buttons" class="text-center">
                    <form method="post" action="">
                        <input type="hidden" id="request_id" name="request_id" value="">
                        <button type="submit" name="action" value="approve" class="btn btn-success">
                            <i class="fas fa-check me-1"></i>Approve Association
                        </button>
                        <button type="submit" name="action" value="reject" class="btn btn-danger">
                            <i class="fas fa-times me-1"></i>Reject Association
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1" aria-labelledby="notesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="notesModalLabel">Message from Practitioner</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="fw-bold mb-1" id="notes-practitioner">Practitioner Name</p>
                <div class="card">
                    <div class="card-body" id="notes-content">
                        Notes content will appear here
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // View profile modal
    const profileButtons = document.querySelectorAll('.view-profile');
    profileButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const reg = this.getAttribute('data-reg');
            const email = this.getAttribute('data-email');
            const phone = this.getAttribute('data-phone');
            const address = this.getAttribute('data-address');
            const qualification = this.getAttribute('data-qualification');
            const specialization = this.getAttribute('data-specialization');
            
            document.getElementById('request_id').value = id;
            document.getElementById('practitioner-name').textContent = name;
            document.getElementById('practitioner-reg').textContent = reg;
            document.getElementById('practitioner-email').textContent = email;
            document.getElementById('practitioner-phone').textContent = phone;
            document.getElementById('practitioner-address').textContent = address;
            document.getElementById('practitioner-qualification').textContent = qualification;
            document.getElementById('practitioner-specialization').textContent = specialization;
            
            // Show/hide action buttons based on tab
            const activeTab = document.querySelector('.nav-link.active').getAttribute('id');
            if (activeTab === 'pending-tab') {
                document.getElementById('action-buttons').style.display = 'block';
            } else {
                document.getElementById('action-buttons').style.display = 'none';
            }
        });
    });
    
    // View notes modal
    const notesButtons = document.querySelectorAll('.view-notes');
    notesButtons.forEach(button => {
        button.addEventListener('click', function() {
            const notes = this.getAttribute('data-notes');
            const practitioner = this.getAttribute('data-practitioner');
            
            document.getElementById('notes-practitioner').textContent = practitioner;
            document.getElementById('notes-content').textContent = notes;
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>












