# MSYSTEM Environment Information
# Copyright (C) 2016 Ren<PERSON>
# Licensed under public domain

# Once sourced, this script provides common information associated with the
# current MSYSTEM. For example, the compiler architecture and host type.

# The MSYSTEM_ prefix is used for avoiding too generic names. For example,
# makepkg is sensitive to the value of CARCH, so MSYSTEM_CARCH is defined
# instead. The MINGW_ prefix does not influence makepkg-mingw variables and
# is not used for the MSYS shell.

export MSYSTEM="${MSYSTEM:-MSYS}"

unset MSYSTEM_PREFIX
unset MSYSTEM_CARCH
unset MSYSTEM_CHOST

unset MINGW_CHOST
unset MINGW_PREFIX
unset MINGW_PACKAGE_PREFIX

if [ -e "/etc/msystem.d/${MSYSTEM}" ]; then
    source "/etc/msystem.d/${MSYSTEM}"
else
    source "/etc/msystem.d/MSYS"
fi
