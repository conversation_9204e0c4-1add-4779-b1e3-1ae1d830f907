<?php
/**
 * Add Azure ID Column to Administrators Table
 * 
 * This script adds the azure_id column to the administrators table
 * and also adds an is_active column if it doesn't exist.
 */

session_start();
require_once '../includes/config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    echo "Access denied. Please log in as an administrator.";
    exit;
}

echo "<h2>Adding Azure AD Support to Administrators Table</h2>";

// Check if azure_id column exists
$column_check = $conn->query("SHOW COLUMNS FROM administrators LIKE 'azure_id'");
if ($column_check->num_rows == 0) {
    echo "<p>Adding azure_id column...</p>";
    $add_azure_id = "ALTER TABLE administrators ADD COLUMN azure_id VARCHAR(255) NULL UNIQUE";
    if ($conn->query($add_azure_id)) {
        echo "<p style='color: green;'>✓ azure_id column added successfully</p>";
    } else {
        echo "<p style='color: red;'>✗ Error adding azure_id column: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>✓ azure_id column already exists</p>";
}

// Check if is_active column exists
$active_check = $conn->query("SHOW COLUMNS FROM administrators LIKE 'is_active'");
if ($active_check->num_rows == 0) {
    echo "<p>Adding is_active column...</p>";
    $add_is_active = "ALTER TABLE administrators ADD COLUMN is_active BOOLEAN DEFAULT TRUE";
    if ($conn->query($add_is_active)) {
        echo "<p style='color: green;'>✓ is_active column added successfully</p>";
    } else {
        echo "<p style='color: red;'>✗ Error adding is_active column: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>✓ is_active column already exists</p>";
}

// Show current table structure
echo "<h3>Current Administrators Table Structure:</h3>";
$structure = $conn->query("DESCRIBE administrators");
if ($structure) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<p><a href='test_azure_integration.php'>Test Azure Integration</a> | <a href='azure_config_setup.php'>Configure Azure AD</a> | <a href='dashboard.php'>Back to Dashboard</a></p>";
?>
