<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Creating Test Documents</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";

// Create upload directory if it doesn't exist
$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
    echo "<p style='color: green;'>✅ Created upload directory: $upload_dir</p>";
}

// Function to create a simple PDF-like content
function createDocumentContent($type, $firm_id) {
    $content = "%PDF-1.4\n";
    $content .= "1 0 obj\n";
    $content .= "<<\n";
    $content .= "/Type /Catalog\n";
    $content .= "/Pages 2 0 R\n";
    $content .= ">>\n";
    $content .= "endobj\n\n";
    
    $content .= "2 0 obj\n";
    $content .= "<<\n";
    $content .= "/Type /Pages\n";
    $content .= "/Kids [3 0 R]\n";
    $content .= "/Count 1\n";
    $content .= ">>\n";
    $content .= "endobj\n\n";
    
    $content .= "3 0 obj\n";
    $content .= "<<\n";
    $content .= "/Type /Page\n";
    $content .= "/Parent 2 0 R\n";
    $content .= "/MediaBox [0 0 612 792]\n";
    $content .= "/Contents 4 0 R\n";
    $content .= ">>\n";
    $content .= "endobj\n\n";
    
    $content .= "4 0 obj\n";
    $content .= "<<\n";
    $content .= "/Length 100\n";
    $content .= ">>\n";
    $content .= "stream\n";
    $content .= "BT\n";
    $content .= "/F1 12 Tf\n";
    $content .= "100 700 Td\n";
    $content .= "(Sample " . strtoupper($type) . " Document for Firm $firm_id) Tj\n";
    $content .= "0 -20 Td\n";
    $content .= "(Generated: " . date('Y-m-d H:i:s') . ") Tj\n";
    $content .= "ET\n";
    $content .= "endstream\n";
    $content .= "endobj\n\n";
    
    $content .= "xref\n";
    $content .= "0 5\n";
    $content .= "0000000000 65535 f \n";
    $content .= "0000000009 65535 n \n";
    $content .= "0000000074 65535 n \n";
    $content .= "0000000120 65535 n \n";
    $content .= "0000000179 65535 n \n";
    $content .= "trailer\n";
    $content .= "<<\n";
    $content .= "/Size 5\n";
    $content .= "/Root 1 0 R\n";
    $content .= ">>\n";
    $content .= "startxref\n";
    $content .= "492\n";
    $content .= "%%EOF\n";
    
    return $content;
}

// Sample documents to create
$sample_documents = [
    [
        'type' => 'memart',
        'name' => 'MEMART_' . $firm_id . '_' . date('Ymd') . '.pdf',
        'title' => 'CTC of Memorandum & Articles of Association'
    ],
    [
        'type' => 'cac_status',
        'name' => 'CAC_Status_' . $firm_id . '_' . date('Ymd') . '.pdf',
        'title' => 'CAC Company Status Report'
    ],
    [
        'type' => 'utility_bill',
        'name' => 'Utility_Bill_' . $firm_id . '_' . date('Ymd') . '.pdf',
        'title' => 'Current Utility Bill'
    ],
    [
        'type' => 'incorporation_cert',
        'name' => 'Incorporation_' . $firm_id . '_' . date('Ymd') . '.pdf',
        'title' => 'CAC Certificate of Incorporation'
    ]
];

echo "<h3>Creating Sample Documents:</h3>";

foreach ($sample_documents as $doc) {
    $file_path = $upload_dir . $doc['name'];
    
    // Check if document already exists in database
    $check_sql = "SELECT id FROM firm_documents WHERE firm_id = ? AND document_type = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("is", $firm_id, $doc['type']);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        echo "<p style='color: blue;'>ℹ️ Document already exists: " . $doc['title'] . "</p>";
        continue;
    }
    
    // Create sample document content
    $content = createDocumentContent($doc['type'], $firm_id);
    
    if (file_put_contents($file_path, $content)) {
        $actual_size = filesize($file_path);
        
        // Insert into database
        $insert_sql = "INSERT INTO firm_documents (firm_id, document_type, file_name, file_path, file_size, status) 
                       VALUES (?, ?, ?, ?, ?, 'uploaded')";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("isssi", $firm_id, $doc['type'], $doc['name'], $file_path, $actual_size);
        
        if ($insert_stmt->execute()) {
            echo "<p style='color: green;'>✅ Created: " . $doc['title'] . " (" . $doc['name'] . ")</p>";
        } else {
            echo "<p style='color: red;'>❌ Database error for " . $doc['title'] . ": " . $insert_stmt->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to create file: " . $doc['name'] . "</p>";
    }
}

// Show current documents
echo "<h3>Current Documents Status:</h3>";
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY document_type";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Document Type</th><th>File Name</th><th>Status</th><th>Size</th><th>File Exists</th><th>Actions</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        $file_status = $file_exists ? '✅ Yes' : '❌ No';
        $file_color = $file_exists ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . number_format($doc['file_size']) . " bytes</td>";
        echo "<td style='color: $file_color;'>$file_status</td>";
        echo "<td>";
        
        if ($file_exists) {
            echo "<a href='view_document.php?type=" . $doc['document_type'] . "' target='_blank' style='margin-right: 5px;'>View</a>";
            echo "<a href='download_document.php?type=" . $doc['document_type'] . "'>Download</a>";
        } else {
            echo "<span style='color: red;'>File Missing</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No documents found.</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
    <h3 style="color: #155724;">Test Documents Created!</h3>
    <p style="color: #155724;">Sample PDF documents have been created for testing view and download functionality.</p>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">View in Dashboard</a>
        <a href="check_documents.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Check Status</a>
    </p>
</div>
