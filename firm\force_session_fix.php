<?php
session_start();
require_once '../includes/config.php';

echo "<h2>Force Session Fix</h2>";

// Show current session before fix
echo "<h3>Before Fix:</h3>";
echo "<p><strong>Current Session Data:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
print_r($_SESSION);
echo "</pre>";

// Get the correct firm from database
$sql = "SELECT * FROM tax_firms ORDER BY id LIMIT 1";
$result = $conn->query($sql);
$firm = $result->fetch_assoc();

if ($firm) {
    echo "<h3>Correct Firm Data from Database:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    foreach ($firm as $key => $value) {
        echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
    }
    echo "</table>";
    
    // Force update session
    echo "<h3>Updating Session...</h3>";
    
    // Clear existing session
    $_SESSION = array();
    
    // Set correct session values
    $_SESSION['firm_id'] = $firm['id'];
    $_SESSION['firm_name'] = $firm['name'];
    $_SESSION['firm_email'] = $firm['email'];
    $_SESSION['firm_registration_number'] = $firm['registration_number'];
    $_SESSION['firm_registration_status'] = $firm['registration_status'];
    $_SESSION['login_time'] = time();
    $_SESSION['last_activity'] = time();
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<p style='color: #155724; margin: 0;'>✅ Session forcefully updated!</p>";
    echo "</div>";
    
    // Show session after fix
    echo "<h3>After Fix:</h3>";
    echo "<p><strong>Updated Session Data:</strong></p>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    print_r($_SESSION);
    echo "</pre>";
    
    // Test the session by making a database query
    echo "<h3>Testing Session:</h3>";
    $test_sql = "SELECT id, name, email FROM tax_firms WHERE id = ?";
    $test_stmt = $conn->prepare($test_sql);
    $test_stmt->bind_param("i", $_SESSION['firm_id']);
    $test_stmt->execute();
    $test_result = $test_stmt->get_result();
    $test_firm = $test_result->fetch_assoc();
    
    if ($test_firm) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
        echo "<p style='color: #155724; margin: 0;'>✅ Session test successful! Found firm: " . htmlspecialchars($test_firm['name']) . "</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo "<p style='color: #721c24; margin: 0;'>❌ Session test failed! Firm ID " . $_SESSION['firm_id'] . " not found.</p>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>❌ No firms found in database!</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px;">
    <h3>Next Steps:</h3>
    <p><strong>The session has been forcefully updated. Now test the dashboard:</strong></p>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="test_profile_data.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Test Profile Data</a>
    </p>
    <p><strong>Instructions:</strong></p>
    <ol>
        <li>Click "Go to Dashboard" above</li>
        <li>Try editing any profile section (Address, Contact, or CEO)</li>
        <li>Save the changes</li>
        <li>Refresh the page to verify data persists</li>
    </ol>
</div>

<script>
// Auto-redirect to dashboard after 3 seconds
setTimeout(function() {
    if (confirm('Session has been fixed! Click OK to go to dashboard now, or Cancel to stay here.')) {
        window.location.href = 'dashboard.php';
    }
}, 2000);
</script>
