<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/auth_check.php';
require_once '../includes/enhanced_certificate_generator.php';

// Check admin authentication
require_admin_auth();

// Check permissions
if (!has_permission('manage_registrations')) {
    $_SESSION['alert'] = [
        'type' => 'danger',
        'message' => 'You do not have permission to manage registrations.'
    ];
    header('Location: dashboard.php');
    exit;
}

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_ids = $_POST['selected_registrations'] ?? [];
    
    if (!empty($selected_ids) && in_array($action, ['approve', 'reject'])) {
        $success_count = 0;
        $error_count = 0;
        
        foreach ($selected_ids as $id) {
            $new_status = ($action === 'approve') ? 'Active' : 'Rejected';
            $update_sql = "UPDATE tax_practitioners SET registration_status = ? WHERE id = ? AND registration_status = 'Pending'";
            $stmt = $conn->prepare($update_sql);

            if ($stmt && $stmt->bind_param("si", $new_status, $id) && $stmt->execute()) {
                $success_count++;

                // If approving, generate certificate
                if ($action === 'approve') {
                    $certificate = generatePractitionerCertificate($id);
                    if ($certificate) {
                        // Send certificate notification email
                        sendCertificateNotification($certificate);
                    }
                }

                // Log the action
                log_admin_action($_SESSION['admin_id'], 'bulk_' . $action . '_registration', "Bulk {$action}ed registration ID: {$id}");
            } else {
                $error_count++;
            }
        }
        
        $message = "Bulk action completed: {$success_count} registrations {$action}ed";
        if ($error_count > 0) {
            $message .= ", {$error_count} failed";
        }
        
        $_SESSION['alert'] = [
            'type' => $error_count > 0 ? 'warning' : 'success',
            'message' => $message
        ];
    }
    
    header('Location: pending_registrations.php');
    exit;
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$citn_filter = $_GET['citn_filter'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$per_page = 20;
$page = max(1, intval($_GET['page'] ?? 1));
$offset = ($page - 1) * $per_page;

// Build query
$status_filter = $_GET['status'] ?? 'Pending';
$where_conditions = [];
$params = [];
$param_types = "";

// Filter by status
if ($status_filter !== 'all') {
    $where_conditions[] = "registration_status = ?";
    $params[] = $status_filter;
    $param_types .= "s";
} else {
    $where_conditions[] = "1=1"; // Show all if 'all' is selected
}

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR registration_number LIKE ?)";
    $search_param = "%{$search}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $param_types .= "sss";
}

if (!empty($citn_filter)) {
    if ($citn_filter === 'with_citn') {
        $where_conditions[] = "citn_number IS NOT NULL AND citn_number != ''";
    } elseif ($citn_filter === 'without_citn') {
        $where_conditions[] = "(citn_number IS NULL OR citn_number = '')";
    } elseif ($citn_filter === 'verified') {
        $where_conditions[] = "citn_verification_status = 'verified'";
    } elseif ($citn_filter === 'pending_verification') {
        $where_conditions[] = "citn_verification_status = 'pending'";
    }
}

if (!empty($date_from)) {
    $where_conditions[] = "registration_date >= ?";
    $params[] = $date_from;
    $param_types .= "s";
}

if (!empty($date_to)) {
    $where_conditions[] = "registration_date <= ?";
    $params[] = $date_to . ' 23:59:59';
    $param_types .= "s";
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$count_sql = "SELECT COUNT(*) as total FROM tax_practitioners WHERE {$where_clause}";
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// Get pending registrations
$sql = "SELECT id, full_name, email, phone, registration_number, registration_date, 
               citn_number, citn_verification_status, employment_status, 
               company_name, business_name, state_of_origin
        FROM tax_practitioners 
        WHERE {$where_clause}
        ORDER BY registration_date DESC 
        LIMIT ? OFFSET ?";

$stmt = $conn->prepare($sql);
$params[] = $per_page;
$params[] = $offset;
$param_types .= "ii";
$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

$page_title = "Manage Registrations";
include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Manage Registrations</h1>
                    <p class="text-muted">Review and manage practitioner registrations</p>
                </div>
                <div>
                    <span class="badge bg-info fs-6"><?php echo $total_records; ?>
                        <?php echo $status_filter === 'all' ? 'Total' : ucfirst($status_filter); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (isset($_SESSION['alert'])): ?>
    <div class="alert alert-<?php echo $_SESSION['alert']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['alert']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['alert']); endif; ?>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Name, email, or reg number" value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Registration Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="Pending" <?php echo $status_filter === 'Pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="Active" <?php echo $status_filter === 'Active' ? 'selected' : ''; ?>>Active</option>
                        <option value="Rejected" <?php echo $status_filter === 'Rejected' ? 'selected' : ''; ?>>Rejected</option>
                        <option value="CITN_Verification_Required" <?php echo $status_filter === 'CITN_Verification_Required' ? 'selected' : ''; ?>>CITN Required</option>
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Statuses</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="citn_filter" class="form-label">CITN Status</label>
                    <select class="form-select" id="citn_filter" name="citn_filter">
                        <option value="">All</option>
                        <option value="with_citn" <?php echo $citn_filter === 'with_citn' ? 'selected' : ''; ?>>With CITN</option>
                        <option value="without_citn" <?php echo $citn_filter === 'without_citn' ? 'selected' : ''; ?>>Without CITN</option>
                        <option value="verified" <?php echo $citn_filter === 'verified' ? 'selected' : ''; ?>>CITN Verified</option>
                        <option value="pending_verification" <?php echo $citn_filter === 'pending_verification' ? 'selected' : ''; ?>>CITN Pending</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="pending_registrations.php" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i>Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <form method="POST" id="bulkForm">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Pending Registrations (<?php echo $total_records; ?>)</h5>
                <div class="d-flex gap-2">
                    <select name="bulk_action" class="form-select form-select-sm" style="width: auto;">
                        <option value="">Bulk Actions</option>
                        <option value="approve">Approve Selected</option>
                        <option value="reject">Reject Selected</option>
                    </select>
                    <button type="submit" class="btn btn-sm btn-primary" onclick="return confirmBulkAction()">
                        <i class="fas fa-check me-1"></i>Apply
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if ($result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Practitioner Details</th>
                                <th>Contact Info</th>
                                <th>CITN Status</th>
                                <th>Employment</th>
                                <th>Registration Date</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" name="selected_registrations[]" 
                                           value="<?php echo $row['id']; ?>" class="form-check-input registration-checkbox">
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($row['full_name']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            Reg #: <?php echo htmlspecialchars($row['registration_number']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($row['email']); ?>
                                        <br>
                                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($row['phone'] ?? 'N/A'); ?>
                                        <br>
                                        <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($row['state_of_origin'] ?? 'N/A'); ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($row['citn_number'])): ?>
                                        <div class="mb-1">
                                            <small class="text-muted">CITN:</small> <?php echo htmlspecialchars($row['citn_number']); ?>
                                        </div>
                                        <?php
                                        $status_class = 'secondary';
                                        $status_text = 'Not Verified';
                                        switch ($row['citn_verification_status']) {
                                            case 'verified':
                                                $status_class = 'success';
                                                $status_text = 'Verified';
                                                break;
                                            case 'pending':
                                                $status_class = 'warning';
                                                $status_text = 'Pending';
                                                break;
                                            case 'invalid':
                                                $status_class = 'danger';
                                                $status_text = 'Invalid';
                                                break;
                                        }
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">No CITN</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <span class="badge bg-info"><?php echo ucfirst($row['employment_status'] ?? 'N/A'); ?></span>
                                        <?php if (!empty($row['company_name'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($row['company_name']); ?></small>
                                        <?php endif; ?>
                                        <?php if (!empty($row['business_name'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($row['business_name']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <small><?php echo date('M d, Y', strtotime($row['registration_date'])); ?></small>
                                    <br>
                                    <small class="text-muted"><?php echo date('H:i', strtotime($row['registration_date'])); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                        <a href="review_application.php?id=<?php echo $row['id']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i> Review
                                        </a>
                                        <a href="approve_registration.php?id=<?php echo $row['id']; ?>&action=approve" 
                                           class="btn btn-success btn-sm" 
                                           onclick="return confirm('Approve this registration?')">
                                            <i class="fas fa-check"></i> Approve
                                        </a>
                                        <a href="approve_registration.php?id=<?php echo $row['id']; ?>&action=reject" 
                                           class="btn btn-danger btn-sm" 
                                           onclick="return confirm('Reject this registration?')">
                                            <i class="fas fa-times"></i> Reject
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Pending Registrations</h5>
                    <p class="text-muted">All registrations have been processed or no registrations match your filters.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </form>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <nav aria-label="Pagination">
        <ul class="pagination justify-content-center">
            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Previous</a>
            </li>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
            </li>
            <?php endfor; ?>
            
            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Next</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.registration-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Confirm bulk action
function confirmBulkAction() {
    const selected = document.querySelectorAll('.registration-checkbox:checked');
    const action = document.querySelector('select[name="bulk_action"]').value;
    
    if (selected.length === 0) {
        alert('Please select at least one registration.');
        return false;
    }
    
    if (!action) {
        alert('Please select an action.');
        return false;
    }
    
    return confirm(`Are you sure you want to ${action} ${selected.length} registration(s)?`);
}

// Auto-hide alerts
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('show')) {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }
    });
}, 5000);
</script>

<?php include '../includes/admin_footer.php'; ?>
