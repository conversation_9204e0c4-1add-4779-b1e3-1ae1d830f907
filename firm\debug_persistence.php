<?php
session_start();
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['firm_id'])) {
    echo "<p style='color: red;'>Please login first.</p>";
    exit();
}

$firm_id = $_SESSION['firm_id'];

echo "<h2>Document Persistence Debug</h2>";
echo "<p><strong>Firm ID:</strong> $firm_id</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Check database connection
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
}

// Check if firm_documents table exists
$table_check = $conn->query("SHOW TABLES LIKE 'firm_documents'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<p style='color: green;'>✅ firm_documents table exists</p>";
} else {
    echo "<p style='color: red;'>❌ firm_documents table does not exist</p>";
    exit();
}

// Get all documents for this firm
echo "<h3>1. Raw Database Query Results</h3>";
$docs_sql = "SELECT * FROM firm_documents WHERE firm_id = ? ORDER BY upload_date DESC";
$docs_stmt = $conn->prepare($docs_sql);
$docs_stmt->bind_param("i", $firm_id);
$docs_stmt->execute();
$docs_result = $docs_stmt->get_result();

if ($docs_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Document Type</th><th>File Name</th><th>File Path</th><th>Status</th><th>Upload Date</th><th>File Exists</th></tr>";
    
    while ($doc = $docs_result->fetch_assoc()) {
        $file_exists = file_exists($doc['file_path']);
        $file_color = $file_exists ? 'green' : 'red';
        $file_status = $file_exists ? '✅ Yes' : '❌ No';
        
        echo "<tr>";
        echo "<td>" . $doc['id'] . "</td>";
        echo "<td>" . htmlspecialchars($doc['document_type']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_path']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td style='color: $file_color;'>$file_status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ No documents found in database</p>";
}

// Test the dashboard query specifically
echo "<h3>2. Dashboard Query Test</h3>";
$dashboard_sql = "SELECT document_type, file_name, file_path, status, upload_date FROM firm_documents WHERE firm_id = ?";
$dashboard_stmt = $conn->prepare($dashboard_sql);
$dashboard_stmt->bind_param("i", $firm_id);
$dashboard_stmt->execute();
$dashboard_result = $dashboard_stmt->get_result();

$document_statuses = [];
while ($doc = $dashboard_result->fetch_assoc()) {
    $document_statuses[$doc['document_type']] = $doc;
}

echo "<p><strong>Documents found by dashboard query:</strong> " . count($document_statuses) . "</p>";

if (!empty($document_statuses)) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Document Type</th><th>File Name</th><th>Status</th><th>Upload Date</th><th>File Path</th></tr>";
    
    foreach ($document_statuses as $type => $doc) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($type) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_name']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['status']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['upload_date']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['file_path']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test the getDocumentStatus function
echo "<h3>3. Document Status Function Test</h3>";

function testGetDocumentStatus($type, $statuses) {
    if (isset($statuses[$type])) {
        $doc = $statuses[$type];
        // Always show uploaded documents as having files (since we removed view/download)
        $has_file = ($doc['status'] === 'uploaded');
        return [
            'status' => $doc['status'],
            'file_name' => $doc['file_name'],
            'upload_date' => $doc['upload_date'] ? date('M j, Y', strtotime($doc['upload_date'])) : null,
            'has_file' => $has_file,
            'file_exists' => file_exists($doc['file_path'] ?? ''),
            'file_path' => $doc['file_path'] ?? null
        ];
    }
    return [
        'status' => 'pending',
        'file_name' => null,
        'upload_date' => null,
        'has_file' => false,
        'file_exists' => false,
        'file_path' => null
    ];
}

$test_types = ['memart', 'cac_status', 'tax_clearance', 'utility_bill', 'incorporation_cert'];

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Document Type</th><th>Status</th><th>Has File</th><th>File Exists</th><th>Upload Date</th><th>Expected Display</th></tr>";

$uploaded_count = 0;
$pending_count = 0;

foreach ($test_types as $type) {
    $status = testGetDocumentStatus($type, $document_statuses);
    
    if ($status['has_file']) {
        $uploaded_count++;
        $expected_display = "✅ Uploaded with Delete button";
    } else {
        $pending_count++;
        $expected_display = "⏳ Pending with Upload button";
    }
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($type) . "</td>";
    echo "<td>" . htmlspecialchars($status['status']) . "</td>";
    echo "<td style='color: " . ($status['has_file'] ? 'green' : 'red') . ";'>" . ($status['has_file'] ? 'Yes' : 'No') . "</td>";
    echo "<td style='color: " . ($status['file_exists'] ? 'green' : 'red') . ";'>" . ($status['file_exists'] ? 'Yes' : 'No') . "</td>";
    echo "<td>" . htmlspecialchars($status['upload_date'] ?? 'N/A') . "</td>";
    echo "<td>" . $expected_display . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Expected Dashboard Counters:</h4>";
echo "<p><strong>Uploaded:</strong> $uploaded_count</p>";
echo "<p><strong>Pending:</strong> $pending_count</p>";
echo "<p><strong>Total:</strong> " . count($test_types) . "</p>";
echo "</div>";

// Check upload directory
echo "<h3>4. File System Check</h3>";
$upload_dir = '../uploads/documents/firm_' . $firm_id . '/';

if (file_exists($upload_dir)) {
    echo "<p style='color: green;'>✅ Upload directory exists: $upload_dir</p>";
    
    $files = scandir($upload_dir);
    $files = array_diff($files, array('.', '..'));
    
    if (count($files) > 0) {
        echo "<h4>Files in directory:</h4>";
        echo "<ul>";
        foreach ($files as $file) {
            $file_path = $upload_dir . $file;
            $file_size = filesize($file_path);
            $file_time = date('Y-m-d H:i:s', filemtime($file_path));
            echo "<li><strong>$file</strong> (" . number_format($file_size) . " bytes, modified: $file_time)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Directory is empty</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Upload directory does not exist</p>";
}

$conn->close();
?>

<div style="margin: 20px 0; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px;">
    <h3 style="color: #0c5460;">🔍 Persistence Check Complete</h3>
    <p style="color: #0c5460;">This debug shows the current state of your documents. If files disappear after refresh, check:</p>
    <ul style="color: #0c5460;">
        <li>Are database records being created with status = 'uploaded'?</li>
        <li>Are files being saved to the correct directory?</li>
        <li>Is the dashboard query finding the uploaded documents?</li>
        <li>Are the document status functions working correctly?</li>
    </ul>
    <p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
        <a href="test_upload.html" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">Test Upload</a>
    </p>
</div>
