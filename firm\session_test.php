<?php
session_start();
require_once '../includes/config.php';

header('Content-Type: application/json');

// Simple test to check if session is working
$response = [
    'session_id' => session_id(),
    'session_data' => $_SESSION,
    'firm_id_in_session' => $_SESSION['firm_id'] ?? 'NOT SET',
    'timestamp' => date('Y-m-d H:i:s')
];

// Test database connection
if (isset($_SESSION['firm_id'])) {
    $firm_id = $_SESSION['firm_id'];
    
    $sql = "SELECT id, name, email FROM tax_firms WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $firm_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $firm = $result->fetch_assoc();
    
    if ($firm) {
        $response['database_test'] = 'SUCCESS';
        $response['firm_found'] = $firm;
    } else {
        $response['database_test'] = 'FAILED';
        $response['error'] = 'Firm not found in database';
        
        // Get available firms
        $all_sql = "SELECT id, name, email FROM tax_firms";
        $all_result = $conn->query($all_sql);
        $available_firms = [];
        while ($row = $all_result->fetch_assoc()) {
            $available_firms[] = $row;
        }
        $response['available_firms'] = $available_firms;
    }
} else {
    $response['database_test'] = 'NO_SESSION';
    $response['error'] = 'No firm_id in session';
}

echo json_encode($response, JSON_PRETTY_PRINT);
$conn->close();
?>
