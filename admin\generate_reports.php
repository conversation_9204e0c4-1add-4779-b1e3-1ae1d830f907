<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/auth_check.php';

// Check admin authentication
require_admin_auth();

// Check permissions
if (!has_permission('generate_reports')) {
    $_SESSION['alert'] = [
        'type' => 'danger',
        'message' => 'You do not have permission to generate reports.'
    ];
    header('Location: dashboard.php');
    exit;
}

// Handle report generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_report'])) {
    $report_type = $_POST['report_type'];
    $format = $_POST['format'];
    $date_from = $_POST['date_from'];
    $date_to = $_POST['date_to'];
    $status_filter = $_POST['status_filter'] ?? '';
    $citn_filter = $_POST['citn_filter'] ?? '';
    $include_details = isset($_POST['include_details']);
    
    // Validate inputs
    if (empty($report_type) || empty($format) || empty($date_from) || empty($date_to)) {
        $_SESSION['alert'] = [
            'type' => 'danger',
            'message' => 'Please fill in all required fields.'
        ];
    } else {
        // Generate the report
        $report_data = generateReport($conn, $report_type, $date_from, $date_to, $status_filter, $citn_filter, $include_details);
        
        if ($report_data !== false) {
            // Log the report generation
            log_admin_action($_SESSION['admin_id'], 'generate_report', "Generated {$report_type} report ({$format}) from {$date_from} to {$date_to}");
            
            // Export the report
            exportReport($report_data, $report_type, $format, $date_from, $date_to);
            exit;
        } else {
            $_SESSION['alert'] = [
                'type' => 'danger',
                'message' => 'Failed to generate report. Please try again.'
            ];
        }
    }
}

// Get report statistics for dashboard
$stats = getReportStatistics($conn);

$page_title = "Generate Reports";
include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Generate Reports</h1>
                    <p class="text-muted">Create comprehensive reports for analysis and compliance</p>
                </div>
                <div>
                    <a href="reports_dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar me-1"></i>Reports Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (isset($_SESSION['alert'])): ?>
    <div class="alert alert-<?php echo $_SESSION['alert']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['alert']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['alert']); endif; ?>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4><?php echo $stats['total_practitioners']; ?></h4>
                    <p class="mb-0">Total Practitioners</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h4><?php echo $stats['active_practitioners']; ?></h4>
                    <p class="mb-0">Active Practitioners</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4><?php echo $stats['pending_practitioners']; ?></h4>
                    <p class="mb-0">Pending Applications</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-certificate fa-2x mb-2"></i>
                    <h4><?php echo $stats['citn_verified']; ?></h4>
                    <p class="mb-0">CITN Verified</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Generation Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-file-export me-2"></i>Generate Report</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="report_type" class="form-label">Report Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="report_type" name="report_type" required>
                                    <option value="">Select Report Type</option>
                                    <option value="practitioners_summary">Practitioners Summary</option>
                                    <option value="practitioners_detailed">Practitioners Detailed</option>
                                    <option value="registration_status">Registration Status Report</option>
                                    <option value="citn_verification">CITN Verification Report</option>
                                    <option value="employment_analysis">Employment Analysis</option>
                                    <option value="geographical_distribution">Geographical Distribution</option>
                                    <option value="monthly_registrations">Monthly Registrations</option>
                                    <option value="audit_trail">Audit Trail Report</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="format" class="form-label">Export Format <span class="text-danger">*</span></label>
                                <select class="form-select" id="format" name="format" required>
                                    <option value="">Select Format</option>
                                    <option value="csv">CSV (Comma Separated)</option>
                                    <option value="excel">Excel (.xlsx)</option>
                                    <option value="pdf">PDF Document</option>
                                    <option value="json">JSON Data</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="date_from" class="form-label">From Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo date('Y-m-01'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="date_to" class="form-label">To Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="status_filter" class="form-label">Registration Status Filter</label>
                                <select class="form-select" id="status_filter" name="status_filter">
                                    <option value="">All Statuses</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Active">Active</option>
                                    <option value="Suspended">Suspended</option>
                                    <option value="Rejected">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="citn_filter" class="form-label">CITN Status Filter</label>
                                <select class="form-select" id="citn_filter" name="citn_filter">
                                    <option value="">All CITN Statuses</option>
                                    <option value="verified">CITN Verified</option>
                                    <option value="pending">CITN Pending</option>
                                    <option value="invalid">CITN Invalid</option>
                                    <option value="not_provided">No CITN Provided</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_details" name="include_details" checked>
                                    <label class="form-check-label" for="include_details">
                                        Include detailed information (contact details, employment info, etc.)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" name="generate_report" class="btn btn-primary btn-lg">
                                <i class="fas fa-download me-2"></i>Generate & Download Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Report Types Info -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Report Types</h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="reportTypesAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#practitioners">
                                    Practitioners Reports
                                </button>
                            </h2>
                            <div id="practitioners" class="accordion-collapse collapse" data-bs-parent="#reportTypesAccordion">
                                <div class="accordion-body">
                                    <small>
                                        <strong>Summary:</strong> Basic practitioner information<br>
                                        <strong>Detailed:</strong> Complete practitioner profiles with all data
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#status">
                                    Status Reports
                                </button>
                            </h2>
                            <div id="status" class="accordion-collapse collapse" data-bs-parent="#reportTypesAccordion">
                                <div class="accordion-body">
                                    <small>
                                        <strong>Registration Status:</strong> Breakdown by approval status<br>
                                        <strong>CITN Verification:</strong> CITN verification statistics
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#analysis">
                                    Analysis Reports
                                </button>
                            </h2>
                            <div id="analysis" class="accordion-collapse collapse" data-bs-parent="#reportTypesAccordion">
                                <div class="accordion-body">
                                    <small>
                                        <strong>Employment:</strong> Employment status and company analysis<br>
                                        <strong>Geographical:</strong> Distribution by states and LGAs<br>
                                        <strong>Monthly:</strong> Registration trends over time
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Reports -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>Recent Reports</h6>
                </div>
                <div class="card-body">
                    <?php
                    // Get recent report generation activities
                    $recent_reports_sql = "SELECT action, details, created_at 
                                          FROM admin_activity_log 
                                          WHERE action = 'generate_report' 
                                          ORDER BY created_at DESC 
                                          LIMIT 5";
                    $recent_reports_result = $conn->query($recent_reports_sql);
                    
                    if ($recent_reports_result && $recent_reports_result->num_rows > 0):
                    ?>
                    <div class="list-group list-group-flush">
                        <?php while ($report = $recent_reports_result->fetch_assoc()): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <small class="text-muted"><?php echo htmlspecialchars($report['details']); ?></small>
                                </div>
                                <small class="text-muted"><?php echo date('M d, H:i', strtotime($report['created_at'])); ?></small>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <p class="text-muted mb-0">No recent reports generated.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-hide alerts
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('show')) {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }
    });
}, 5000);

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const reportType = document.getElementById('report_type').value;
    const format = document.getElementById('format').value;
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    
    if (!reportType || !format || !dateFrom || !dateTo) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    if (new Date(dateFrom) > new Date(dateTo)) {
        e.preventDefault();
        alert('From date cannot be later than To date.');
        return false;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Report...';
    submitBtn.disabled = true;
});
</script>

<?php
// Helper functions for report generation
function getReportStatistics($conn) {
    $stats = [
        'total_practitioners' => 0,
        'active_practitioners' => 0,
        'pending_practitioners' => 0,
        'citn_verified' => 0
    ];
    
    // Check if table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'tax_practitioners'");
    if ($table_check->num_rows == 0) {
        return $stats;
    }
    
    // Get statistics
    $total_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners");
    if ($total_result) {
        $stats['total_practitioners'] = $total_result->fetch_assoc()['count'];
    }
    
    $active_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners WHERE registration_status = 'Active'");
    if ($active_result) {
        $stats['active_practitioners'] = $active_result->fetch_assoc()['count'];
    }
    
    $pending_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners WHERE registration_status = 'Pending'");
    if ($pending_result) {
        $stats['pending_practitioners'] = $pending_result->fetch_assoc()['count'];
    }
    
    $citn_result = $conn->query("SELECT COUNT(*) as count FROM tax_practitioners WHERE citn_verification_status = 'verified'");
    if ($citn_result) {
        $stats['citn_verified'] = $citn_result->fetch_assoc()['count'];
    }
    
    return $stats;
}

function generateReport($conn, $report_type, $date_from, $date_to, $status_filter, $citn_filter, $include_details) {
    // This function would contain the logic to generate different types of reports
    // For now, return a simple dataset
    
    $where_conditions = ["registration_date BETWEEN ? AND ?"];
    $params = [$date_from, $date_to . ' 23:59:59'];
    $param_types = "ss";
    
    if (!empty($status_filter)) {
        $where_conditions[] = "registration_status = ?";
        $params[] = $status_filter;
        $param_types .= "s";
    }
    
    if (!empty($citn_filter)) {
        if ($citn_filter === 'not_provided') {
            $where_conditions[] = "(citn_number IS NULL OR citn_number = '')";
        } else {
            $where_conditions[] = "citn_verification_status = ?";
            $params[] = $citn_filter;
            $param_types .= "s";
        }
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $fields = "id, full_name, email, registration_number, registration_status, registration_date";
    if ($include_details) {
        $fields .= ", phone, address, employment_status, company_name, business_name, citn_number, citn_verification_status";
    }
    
    $sql = "SELECT {$fields} FROM tax_practitioners WHERE {$where_clause} ORDER BY registration_date DESC";
    $stmt = $conn->prepare($sql);
    
    if ($stmt === false) {
        return false;
    }
    
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    return $data;
}

function exportReport($data, $report_type, $format, $date_from, $date_to) {
    $filename = $report_type . '_' . $date_from . '_to_' . $date_to;
    
    switch ($format) {
        case 'csv':
            exportCSV($data, $filename);
            break;
        case 'excel':
            exportExcel($data, $filename);
            break;
        case 'pdf':
            exportPDF($data, $filename, $report_type);
            break;
        case 'json':
            exportJSON($data, $filename);
            break;
    }
}

function exportCSV($data, $filename) {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    if (!empty($data)) {
        // Write headers
        fputcsv($output, array_keys($data[0]));
        
        // Write data
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
}

function exportJSON($data, $filename) {
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $filename . '.json"');
    
    echo json_encode($data, JSON_PRETTY_PRINT);
}

function exportExcel($data, $filename) {
    // For now, export as CSV with Excel-friendly format
    exportCSV($data, $filename);
}

function exportPDF($data, $filename, $report_type) {
    // For now, export as CSV
    exportCSV($data, $filename);
}

include '../includes/admin_footer.php';
?>
