<?php
require_once '../includes/config.php';

// Check if administrators table exists
$table_check = $conn->query("SHOW TABLES LIKE 'administrators'");
$table_exists = $table_check->num_rows > 0;

echo "<h1>Admin System Check</h1>";

if (!$table_exists) {
    echo "<p>The administrators table does not exist. Creating it now...</p>";
    
    // Create administrators table
    $create_table_sql = "CREATE TABLE IF NOT EXISTS `administrators` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `full_name` varchar(100) NOT NULL,
      `email` varchar(100) NOT NULL,
      `password` varchar(255) NOT NULL,
      `role` enum('admin','super_admin','readonly') NOT NULL DEFAULT 'admin',
      `last_login` datetime DEFAULT NULL,
      `created_by` int(11) DEFAULT NULL,
      `created_at` datetime NOT NULL,
      `updated_at` datetime DEFAULT NULL,
      PRIMARY KEY (`id`),
      UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    if ($conn->query($create_table_sql)) {
        echo "<p>Administrators table created successfully.</p>";
        
        // Create initial admin user
        $admin_name = "System Administrator";
        $admin_email = "<EMAIL>";
        $admin_password = password_hash("admin123", PASSWORD_DEFAULT);
        
        $insert_sql = "INSERT INTO administrators (full_name, email, password, role, created_at) 
                      VALUES (?, ?, ?, 'super_admin', NOW())";
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("sss", $admin_name, $admin_email, $admin_password);
        
        if ($stmt->execute()) {
            echo "<p>Initial admin user created:</p>";
            echo "<ul>";
            echo "<li>Email: <EMAIL></li>";
            echo "<li>Password: admin123</li>";
            echo "</ul>";
            echo "<p><strong>Please change this password immediately after logging in!</strong></p>";
        } else {
            echo "<p>Error creating initial admin user: " . $stmt->error . "</p>";
        }
    } else {
        echo "<p>Error creating administrators table: " . $conn->error . "</p>";
    }
} else {
    echo "<p>The administrators table exists.</p>";
    
    // Check if there are any admin users
    $admin_check = $conn->query("SELECT COUNT(*) as count FROM administrators");
    $admin_count = $admin_check->fetch_assoc()['count'];
    
    echo "<p>There are currently " . $admin_count . " administrator(s) in the system.</p>";
}

// Check if admin_activity_log table exists
$log_table_check = $conn->query("SHOW TABLES LIKE 'admin_activity_log'");
$log_table_exists = $log_table_check->num_rows > 0;

if (!$log_table_exists) {
    echo "<p>The admin_activity_log table does not exist. Creating it now...</p>";
    
    // Create admin activity log table
    $create_log_table = "CREATE TABLE IF NOT EXISTS `admin_activity_log` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `admin_id` int(11) NOT NULL,
      `action` varchar(50) NOT NULL,
      `details` text DEFAULT NULL,
      `ip_address` varchar(45) DEFAULT NULL,
      `created_at` datetime NOT NULL,
      PRIMARY KEY (`id`),
      KEY `admin_id` (`admin_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    if ($conn->query($create_log_table)) {
        echo "<p>Admin activity log table created successfully.</p>";
    } else {
        echo "<p>Error creating admin activity log table: " . $conn->error . "</p>";
    }
} else {
    echo "<p>The admin_activity_log table exists.</p>";
}

echo "<p><a href='dashboard.php'>Return to Dashboard</a></p>";
?>